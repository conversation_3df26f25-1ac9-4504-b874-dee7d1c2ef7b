2025-06-04 16:33:42.115 +05:30 [INF] Starting Customer Management API
2025-06-04 16:33:44.475 +05:30 [ERR] An error occurred while initializing the database
Npgsql.PostgresException (0x80004005): 28000: role "timescale" does not exist
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|213_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.UnpooledDataSource.Get(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlDatabaseCreator.Exists(Boolean async, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlDatabaseCreator.Exists(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at CustomerManagement.Infrastructure.Data.DatabaseExtensions.InitializeDatabaseAsync(IHost host) in D:\Projects\TRITRACKZ\TriTrackzMicroservices\CustomerManagement\CustomerManagement.Infrastructure\Data\DatabaseExtensions.cs:line 22
  Exception data:
    Severity: FATAL
    SqlState: 28000
    MessageText: role "timescale" does not exist
    File: miscinit.c
    Line: 756
    Routine: InitializeSessionUserId
2025-06-04 16:33:44.841 +05:30 [FTL] Customer Management API terminated unexpectedly
Npgsql.PostgresException (0x80004005): 28000: role "timescale" does not exist
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.Internal.NpgsqlConnector.<Open>g__OpenCore|213_1(NpgsqlConnector conn, SslMode sslMode, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken, Boolean isFirstAttempt)
   at Npgsql.Internal.NpgsqlConnector.Open(NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.UnpooledDataSource.Get(NpgsqlConnection conn, NpgsqlTimeout timeout, Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.<Open>g__OpenAsync|42_0(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternalAsync(Boolean errorsExpected, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenAsync(CancellationToken cancellationToken, Boolean errorsExpected)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlDatabaseCreator.Exists(Boolean async, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlDatabaseCreator.Exists(Boolean async, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabaseCreator.EnsureCreatedAsync(CancellationToken cancellationToken)
   at CustomerManagement.Infrastructure.Data.DatabaseExtensions.InitializeDatabaseAsync(IHost host) in D:\Projects\TRITRACKZ\TriTrackzMicroservices\CustomerManagement\CustomerManagement.Infrastructure\Data\DatabaseExtensions.cs:line 22
   at Program.<Main>$(String[] args) in D:\Projects\TRITRACKZ\TriTrackzMicroservices\CustomerManagement\CustomerManagement.API\Program.cs:line 153
  Exception data:
    Severity: FATAL
    SqlState: 28000
    MessageText: role "timescale" does not exist
    File: miscinit.c
    Line: 756
    Routine: InitializeSessionUserId
2025-06-04 17:06:29.840 +05:30 [INF] Executed DbCommand (234ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:06:31.197 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:06:32.635 +05:30 [INF] Executed DbCommand (186ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
2025-06-04 17:06:33.902 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:06:34.068 +05:30 [INF] Executed DbCommand (160ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-04 17:06:34.086 +05:30 [INF] Applying migration '20250527065642_InitialCreate'.
2025-06-04 17:06:34.373 +05:30 [INF] Executed DbCommand (185ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'customer_management') THEN
        CREATE SCHEMA customer_management;
    END IF;
END $EF$;
2025-06-04 17:06:34.550 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."Customers" (
    "Id" uuid NOT NULL,
    "CustomerNumber" character varying(50) NOT NULL,
    "Name" character varying(200) NOT NULL,
    "LegalName" character varying(200),
    "Type" integer NOT NULL,
    "Status" integer NOT NULL,
    "Tier" integer NOT NULL,
    "TaxId" character varying(50),
    "RegistrationNumber" character varying(50),
    "Industry" character varying(100),
    "BusinessType" character varying(100),
    "EstablishedDate" timestamp with time zone,
    "Website" character varying(500),
    "Description" character varying(1000),
    "Notes" character varying(2000),
    "PreferredLanguage" character varying(10),
    "PreferredCurrency" character varying(3),
    "TimeZone" character varying(50),
    "IsVip" boolean NOT NULL,
    "AnnualRevenue" numeric(18,2),
    "EmployeeCount" integer,
    "ParentCustomerId" uuid,
    "ProfileCompleteness" integer NOT NULL,
    "LastActivityDate" timestamp with time zone,
    "CreditLimit" numeric(18,2),
    "PaymentTermsDays" integer,
    "InterestRate" numeric(5,2),
    "CreditCurrency" character varying(3),
    "CreditRequiresApproval" boolean,
    "CreditLastReviewDate" timestamp with time zone,
    "CreditNextReviewDate" timestamp with time zone,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    "OrganizationId" uuid NOT NULL,
    "Version" character varying(10) NOT NULL,
    CONSTRAINT "PK_Customers" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_Customers_Customers_ParentCustomerId" FOREIGN KEY ("ParentCustomerId") REFERENCES customer_management."Customers" ("Id") ON DELETE RESTRICT
);
2025-06-04 17:06:34.726 +05:30 [INF] Executed DbCommand (164ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."Contacts" (
    "Id" uuid NOT NULL,
    "CustomerId" uuid NOT NULL,
    "FirstName" character varying(100) NOT NULL,
    "LastName" character varying(100) NOT NULL,
    "MiddleName" character varying(100),
    "Title" character varying(50),
    "Department" character varying(100),
    "Position" character varying(100),
    "Role" integer NOT NULL,
    "Email" character varying(200),
    "Phone" character varying(20),
    "Mobile" character varying(20),
    "Fax" character varying(20),
    "Website" character varying(500),
    "IsActive" boolean NOT NULL,
    "IsPrimary" boolean NOT NULL,
    "PreferredContactMethod" character varying(50),
    "TimeZone" character varying(50),
    "Language" character varying(10),
    "Notes" character varying(2000),
    "LastContactDate" timestamp with time zone,
    "UserId" uuid,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    "OrganizationId" uuid NOT NULL,
    "Version" character varying(10) NOT NULL,
    CONSTRAINT "PK_Contacts" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_Contacts_Customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES customer_management."Customers" ("Id") ON DELETE CASCADE
);
2025-06-04 17:06:34.900 +05:30 [INF] Executed DbCommand (165ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."CustomerAttributes" (
    "Id" uuid NOT NULL,
    "CustomerId" uuid NOT NULL,
    "Name" character varying(100) NOT NULL,
    "Value" character varying(500) NOT NULL,
    "DataType" character varying(50),
    "Category" character varying(100),
    "Description" character varying(500),
    "IsRequired" boolean NOT NULL,
    "IsVisible" boolean NOT NULL,
    "DisplayOrder" integer NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    CONSTRAINT "PK_CustomerAttributes" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_CustomerAttributes_Customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES customer_management."Customers" ("Id") ON DELETE CASCADE
);
2025-06-04 17:06:35.071 +05:30 [INF] Executed DbCommand (165ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."ServiceAgreements" (
    "Id" uuid NOT NULL,
    "CustomerId" uuid NOT NULL,
    "AgreementNumber" character varying(50) NOT NULL,
    "Name" character varying(200) NOT NULL,
    "Description" character varying(1000),
    "ServiceType" character varying(100) NOT NULL,
    "EffectiveDate" timestamp with time zone NOT NULL,
    "ExpiryDate" timestamp with time zone,
    "IsActive" boolean NOT NULL,
    "AutoRenew" boolean NOT NULL,
    "RenewalTermMonths" integer,
    "Terms" character varying(4000),
    "Conditions" character varying(4000),
    "PerformanceMetrics" character varying(2000),
    "ComplianceRequirements" character varying(2000),
    "ContractValue" numeric(18,2),
    "Currency" character varying(3),
    "Status" character varying(50) NOT NULL,
    "LastReviewDate" timestamp with time zone,
    "NextReviewDate" timestamp with time zone,
    "ReviewNotes" character varying(2000),
    "ApprovedBy" character varying(100),
    "ApprovedAt" timestamp with time zone,
    "DocumentPath" character varying(500),
    "DigitalSignature" character varying(1000),
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    "OrganizationId" uuid NOT NULL,
    "Version" character varying(10) NOT NULL,
    CONSTRAINT "PK_ServiceAgreements" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_ServiceAgreements_Customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES customer_management."Customers" ("Id") ON DELETE CASCADE
);
2025-06-04 17:06:35.245 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."ContactPermissions" (
    "Id" uuid NOT NULL,
    "ContactId" uuid NOT NULL,
    "Permission" character varying(100) NOT NULL,
    "Description" character varying(500),
    "IsActive" boolean NOT NULL,
    "ExpiryDate" timestamp with time zone,
    "GrantedBy" character varying(100),
    "GrantedAt" timestamp with time zone,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    CONSTRAINT "PK_ContactPermissions" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_ContactPermissions_Contacts_ContactId" FOREIGN KEY ("ContactId") REFERENCES customer_management."Contacts" ("Id") ON DELETE CASCADE
);
2025-06-04 17:06:35.413 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."ServiceAgreementTerms" (
    "Id" uuid NOT NULL,
    "ServiceAgreementId" uuid NOT NULL,
    "TermType" character varying(50) NOT NULL,
    "Name" character varying(200) NOT NULL,
    "Value" character varying(1000) NOT NULL,
    "Description" character varying(1000),
    "IsRequired" boolean NOT NULL,
    "IsNegotiable" boolean NOT NULL,
    "DisplayOrder" integer NOT NULL,
    "Category" character varying(100),
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    CONSTRAINT "PK_ServiceAgreementTerms" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_ServiceAgreementTerms_ServiceAgreements_ServiceAgreementId" FOREIGN KEY ("ServiceAgreementId") REFERENCES customer_management."ServiceAgreements" ("Id") ON DELETE CASCADE
);
2025-06-04 17:06:35.582 +05:30 [INF] Executed DbCommand (163ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ContactPermissions_ContactId" ON customer_management."ContactPermissions" ("ContactId");
2025-06-04 17:06:35.746 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ContactPermissions_ExpiryDate" ON customer_management."ContactPermissions" ("ExpiryDate");
2025-06-04 17:06:35.914 +05:30 [INF] Executed DbCommand (165ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ContactPermissions_IsActive" ON customer_management."ContactPermissions" ("IsActive");
2025-06-04 17:06:36.079 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ContactPermissions_IsDeleted" ON customer_management."ContactPermissions" ("IsDeleted");
2025-06-04 17:06:36.244 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ContactPermissions_Permission" ON customer_management."ContactPermissions" ("Permission");
2025-06-04 17:06:36.407 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_CustomerId" ON customer_management."Contacts" ("CustomerId");
2025-06-04 17:06:36.571 +05:30 [INF] Executed DbCommand (160ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_IsActive" ON customer_management."Contacts" ("IsActive");
2025-06-04 17:06:36.736 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_IsDeleted" ON customer_management."Contacts" ("IsDeleted");
2025-06-04 17:06:36.901 +05:30 [INF] Executed DbCommand (163ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_OrganizationId" ON customer_management."Contacts" ("OrganizationId");
2025-06-04 17:06:37.066 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_Role" ON customer_management."Contacts" ("Role");
2025-06-04 17:06:37.231 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_UserId" ON customer_management."Contacts" ("UserId");
2025-06-04 17:06:37.395 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CustomerAttributes_Category" ON customer_management."CustomerAttributes" ("Category");
2025-06-04 17:06:37.561 +05:30 [INF] Executed DbCommand (163ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CustomerAttributes_CustomerId" ON customer_management."CustomerAttributes" ("CustomerId");
2025-06-04 17:06:37.727 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_CustomerAttributes_CustomerId_Name" ON customer_management."CustomerAttributes" ("CustomerId", "Name");
2025-06-04 17:06:37.893 +05:30 [INF] Executed DbCommand (163ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CustomerAttributes_IsDeleted" ON customer_management."CustomerAttributes" ("IsDeleted");
2025-06-04 17:06:38.062 +05:30 [INF] Executed DbCommand (165ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CustomerAttributes_IsVisible" ON customer_management."CustomerAttributes" ("IsVisible");
2025-06-04 17:06:38.227 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CustomerAttributes_Name" ON customer_management."CustomerAttributes" ("Name");
2025-06-04 17:06:38.405 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Customers_CustomerNumber_OrganizationId" ON customer_management."Customers" ("CustomerNumber", "OrganizationId");
2025-06-04 17:06:38.570 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_Industry" ON customer_management."Customers" ("Industry");
2025-06-04 17:06:38.739 +05:30 [INF] Executed DbCommand (166ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_IsDeleted" ON customer_management."Customers" ("IsDeleted");
2025-06-04 17:06:38.906 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_OrganizationId" ON customer_management."Customers" ("OrganizationId");
2025-06-04 17:06:39.073 +05:30 [INF] Executed DbCommand (163ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_ParentCustomerId" ON customer_management."Customers" ("ParentCustomerId");
2025-06-04 17:06:39.240 +05:30 [INF] Executed DbCommand (163ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_Status" ON customer_management."Customers" ("Status");
2025-06-04 17:06:39.404 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_Tier" ON customer_management."Customers" ("Tier");
2025-06-04 17:06:39.569 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_Type" ON customer_management."Customers" ("Type");
2025-06-04 17:06:39.733 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_ServiceAgreements_AgreementNumber_OrganizationId" ON customer_management."ServiceAgreements" ("AgreementNumber", "OrganizationId");
2025-06-04 17:06:39.897 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_CustomerId" ON customer_management."ServiceAgreements" ("CustomerId");
2025-06-04 17:06:40.071 +05:30 [INF] Executed DbCommand (171ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_EffectiveDate" ON customer_management."ServiceAgreements" ("EffectiveDate");
2025-06-04 17:06:40.235 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_ExpiryDate" ON customer_management."ServiceAgreements" ("ExpiryDate");
2025-06-04 17:06:40.399 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_IsActive" ON customer_management."ServiceAgreements" ("IsActive");
2025-06-04 17:06:40.569 +05:30 [INF] Executed DbCommand (167ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_IsDeleted" ON customer_management."ServiceAgreements" ("IsDeleted");
2025-06-04 17:06:40.740 +05:30 [INF] Executed DbCommand (167ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_NextReviewDate" ON customer_management."ServiceAgreements" ("NextReviewDate");
2025-06-04 17:06:40.927 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_OrganizationId" ON customer_management."ServiceAgreements" ("OrganizationId");
2025-06-04 17:06:41.093 +05:30 [INF] Executed DbCommand (163ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_ServiceType" ON customer_management."ServiceAgreements" ("ServiceType");
2025-06-04 17:06:41.257 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_Status" ON customer_management."ServiceAgreements" ("Status");
2025-06-04 17:06:41.423 +05:30 [INF] Executed DbCommand (163ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreementTerms_Category" ON customer_management."ServiceAgreementTerms" ("Category");
2025-06-04 17:06:41.591 +05:30 [INF] Executed DbCommand (164ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreementTerms_DisplayOrder" ON customer_management."ServiceAgreementTerms" ("DisplayOrder");
2025-06-04 17:06:41.767 +05:30 [INF] Executed DbCommand (171ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreementTerms_IsDeleted" ON customer_management."ServiceAgreementTerms" ("IsDeleted");
2025-06-04 17:06:41.938 +05:30 [INF] Executed DbCommand (161ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreementTerms_ServiceAgreementId" ON customer_management."ServiceAgreementTerms" ("ServiceAgreementId");
2025-06-04 17:06:42.106 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_ServiceAgreementTerms_ServiceAgreementId_Name" ON customer_management."ServiceAgreementTerms" ("ServiceAgreementId", "Name");
2025-06-04 17:06:42.271 +05:30 [INF] Executed DbCommand (162ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreementTerms_TermType" ON customer_management."ServiceAgreementTerms" ("TermType");
2025-06-04 17:06:42.435 +05:30 [INF] Executed DbCommand (160ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250527065642_InitialCreate', '8.0.0');
2025-06-04 17:08:01.011 +05:30 [INF] Executed DbCommand (205ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:08:01.241 +05:30 [INF] Executed DbCommand (158ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-04 17:08:02.511 +05:30 [INF] Executed DbCommand (156ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:08:03.809 +05:30 [INF] Executed DbCommand (158ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:08:03.970 +05:30 [INF] Executed DbCommand (157ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-04 17:08:03.983 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-06-04 17:08:34.222 +05:30 [INF] Executed DbCommand (310ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DROP DATABASE tritrackz_customermanagement WITH (FORCE);
2025-06-04 17:08:55.316 +05:30 [INF] Executed DbCommand (242ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE DATABASE tritrackz_customermanagement;
2025-06-04 17:08:59.039 +05:30 [INF] Executed DbCommand (176ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "__EFMigrationsHistory" (
    "MigrationId" character varying(150) NOT NULL,
    "ProductVersion" character varying(32) NOT NULL,
    CONSTRAINT "PK___EFMigrationsHistory" PRIMARY KEY ("MigrationId")
);
2025-06-04 17:09:00.369 +05:30 [INF] Executed DbCommand (210ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:09:00.549 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-04 17:09:00.602 +05:30 [INF] Applying migration '20250527065642_InitialCreate'.
2025-06-04 17:09:01.077 +05:30 [INF] Executed DbCommand (176ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
DO $EF$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'customer_management') THEN
        CREATE SCHEMA customer_management;
    END IF;
END $EF$;
2025-06-04 17:09:01.259 +05:30 [INF] Executed DbCommand (177ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."Customers" (
    "Id" uuid NOT NULL,
    "CustomerNumber" character varying(50) NOT NULL,
    "Name" character varying(200) NOT NULL,
    "LegalName" character varying(200),
    "Type" integer NOT NULL,
    "Status" integer NOT NULL,
    "Tier" integer NOT NULL,
    "TaxId" character varying(50),
    "RegistrationNumber" character varying(50),
    "Industry" character varying(100),
    "BusinessType" character varying(100),
    "EstablishedDate" timestamp with time zone,
    "Website" character varying(500),
    "Description" character varying(1000),
    "Notes" character varying(2000),
    "PreferredLanguage" character varying(10),
    "PreferredCurrency" character varying(3),
    "TimeZone" character varying(50),
    "IsVip" boolean NOT NULL,
    "AnnualRevenue" numeric(18,2),
    "EmployeeCount" integer,
    "ParentCustomerId" uuid,
    "ProfileCompleteness" integer NOT NULL,
    "LastActivityDate" timestamp with time zone,
    "CreditLimit" numeric(18,2),
    "PaymentTermsDays" integer,
    "InterestRate" numeric(5,2),
    "CreditCurrency" character varying(3),
    "CreditRequiresApproval" boolean,
    "CreditLastReviewDate" timestamp with time zone,
    "CreditNextReviewDate" timestamp with time zone,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    "OrganizationId" uuid NOT NULL,
    "Version" character varying(10) NOT NULL,
    CONSTRAINT "PK_Customers" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_Customers_Customers_ParentCustomerId" FOREIGN KEY ("ParentCustomerId") REFERENCES customer_management."Customers" ("Id") ON DELETE RESTRICT
);
2025-06-04 17:09:01.446 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."Contacts" (
    "Id" uuid NOT NULL,
    "CustomerId" uuid NOT NULL,
    "FirstName" character varying(100) NOT NULL,
    "LastName" character varying(100) NOT NULL,
    "MiddleName" character varying(100),
    "Title" character varying(50),
    "Department" character varying(100),
    "Position" character varying(100),
    "Role" integer NOT NULL,
    "Email" character varying(200),
    "Phone" character varying(20),
    "Mobile" character varying(20),
    "Fax" character varying(20),
    "Website" character varying(500),
    "IsActive" boolean NOT NULL,
    "IsPrimary" boolean NOT NULL,
    "PreferredContactMethod" character varying(50),
    "TimeZone" character varying(50),
    "Language" character varying(10),
    "Notes" character varying(2000),
    "LastContactDate" timestamp with time zone,
    "UserId" uuid,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    "OrganizationId" uuid NOT NULL,
    "Version" character varying(10) NOT NULL,
    CONSTRAINT "PK_Contacts" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_Contacts_Customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES customer_management."Customers" ("Id") ON DELETE CASCADE
);
2025-06-04 17:09:01.634 +05:30 [INF] Executed DbCommand (178ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."CustomerAttributes" (
    "Id" uuid NOT NULL,
    "CustomerId" uuid NOT NULL,
    "Name" character varying(100) NOT NULL,
    "Value" character varying(500) NOT NULL,
    "DataType" character varying(50),
    "Category" character varying(100),
    "Description" character varying(500),
    "IsRequired" boolean NOT NULL,
    "IsVisible" boolean NOT NULL,
    "DisplayOrder" integer NOT NULL,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    CONSTRAINT "PK_CustomerAttributes" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_CustomerAttributes_Customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES customer_management."Customers" ("Id") ON DELETE CASCADE
);
2025-06-04 17:09:01.826 +05:30 [INF] Executed DbCommand (180ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."ServiceAgreements" (
    "Id" uuid NOT NULL,
    "CustomerId" uuid NOT NULL,
    "AgreementNumber" character varying(50) NOT NULL,
    "Name" character varying(200) NOT NULL,
    "Description" character varying(1000),
    "ServiceType" character varying(100) NOT NULL,
    "EffectiveDate" timestamp with time zone NOT NULL,
    "ExpiryDate" timestamp with time zone,
    "IsActive" boolean NOT NULL,
    "AutoRenew" boolean NOT NULL,
    "RenewalTermMonths" integer,
    "Terms" character varying(4000),
    "Conditions" character varying(4000),
    "PerformanceMetrics" character varying(2000),
    "ComplianceRequirements" character varying(2000),
    "ContractValue" numeric(18,2),
    "Currency" character varying(3),
    "Status" character varying(50) NOT NULL,
    "LastReviewDate" timestamp with time zone,
    "NextReviewDate" timestamp with time zone,
    "ReviewNotes" character varying(2000),
    "ApprovedBy" character varying(100),
    "ApprovedAt" timestamp with time zone,
    "DocumentPath" character varying(500),
    "DigitalSignature" character varying(1000),
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    "OrganizationId" uuid NOT NULL,
    "Version" character varying(10) NOT NULL,
    CONSTRAINT "PK_ServiceAgreements" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_ServiceAgreements_Customers_CustomerId" FOREIGN KEY ("CustomerId") REFERENCES customer_management."Customers" ("Id") ON DELETE CASCADE
);
2025-06-04 17:09:02.012 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."ContactPermissions" (
    "Id" uuid NOT NULL,
    "ContactId" uuid NOT NULL,
    "Permission" character varying(100) NOT NULL,
    "Description" character varying(500),
    "IsActive" boolean NOT NULL,
    "ExpiryDate" timestamp with time zone,
    "GrantedBy" character varying(100),
    "GrantedAt" timestamp with time zone,
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    CONSTRAINT "PK_ContactPermissions" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_ContactPermissions_Contacts_ContactId" FOREIGN KEY ("ContactId") REFERENCES customer_management."Contacts" ("Id") ON DELETE CASCADE
);
2025-06-04 17:09:02.192 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE customer_management."ServiceAgreementTerms" (
    "Id" uuid NOT NULL,
    "ServiceAgreementId" uuid NOT NULL,
    "TermType" character varying(50) NOT NULL,
    "Name" character varying(200) NOT NULL,
    "Value" character varying(1000) NOT NULL,
    "Description" character varying(1000),
    "IsRequired" boolean NOT NULL,
    "IsNegotiable" boolean NOT NULL,
    "DisplayOrder" integer NOT NULL,
    "Category" character varying(100),
    "CreatedAt" timestamp with time zone NOT NULL,
    "UpdatedAt" timestamp with time zone,
    "CreatedBy" character varying(100) NOT NULL,
    "UpdatedBy" character varying(100),
    "IsDeleted" boolean NOT NULL,
    "DeletedAt" timestamp with time zone,
    "DeletedBy" character varying(100),
    CONSTRAINT "PK_ServiceAgreementTerms" PRIMARY KEY ("Id"),
    CONSTRAINT "FK_ServiceAgreementTerms_ServiceAgreements_ServiceAgreementId" FOREIGN KEY ("ServiceAgreementId") REFERENCES customer_management."ServiceAgreements" ("Id") ON DELETE CASCADE
);
2025-06-04 17:09:02.370 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ContactPermissions_ContactId" ON customer_management."ContactPermissions" ("ContactId");
2025-06-04 17:09:02.545 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ContactPermissions_ExpiryDate" ON customer_management."ContactPermissions" ("ExpiryDate");
2025-06-04 17:09:02.721 +05:30 [INF] Executed DbCommand (173ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ContactPermissions_IsActive" ON customer_management."ContactPermissions" ("IsActive");
2025-06-04 17:09:02.896 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ContactPermissions_IsDeleted" ON customer_management."ContactPermissions" ("IsDeleted");
2025-06-04 17:09:03.074 +05:30 [INF] Executed DbCommand (175ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ContactPermissions_Permission" ON customer_management."ContactPermissions" ("Permission");
2025-06-04 17:09:03.249 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_CustomerId" ON customer_management."Contacts" ("CustomerId");
2025-06-04 17:09:03.425 +05:30 [INF] Executed DbCommand (171ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_IsActive" ON customer_management."Contacts" ("IsActive");
2025-06-04 17:09:03.600 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_IsDeleted" ON customer_management."Contacts" ("IsDeleted");
2025-06-04 17:09:03.787 +05:30 [INF] Executed DbCommand (179ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_OrganizationId" ON customer_management."Contacts" ("OrganizationId");
2025-06-04 17:09:03.967 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_Role" ON customer_management."Contacts" ("Role");
2025-06-04 17:09:04.144 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Contacts_UserId" ON customer_management."Contacts" ("UserId");
2025-06-04 17:09:04.321 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CustomerAttributes_Category" ON customer_management."CustomerAttributes" ("Category");
2025-06-04 17:09:04.498 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CustomerAttributes_CustomerId" ON customer_management."CustomerAttributes" ("CustomerId");
2025-06-04 17:09:04.685 +05:30 [INF] Executed DbCommand (183ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_CustomerAttributes_CustomerId_Name" ON customer_management."CustomerAttributes" ("CustomerId", "Name");
2025-06-04 17:09:04.867 +05:30 [INF] Executed DbCommand (178ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CustomerAttributes_IsDeleted" ON customer_management."CustomerAttributes" ("IsDeleted");
2025-06-04 17:09:05.046 +05:30 [INF] Executed DbCommand (173ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CustomerAttributes_IsVisible" ON customer_management."CustomerAttributes" ("IsVisible");
2025-06-04 17:09:05.228 +05:30 [INF] Executed DbCommand (178ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_CustomerAttributes_Name" ON customer_management."CustomerAttributes" ("Name");
2025-06-04 17:09:05.410 +05:30 [INF] Executed DbCommand (179ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_Customers_CustomerNumber_OrganizationId" ON customer_management."Customers" ("CustomerNumber", "OrganizationId");
2025-06-04 17:09:05.588 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_Industry" ON customer_management."Customers" ("Industry");
2025-06-04 17:09:05.767 +05:30 [INF] Executed DbCommand (175ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_IsDeleted" ON customer_management."Customers" ("IsDeleted");
2025-06-04 17:09:05.953 +05:30 [INF] Executed DbCommand (175ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_OrganizationId" ON customer_management."Customers" ("OrganizationId");
2025-06-04 17:09:06.138 +05:30 [INF] Executed DbCommand (182ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_ParentCustomerId" ON customer_management."Customers" ("ParentCustomerId");
2025-06-04 17:09:06.315 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_Status" ON customer_management."Customers" ("Status");
2025-06-04 17:09:06.505 +05:30 [INF] Executed DbCommand (182ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_Tier" ON customer_management."Customers" ("Tier");
2025-06-04 17:09:06.685 +05:30 [INF] Executed DbCommand (176ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_Customers_Type" ON customer_management."Customers" ("Type");
2025-06-04 17:09:06.866 +05:30 [INF] Executed DbCommand (176ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_ServiceAgreements_AgreementNumber_OrganizationId" ON customer_management."ServiceAgreements" ("AgreementNumber", "OrganizationId");
2025-06-04 17:09:07.047 +05:30 [INF] Executed DbCommand (176ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_CustomerId" ON customer_management."ServiceAgreements" ("CustomerId");
2025-06-04 17:09:07.237 +05:30 [INF] Executed DbCommand (173ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_EffectiveDate" ON customer_management."ServiceAgreements" ("EffectiveDate");
2025-06-04 17:09:07.414 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_ExpiryDate" ON customer_management."ServiceAgreements" ("ExpiryDate");
2025-06-04 17:09:07.590 +05:30 [INF] Executed DbCommand (173ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_IsActive" ON customer_management."ServiceAgreements" ("IsActive");
2025-06-04 17:09:07.771 +05:30 [INF] Executed DbCommand (177ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_IsDeleted" ON customer_management."ServiceAgreements" ("IsDeleted");
2025-06-04 17:09:07.947 +05:30 [INF] Executed DbCommand (173ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_NextReviewDate" ON customer_management."ServiceAgreements" ("NextReviewDate");
2025-06-04 17:09:08.125 +05:30 [INF] Executed DbCommand (171ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_OrganizationId" ON customer_management."ServiceAgreements" ("OrganizationId");
2025-06-04 17:09:08.303 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_ServiceType" ON customer_management."ServiceAgreements" ("ServiceType");
2025-06-04 17:09:08.481 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreements_Status" ON customer_management."ServiceAgreements" ("Status");
2025-06-04 17:09:08.669 +05:30 [INF] Executed DbCommand (182ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreementTerms_Category" ON customer_management."ServiceAgreementTerms" ("Category");
2025-06-04 17:09:08.847 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreementTerms_DisplayOrder" ON customer_management."ServiceAgreementTerms" ("DisplayOrder");
2025-06-04 17:09:09.026 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreementTerms_IsDeleted" ON customer_management."ServiceAgreementTerms" ("IsDeleted");
2025-06-04 17:09:09.202 +05:30 [INF] Executed DbCommand (173ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreementTerms_ServiceAgreementId" ON customer_management."ServiceAgreementTerms" ("ServiceAgreementId");
2025-06-04 17:09:09.379 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX "IX_ServiceAgreementTerms_ServiceAgreementId_Name" ON customer_management."ServiceAgreementTerms" ("ServiceAgreementId", "Name");
2025-06-04 17:09:09.555 +05:30 [INF] Executed DbCommand (172ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX "IX_ServiceAgreementTerms_TermType" ON customer_management."ServiceAgreementTerms" ("TermType");
2025-06-04 17:09:09.731 +05:30 [INF] Executed DbCommand (173ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "__EFMigrationsHistory" ("MigrationId", "ProductVersion")
VALUES ('20250527065642_InitialCreate', '8.0.0');
2025-06-04 17:33:57.474 +05:30 [INF] Starting Customer Management API
2025-06-04 17:34:01.924 +05:30 [INF] Executed DbCommand (196ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-04 17:34:03.232 +05:30 [INF] Executed DbCommand (150ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:34:03.399 +05:30 [INF] Executed DbCommand (150ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-04 17:34:04.713 +05:30 [INF] Executed DbCommand (150ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:34:06.093 +05:30 [INF] Executed DbCommand (155ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:34:06.249 +05:30 [INF] Executed DbCommand (152ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-04 17:34:06.262 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-06-04 17:34:06.268 +05:30 [INF] Starting data seeding...
2025-06-04 17:34:06.638 +05:30 [INF] Executed DbCommand (153ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM customer_management."Customers" AS c
    WHERE NOT (c."IsDeleted"))
2025-06-04 17:34:06.655 +05:30 [INF] Seeding customers...
2025-06-04 17:34:06.871 +05:30 [INF] Seeded 3 customers with contacts and service agreements
2025-06-04 17:34:07.237 +05:30 [INF] Executed DbCommand (174ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Decimal), @p2='?', @p3='?' (DbType = DateTime), @p4='?', @p5='?', @p6='?' (DbType = DateTime), @p7='?', @p8='?', @p9='?' (DbType = Int32), @p10='?' (DbType = DateTime), @p11='?', @p12='?' (DbType = Boolean), @p13='?' (DbType = Boolean), @p14='?' (DbType = DateTime), @p15='?', @p16='?', @p17='?', @p18='?' (DbType = Guid), @p19='?' (DbType = Guid), @p20='?', @p21='?', @p22='?' (DbType = Int32), @p23='?', @p24='?' (DbType = Int32), @p25='?', @p26='?' (DbType = Int32), @p27='?', @p28='?' (DbType = Int32), @p29='?' (DbType = DateTime), @p30='?', @p31='?', @p32='?', @p33='?' (DbType = Guid), @p34='?' (DbType = Decimal), @p35='?', @p36='?' (DbType = DateTime), @p37='?', @p38='?', @p39='?' (DbType = DateTime), @p40='?', @p41='?', @p42='?' (DbType = Int32), @p43='?' (DbType = DateTime), @p44='?', @p45='?' (DbType = Boolean), @p46='?' (DbType = Boolean), @p47='?' (DbType = DateTime), @p48='?', @p49='?', @p50='?', @p51='?' (DbType = Guid), @p52='?' (DbType = Guid), @p53='?', @p54='?', @p55='?' (DbType = Int32), @p56='?', @p57='?' (DbType = Int32), @p58='?', @p59='?' (DbType = Int32), @p60='?', @p61='?' (DbType = Int32), @p62='?' (DbType = DateTime), @p63='?', @p64='?', @p65='?', @p66='?' (DbType = Decimal), @p67='?', @p68='?' (DbType = Decimal), @p69='?' (DbType = DateTime), @p70='?' (DbType = DateTime), @p71='?' (DbType = Int32), @p72='?' (DbType = Boolean), @p73='?' (DbType = Guid), @p74='?' (DbType = Decimal), @p75='?', @p76='?' (DbType = DateTime), @p77='?', @p78='?', @p79='?' (DbType = DateTime), @p80='?', @p81='?', @p82='?' (DbType = Int32), @p83='?' (DbType = DateTime), @p84='?', @p85='?' (DbType = Boolean), @p86='?' (DbType = Boolean), @p87='?' (DbType = DateTime), @p88='?', @p89='?', @p90='?', @p91='?' (DbType = Guid), @p92='?' (DbType = Guid), @p93='?', @p94='?', @p95='?' (DbType = Int32), @p96='?', @p97='?' (DbType = Int32), @p98='?', @p99='?' (DbType = Int32), @p100='?', @p101='?' (DbType = Int32), @p102='?' (DbType = DateTime), @p103='?', @p104='?', @p105='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO customer_management."Customers" ("Id", "AnnualRevenue", "BusinessType", "CreatedAt", "CreatedBy", "CustomerNumber", "DeletedAt", "DeletedBy", "Description", "EmployeeCount", "EstablishedDate", "Industry", "IsDeleted", "IsVip", "LastActivityDate", "LegalName", "Name", "Notes", "OrganizationId", "ParentCustomerId", "PreferredCurrency", "PreferredLanguage", "ProfileCompleteness", "RegistrationNumber", "Status", "TaxId", "Tier", "TimeZone", "Type", "UpdatedAt", "UpdatedBy", "Version", "Website")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32);
INSERT INTO customer_management."Customers" ("Id", "AnnualRevenue", "BusinessType", "CreatedAt", "CreatedBy", "CustomerNumber", "DeletedAt", "DeletedBy", "Description", "EmployeeCount", "EstablishedDate", "Industry", "IsDeleted", "IsVip", "LastActivityDate", "LegalName", "Name", "Notes", "OrganizationId", "ParentCustomerId", "PreferredCurrency", "PreferredLanguage", "ProfileCompleteness", "RegistrationNumber", "Status", "TaxId", "Tier", "TimeZone", "Type", "UpdatedAt", "UpdatedBy", "Version", "Website", "CreditLimit", "CreditCurrency", "InterestRate", "CreditLastReviewDate", "CreditNextReviewDate", "PaymentTermsDays", "CreditRequiresApproval")
VALUES (@p33, @p34, @p35, @p36, @p37, @p38, @p39, @p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72);
INSERT INTO customer_management."Customers" ("Id", "AnnualRevenue", "BusinessType", "CreatedAt", "CreatedBy", "CustomerNumber", "DeletedAt", "DeletedBy", "Description", "EmployeeCount", "EstablishedDate", "Industry", "IsDeleted", "IsVip", "LastActivityDate", "LegalName", "Name", "Notes", "OrganizationId", "ParentCustomerId", "PreferredCurrency", "PreferredLanguage", "ProfileCompleteness", "RegistrationNumber", "Status", "TaxId", "Tier", "TimeZone", "Type", "UpdatedAt", "UpdatedBy", "Version", "Website")
VALUES (@p73, @p74, @p75, @p76, @p77, @p78, @p79, @p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89, @p90, @p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, @p99, @p100, @p101, @p102, @p103, @p104, @p105);
2025-06-04 17:34:07.535 +05:30 [INF] Data seeding completed successfully
2025-06-04 17:34:07.537 +05:30 [INF] Database initialization completed successfully
2025-06-04 17:34:07.555 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 17:34:07.923 +05:30 [INF] Now listening on: http://localhost:5004
2025-06-04 17:34:07.925 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 17:34:07.926 +05:30 [INF] Hosting environment: Development
2025-06-04 17:34:07.927 +05:30 [INF] Content root path: D:\Projects\TRITRACKZ\TriTrackzMicroservices\CustomerManagement\CustomerManagement.API
2025-06-04 17:34:09.800 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/ - null null
2025-06-04 17:34:09.904 +05:30 [WRN] Failed to determine the https port for redirect.
2025-06-04 17:34:09.948 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/ - 404 0 null 205.0147ms
2025-06-04 17:34:10.022 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5004/, Response status code: 404
2025-06-04 17:34:15.037 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/index.html - null null
2025-06-04 17:34:15.206 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/index.html - 200 null text/html;charset=utf-8 169.4992ms
2025-06-04 17:34:15.224 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/swagger-ui-bundle.js - null null
2025-06-04 17:34:15.225 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/swagger-ui.css - null null
2025-06-04 17:34:15.227 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/swagger-ui-standalone-preset.js - null null
2025-06-04 17:34:15.331 +05:30 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-06-04 17:34:15.331 +05:30 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-06-04 17:34:15.587 +05:30 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-06-04 17:34:15.596 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/swagger-ui-bundle.js - 200 1096145 text/javascript 372.3364ms
2025-06-04 17:34:15.602 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/swagger-ui.css - 200 143943 text/css 377.6561ms
2025-06-04 17:34:15.606 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/swagger-ui-standalone-preset.js - 200 339486 text/javascript 379.9214ms
2025-06-04 17:34:15.831 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 17:34:15.881 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/favicon-32x32.png - null null
2025-06-04 17:34:15.898 +05:30 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-06-04 17:34:15.902 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/favicon-32x32.png - 200 628 image/png 21.1578ms
2025-06-04 17:34:16.191 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 359.5556ms
2025-06-04 17:36:10.214 +05:30 [INF] Application is shutting down...
2025-06-04 17:37:14.932 +05:30 [INF] Starting Customer Management API
2025-06-04 17:37:21.069 +05:30 [INF] Executed DbCommand (246ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM pg_class AS cls
JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
WHERE
        cls.relkind IN ('r', 'v', 'm', 'f', 'p') AND
        ns.nspname NOT IN ('pg_catalog', 'information_schema') AND
        -- Exclude tables which are members of PG extensions
        NOT EXISTS (
            SELECT 1 FROM pg_depend WHERE
                classid=(
                    SELECT cls.oid
                    FROM pg_class AS cls
                             JOIN pg_namespace AS ns ON ns.oid = cls.relnamespace
                    WHERE relname='pg_class' AND ns.nspname='pg_catalog'
                ) AND
                objid=cls.oid AND
                deptype IN ('e', 'x')
        )
2025-06-04 17:37:22.330 +05:30 [INF] Executed DbCommand (169ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:37:22.517 +05:30 [INF] Executed DbCommand (170ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-04 17:37:23.821 +05:30 [INF] Executed DbCommand (177ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:37:25.055 +05:30 [INF] Executed DbCommand (171ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1 FROM pg_catalog.pg_class c
    JOIN pg_catalog.pg_namespace n ON n.oid=c.relnamespace
    WHERE n.nspname='public' AND
          c.relname='__EFMigrationsHistory'
)
2025-06-04 17:37:25.227 +05:30 [INF] Executed DbCommand (166ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT "MigrationId", "ProductVersion"
FROM "__EFMigrationsHistory"
ORDER BY "MigrationId";
2025-06-04 17:37:25.248 +05:30 [INF] No migrations were applied. The database is already up to date.
2025-06-04 17:37:25.253 +05:30 [INF] Starting data seeding...
2025-06-04 17:37:25.651 +05:30 [INF] Executed DbCommand (167ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM customer_management."Customers" AS c
    WHERE NOT (c."IsDeleted"))
2025-06-04 17:37:25.686 +05:30 [INF] Data already exists, skipping seeding
2025-06-04 17:37:25.688 +05:30 [INF] Database initialization completed successfully
2025-06-04 17:37:25.724 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-06-04 17:37:26.706 +05:30 [INF] Now listening on: http://localhost:5004
2025-06-04 17:37:26.823 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-06-04 17:37:26.826 +05:30 [INF] Hosting environment: Development
2025-06-04 17:37:26.832 +05:30 [INF] Content root path: D:\Projects\TRITRACKZ\TriTrackzMicroservices\CustomerManagement\CustomerManagement.API
2025-06-04 17:47:10.838 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/ - null null
2025-06-04 17:47:11.074 +05:30 [WRN] Failed to determine the https port for redirect.
2025-06-04 17:47:11.857 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/ - 404 0 null 1023.5865ms
2025-06-04 17:47:11.880 +05:30 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:5004/, Response status code: 404
2025-06-04 17:47:16.492 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/index.html - null null
2025-06-04 17:47:16.598 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/index.html - 200 null text/html;charset=utf-8 105.8963ms
2025-06-04 17:47:16.946 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 17:47:17.297 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 350.9074ms
2025-06-04 17:59:31.882 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 17:59:31.965 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 82.8488ms
2025-06-04 17:59:51.656 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 17:59:51.716 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 59.4974ms
2025-06-04 18:03:56.577 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 18:03:56.658 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 81.0336ms
2025-06-04 18:12:12.886 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 18:12:12.945 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 58.3817ms
2025-06-04 18:12:23.251 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 18:12:23.322 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 70.128ms
2025-06-04 18:12:27.821 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 18:12:27.892 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 71.0311ms
2025-06-04 18:16:26.601 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 18:16:26.668 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 67.0103ms
2025-06-04 18:16:35.806 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 18:16:35.894 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 88.3343ms
2025-06-04 18:16:51.450 +05:30 [INF] Request starting HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - null null
2025-06-04 18:16:51.514 +05:30 [INF] Request finished HTTP/1.1 GET http://localhost:5004/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 63.2987ms
2025-06-04 18:21:05.021 +05:30 [INF] Application is shutting down...
