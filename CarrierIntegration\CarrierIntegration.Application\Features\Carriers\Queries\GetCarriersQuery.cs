using AutoMapper;
using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Domain.Enums;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Features.Carriers.Queries
{
    public class GetCarriersQuery : IRequest<ApiResponseDto<PagedResultDto<CarrierDto>>>
    {
        public Guid OrganizationId { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public string? SearchTerm { get; set; }
        public CarrierType? Type { get; set; }
        public CarrierStatus? Status { get; set; }
        public bool? IsActive { get; set; }
        public bool? SupportsTracking { get; set; }
        public bool? SupportsRating { get; set; }
        public bool? SupportsLabeling { get; set; }
        public bool? SupportsPickup { get; set; }
        public bool? SupportsInternational { get; set; }
    }

    public class GetCarriersQueryValidator : AbstractValidator<GetCarriersQuery>
    {
        public GetCarriersQueryValidator()
        {
            RuleFor(x => x.OrganizationId)
                .NotEmpty()
                .WithMessage("Organization ID is required");

            RuleFor(x => x.PageNumber)
                .GreaterThan(0)
                .WithMessage("Page number must be greater than 0");

            RuleFor(x => x.PageSize)
                .InclusiveBetween(1, 100)
                .WithMessage("Page size must be between 1 and 100");

            RuleFor(x => x.Type)
                .IsInEnum()
                .When(x => x.Type.HasValue)
                .WithMessage("Invalid carrier type");

            RuleFor(x => x.Status)
                .IsInEnum()
                .When(x => x.Status.HasValue)
                .WithMessage("Invalid carrier status");
        }
    }

    public class GetCarriersQueryHandler : IRequestHandler<GetCarriersQuery, ApiResponseDto<PagedResultDto<CarrierDto>>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<GetCarriersQueryHandler> _logger;

        public GetCarriersQueryHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<GetCarriersQueryHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ApiResponseDto<PagedResultDto<CarrierDto>>> Handle(GetCarriersQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting carriers for organization {OrganizationId} with filters: Type={Type}, Status={Status}, SearchTerm={SearchTerm}",
                    request.OrganizationId, request.Type, request.Status, request.SearchTerm);

                var (carriers, totalCount) = await _unitOfWork.Carriers.GetPagedAsync(
                    request.OrganizationId,
                    request.PageNumber,
                    request.PageSize,
                    request.SearchTerm,
                    request.Type,
                    request.Status);

                var carrierDtos = _mapper.Map<System.Collections.Generic.List<CarrierDto>>(carriers);

                // Apply additional filters if needed
                if (request.IsActive.HasValue)
                {
                    carrierDtos = carrierDtos.FindAll(c => c.IsActive == request.IsActive.Value);
                }

                if (request.SupportsTracking.HasValue)
                {
                    carrierDtos = carrierDtos.FindAll(c => c.SupportsTracking == request.SupportsTracking.Value);
                }

                if (request.SupportsRating.HasValue)
                {
                    carrierDtos = carrierDtos.FindAll(c => c.SupportsRating == request.SupportsRating.Value);
                }

                if (request.SupportsLabeling.HasValue)
                {
                    carrierDtos = carrierDtos.FindAll(c => c.SupportsLabeling == request.SupportsLabeling.Value);
                }

                if (request.SupportsPickup.HasValue)
                {
                    carrierDtos = carrierDtos.FindAll(c => c.SupportsPickup == request.SupportsPickup.Value);
                }

                if (request.SupportsInternational.HasValue)
                {
                    carrierDtos = carrierDtos.FindAll(c => c.SupportsInternational == request.SupportsInternational.Value);
                }

                var result = new PagedResultDto<CarrierDto>
                {
                    Items = carrierDtos,
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };

                _logger.LogInformation("Successfully retrieved {Count} carriers out of {TotalCount} for organization {OrganizationId}",
                    carrierDtos.Count, totalCount, request.OrganizationId);

                return ApiResponseDto<PagedResultDto<CarrierDto>>.SuccessResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting carriers for organization {OrganizationId}", request.OrganizationId);
                return ApiResponseDto<PagedResultDto<CarrierDto>>.ErrorResult($"Failed to get carriers: {ex.Message}");
            }
        }
    }
}
