using CarrierIntegration.Domain.Common;
using CarrierIntegration.Domain.Enums;
using CarrierIntegration.Domain.ValueObjects;
using System;

namespace CarrierIntegration.Domain.Entities
{
    public class CarrierAccount : BaseEntity
    {
        public Guid CarrierId { get; private set; }
        public string AccountNumber { get; private set; }
        public string AccountName { get; private set; }
        public string? Description { get; private set; }
        public bool IsActive { get; private set; }
        public bool IsDefault { get; private set; }
        public string? Username { get; private set; }
        public string? PasswordHash { get; private set; }
        public string? ApiKey { get; private set; }
        public string? ApiSecret { get; private set; }
        public string? AccessToken { get; private set; }
        public string? RefreshToken { get; private set; }
        public DateTime? TokenExpiryDate { get; private set; }
        public string? AuthenticationMethod { get; private set; }
        public string? AuthenticationUrl { get; private set; }
        public ContactInfo BillingContact { get; private set; }
        public Address BillingAddress { get; private set; }
        public ContactInfo? TechnicalContact { get; private set; }
        public string? CustomerNumber { get; private set; }
        public string? MeterNumber { get; private set; }
        public string? LicenseKey { get; private set; }
        public string? DeveloperKey { get; private set; }
        public string? Environment { get; private set; } // Production, Sandbox, Test
        public string? BaseUrl { get; private set; }
        public string? TestBaseUrl { get; private set; }
        public bool IsTestMode { get; private set; }
        public decimal? CreditLimit { get; private set; }
        public Currency? CreditCurrency { get; private set; }
        public decimal? CurrentBalance { get; private set; }
        public decimal? AvailableCredit => CreditLimit.HasValue && CurrentBalance.HasValue 
            ? CreditLimit.Value - CurrentBalance.Value : null;
        public string? PaymentTerms { get; private set; }
        public string? BillingCycle { get; private set; }
        public DateTime? LastBillingDate { get; private set; }
        public DateTime? NextBillingDate { get; private set; }
        public bool AutoPay { get; private set; }
        public string? PaymentMethod { get; private set; }
        public string? ContractNumber { get; private set; }
        public DateTime? ContractStartDate { get; private set; }
        public DateTime? ContractEndDate { get; private set; }
        public string? ServiceLevel { get; private set; }
        public string? DiscountCode { get; private set; }
        public decimal? DiscountPercentage { get; private set; }
        public string? SpecialRates { get; private set; }
        public string? RestrictedServices { get; private set; }
        public string? AllowedServices { get; private set; }
        public int? MaxShipmentsPerDay { get; private set; }
        public int? MaxShipmentsPerMonth { get; private set; }
        public decimal? MaxShipmentValue { get; private set; }
        public Currency? MaxShipmentValueCurrency { get; private set; }
        public string? TimeZone { get; private set; }
        public string? PreferredLanguage { get; private set; }
        public string? NotificationPreferences { get; private set; }
        public string? ReportingPreferences { get; private set; }
        public DateTime? LastLoginDate { get; private set; }
        public DateTime? LastApiCall { get; private set; }
        public int ApiCallsToday { get; private set; }
        public int ApiCallsThisMonth { get; private set; }
        public int? MaxApiCallsPerDay { get; private set; }
        public int? MaxApiCallsPerMonth { get; private set; }
        public string? Notes { get; private set; }
        public string? InternalReference { get; private set; }
        public string? ExternalReference { get; private set; }
        public string? Tags { get; private set; }
        public DateTime? ActivationDate { get; private set; }
        public DateTime? DeactivationDate { get; private set; }
        public string? DeactivationReason { get; private set; }

        private CarrierAccount() { } // For EF Core

        public CarrierAccount(
            Guid carrierId,
            Guid organizationId,
            string accountNumber,
            string accountName,
            ContactInfo billingContact,
            Address billingAddress,
            string createdBy = "System") : base(organizationId, createdBy)
        {
            if (carrierId == Guid.Empty)
                throw new DomainException("Carrier ID is required");
            if (string.IsNullOrWhiteSpace(accountNumber))
                throw new DomainException("Account number is required");
            if (string.IsNullOrWhiteSpace(accountName))
                throw new DomainException("Account name is required");

            CarrierId = carrierId;
            AccountNumber = accountNumber.Trim();
            AccountName = accountName.Trim();
            BillingContact = billingContact ?? throw new DomainException("Billing contact is required");
            BillingAddress = billingAddress ?? throw new DomainException("Billing address is required");
            IsActive = true;
            IsDefault = false;
            Environment = "Production";
            IsTestMode = false;
            AutoPay = false;
            ApiCallsToday = 0;
            ApiCallsThisMonth = 0;
            ActivationDate = DateTime.UtcNow;
        }

        public void UpdateBasicInfo(
            string accountName,
            string? description,
            ContactInfo billingContact,
            Address billingAddress,
            ContactInfo? technicalContact,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(accountName))
                throw new DomainException("Account name is required");

            AccountName = accountName.Trim();
            Description = description?.Trim();
            BillingContact = billingContact ?? throw new DomainException("Billing contact is required");
            BillingAddress = billingAddress ?? throw new DomainException("Billing address is required");
            TechnicalContact = technicalContact;

            Update(updatedBy);
        }

        public void UpdateCredentials(
            string? username,
            string? passwordHash,
            string? apiKey,
            string? apiSecret,
            string? accessToken,
            string? refreshToken,
            DateTime? tokenExpiryDate,
            string? authenticationMethod,
            string? authenticationUrl,
            string updatedBy)
        {
            Username = username?.Trim();
            PasswordHash = passwordHash;
            ApiKey = apiKey?.Trim();
            ApiSecret = apiSecret?.Trim();
            AccessToken = accessToken?.Trim();
            RefreshToken = refreshToken?.Trim();
            TokenExpiryDate = tokenExpiryDate;
            AuthenticationMethod = authenticationMethod?.Trim();
            AuthenticationUrl = authenticationUrl?.Trim();

            Update(updatedBy);
        }

        public void UpdateCarrierSpecificInfo(
            string? customerNumber,
            string? meterNumber,
            string? licenseKey,
            string? developerKey,
            string updatedBy)
        {
            CustomerNumber = customerNumber?.Trim();
            MeterNumber = meterNumber?.Trim();
            LicenseKey = licenseKey?.Trim();
            DeveloperKey = developerKey?.Trim();

            Update(updatedBy);
        }

        public void UpdateEnvironmentSettings(
            string? environment,
            string? baseUrl,
            string? testBaseUrl,
            bool isTestMode,
            string updatedBy)
        {
            Environment = environment?.Trim();
            BaseUrl = baseUrl?.Trim();
            TestBaseUrl = testBaseUrl?.Trim();
            IsTestMode = isTestMode;

            Update(updatedBy);
        }

        public void UpdateFinancialInfo(
            decimal? creditLimit,
            Currency? creditCurrency,
            decimal? currentBalance,
            string? paymentTerms,
            string? billingCycle,
            DateTime? lastBillingDate,
            DateTime? nextBillingDate,
            bool autoPay,
            string? paymentMethod,
            string updatedBy)
        {
            if (creditLimit.HasValue && creditLimit.Value < 0)
                throw new DomainException("Credit limit cannot be negative");
            if (currentBalance.HasValue && currentBalance.Value < 0)
                throw new DomainException("Current balance cannot be negative");

            CreditLimit = creditLimit;
            CreditCurrency = creditCurrency;
            CurrentBalance = currentBalance;
            PaymentTerms = paymentTerms?.Trim();
            BillingCycle = billingCycle?.Trim();
            LastBillingDate = lastBillingDate;
            NextBillingDate = nextBillingDate;
            AutoPay = autoPay;
            PaymentMethod = paymentMethod?.Trim();

            Update(updatedBy);
        }

        public void UpdateContractInfo(
            string? contractNumber,
            DateTime? contractStartDate,
            DateTime? contractEndDate,
            string? serviceLevel,
            string? discountCode,
            decimal? discountPercentage,
            string? specialRates,
            string updatedBy)
        {
            if (contractStartDate.HasValue && contractEndDate.HasValue && contractStartDate.Value >= contractEndDate.Value)
                throw new DomainException("Contract start date must be before end date");
            if (discountPercentage.HasValue && (discountPercentage.Value < 0 || discountPercentage.Value > 100))
                throw new DomainException("Discount percentage must be between 0 and 100");

            ContractNumber = contractNumber?.Trim();
            ContractStartDate = contractStartDate;
            ContractEndDate = contractEndDate;
            ServiceLevel = serviceLevel?.Trim();
            DiscountCode = discountCode?.Trim();
            DiscountPercentage = discountPercentage;
            SpecialRates = specialRates?.Trim();

            Update(updatedBy);
        }

        public void UpdateServiceRestrictions(
            string? restrictedServices,
            string? allowedServices,
            int? maxShipmentsPerDay,
            int? maxShipmentsPerMonth,
            decimal? maxShipmentValue,
            Currency? maxShipmentValueCurrency,
            string updatedBy)
        {
            if (maxShipmentsPerDay.HasValue && maxShipmentsPerDay.Value < 0)
                throw new DomainException("Max shipments per day cannot be negative");
            if (maxShipmentsPerMonth.HasValue && maxShipmentsPerMonth.Value < 0)
                throw new DomainException("Max shipments per month cannot be negative");
            if (maxShipmentValue.HasValue && maxShipmentValue.Value < 0)
                throw new DomainException("Max shipment value cannot be negative");

            RestrictedServices = restrictedServices?.Trim();
            AllowedServices = allowedServices?.Trim();
            MaxShipmentsPerDay = maxShipmentsPerDay;
            MaxShipmentsPerMonth = maxShipmentsPerMonth;
            MaxShipmentValue = maxShipmentValue;
            MaxShipmentValueCurrency = maxShipmentValueCurrency;

            Update(updatedBy);
        }

        public void UpdatePreferences(
            string? timeZone,
            string? preferredLanguage,
            string? notificationPreferences,
            string? reportingPreferences,
            string updatedBy)
        {
            TimeZone = timeZone?.Trim();
            PreferredLanguage = preferredLanguage?.Trim();
            NotificationPreferences = notificationPreferences?.Trim();
            ReportingPreferences = reportingPreferences?.Trim();

            Update(updatedBy);
        }

        public void UpdateApiLimits(
            int? maxApiCallsPerDay,
            int? maxApiCallsPerMonth,
            string updatedBy)
        {
            if (maxApiCallsPerDay.HasValue && maxApiCallsPerDay.Value < 0)
                throw new DomainException("Max API calls per day cannot be negative");
            if (maxApiCallsPerMonth.HasValue && maxApiCallsPerMonth.Value < 0)
                throw new DomainException("Max API calls per month cannot be negative");

            MaxApiCallsPerDay = maxApiCallsPerDay;
            MaxApiCallsPerMonth = maxApiCallsPerMonth;

            Update(updatedBy);
        }

        public void UpdateMetadata(
            string? notes,
            string? internalReference,
            string? externalReference,
            string? tags,
            string updatedBy)
        {
            Notes = notes?.Trim();
            InternalReference = internalReference?.Trim();
            ExternalReference = externalReference?.Trim();
            Tags = tags?.Trim();

            Update(updatedBy);
        }

        public void SetAsDefault(string updatedBy)
        {
            IsDefault = true;
            Update(updatedBy);
        }

        public void RemoveAsDefault(string updatedBy)
        {
            IsDefault = false;
            Update(updatedBy);
        }

        public void Activate(string updatedBy)
        {
            IsActive = true;
            ActivationDate = DateTime.UtcNow;
            DeactivationDate = null;
            DeactivationReason = null;
            Update(updatedBy);
        }

        public void Deactivate(string reason, string updatedBy)
        {
            IsActive = false;
            DeactivationDate = DateTime.UtcNow;
            DeactivationReason = reason?.Trim();
            Update(updatedBy);
        }

        public void RecordLogin(string updatedBy)
        {
            LastLoginDate = DateTime.UtcNow;
            Update(updatedBy);
        }

        public void RecordApiCall(string updatedBy)
        {
            LastApiCall = DateTime.UtcNow;
            ApiCallsToday++;
            ApiCallsThisMonth++;
            Update(updatedBy);
        }

        public void ResetDailyApiCalls(string updatedBy)
        {
            ApiCallsToday = 0;
            Update(updatedBy);
        }

        public void ResetMonthlyApiCalls(string updatedBy)
        {
            ApiCallsThisMonth = 0;
            Update(updatedBy);
        }

        public bool IsTokenValid()
        {
            if (string.IsNullOrWhiteSpace(AccessToken))
                return false;

            if (TokenExpiryDate.HasValue && DateTime.UtcNow >= TokenExpiryDate.Value)
                return false;

            return true;
        }

        public bool IsContractValid()
        {
            if (ContractStartDate == null || ContractEndDate == null)
                return true; // No contract restrictions

            var now = DateTime.UtcNow;
            return now >= ContractStartDate && now <= ContractEndDate;
        }

        public bool CanMakeApiCall()
        {
            if (!IsActive)
                return false;

            if (MaxApiCallsPerDay.HasValue && ApiCallsToday >= MaxApiCallsPerDay.Value)
                return false;

            if (MaxApiCallsPerMonth.HasValue && ApiCallsThisMonth >= MaxApiCallsPerMonth.Value)
                return false;

            return true;
        }

        public bool HasCreditAvailable(decimal amount)
        {
            if (!CreditLimit.HasValue || !CurrentBalance.HasValue)
                return true; // No credit restrictions

            return AvailableCredit >= amount;
        }

        public string GetEffectiveBaseUrl()
        {
            return IsTestMode && !string.IsNullOrWhiteSpace(TestBaseUrl) ? TestBaseUrl : BaseUrl ?? string.Empty;
        }
    }
}
