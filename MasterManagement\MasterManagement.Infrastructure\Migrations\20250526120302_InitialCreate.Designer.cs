﻿// <auto-generated />
using System;
using MasterManagement.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace MasterManagement.Infrastructure.Migrations
{
    [DbContext(typeof(MasterManagementDbContext))]
    [Migration("20250526120302_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresExtension(modelBuilder, "postgis");
            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("MasterManagement.Domain.Entities.BusinessRules.ConditionalRule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Action")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("AverageExecutionTimeMs")
                        .HasColumnType("numeric");

                    b.Property<string>("Category")
                        .HasColumnType("text");

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("ContinueOnFailure")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("EffectiveFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ElseAction")
                        .HasColumnType("text");

                    b.Property<long>("ExecutionCount")
                        .HasColumnType("bigint");

                    b.Property<int>("ExecutionOrder")
                        .HasColumnType("integer");

                    b.Property<long>("FailureCount")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsCritical")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastExecuted")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<int>("MaxRetries")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<Guid>("RuleGroupId")
                        .HasColumnType("uuid");

                    b.Property<string>("RuleType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Schedule")
                        .HasColumnType("text");

                    b.Property<long>("SuccessCount")
                        .HasColumnType("bigint");

                    b.Property<string>("Tags")
                        .HasColumnType("text");

                    b.Property<int>("TimeoutSeconds")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Version")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RuleGroupId");

                    b.ToTable("ConditionalRules");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.BusinessRules.RuleEngine", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("AverageExecutionTimeMs")
                        .HasColumnType("numeric");

                    b.Property<int>("CacheExpiryMinutes")
                        .HasColumnType("integer");

                    b.Property<string>("Configuration")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("EnableCaching")
                        .HasColumnType("boolean");

                    b.Property<bool>("EnableLogging")
                        .HasColumnType("boolean");

                    b.Property<string>("ExecutionMode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FailedExecutions")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("RuleEngineType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("SuccessfulExecutions")
                        .HasColumnType("bigint");

                    b.Property<long>("TotalExecutions")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("RuleEngines");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.BusinessRules.RuleExecution", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ConditionalRuleId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<string>("ErrorStackTrace")
                        .HasColumnType("text");

                    b.Property<string>("ExecutionContext")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ExecutionEndTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ExecutionMode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("ExecutionStartTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("ExecutionTimeMs")
                        .HasColumnType("numeric");

                    b.Property<string>("InputData")
                        .HasColumnType("text");

                    b.Property<bool>("IsSuccessful")
                        .HasColumnType("boolean");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("OutputData")
                        .HasColumnType("text");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("Result")
                        .HasColumnType("text");

                    b.Property<Guid>("RuleEngineId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RuleGroupId")
                        .HasColumnType("uuid");

                    b.Property<string>("SessionId")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("RuleEngineId");

                    b.ToTable("RuleExecution");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.BusinessRules.RuleGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("ExecutionMode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("ExecutionOrder")
                        .HasColumnType("integer");

                    b.Property<string>("GroupType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RuleEngineId")
                        .HasColumnType("uuid");

                    b.Property<bool>("StopOnFirstFailure")
                        .HasColumnType("boolean");

                    b.Property<bool>("StopOnFirstSuccess")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("RuleEngineId");

                    b.ToTable("RuleGroups");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Calendar.BusinessDay", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("CustomerServiceAvailable")
                        .HasColumnType("boolean");

                    b.Property<string>("CustomerServiceHours")
                        .HasColumnType("text");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DayOfWeek")
                        .HasColumnType("integer");

                    b.Property<bool>("DeliveryAvailable")
                        .HasColumnType("boolean");

                    b.Property<string>("DeliveryHours")
                        .HasColumnType("text");

                    b.Property<bool>("HasSpecialArrangements")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("HolidayId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsBusinessDay")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsHoliday")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsOverride")
                        .HasColumnType("boolean");

                    b.Property<bool>("OfficeOpen")
                        .HasColumnType("boolean");

                    b.Property<string>("OperatingHours")
                        .HasColumnType("text");

                    b.Property<Guid?>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("OverrideBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("OverrideDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("OverrideReason")
                        .HasColumnType("text");

                    b.Property<bool>("PickupAvailable")
                        .HasColumnType("boolean");

                    b.Property<string>("PickupHours")
                        .HasColumnType("text");

                    b.Property<decimal?>("ServiceLevelAdjustment")
                        .HasColumnType("numeric");

                    b.Property<string>("SpecialArrangements")
                        .HasColumnType("text");

                    b.Property<Guid?>("StateId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("BusinessDays");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Calendar.Holiday", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AffectsCustomerService")
                        .HasColumnType("boolean");

                    b.Property<bool>("AffectsDelivery")
                        .HasColumnType("boolean");

                    b.Property<bool>("AffectsOfficeOperations")
                        .HasColumnType("boolean");

                    b.Property<bool>("AffectsPickup")
                        .HasColumnType("boolean");

                    b.Property<string>("AlternativeArrangements")
                        .HasColumnType("text");

                    b.Property<DateTime?>("AlternativeServiceDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CulturalSignificance")
                        .HasColumnType("text");

                    b.Property<DateTime>("Date")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("HolidayType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRecurring")
                        .HasColumnType("boolean");

                    b.Property<string>("LocalCustoms")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("RecurrencePattern")
                        .HasColumnType("text");

                    b.Property<string>("ReligiousAffiliation")
                        .HasColumnType("text");

                    b.Property<string>("SpecialInstructions")
                        .HasColumnType("text");

                    b.Property<Guid?>("StateId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("Holidays");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.DataQuality.DataQualityMetric", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AlertOnThresholdBreach")
                        .HasColumnType("boolean");

                    b.Property<string>("AlertRecipients")
                        .HasColumnType("text");

                    b.Property<bool>("AutoMeasurement")
                        .HasColumnType("boolean");

                    b.Property<string>("CommonIssues")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CriticalIssueCount")
                        .HasColumnType("integer");

                    b.Property<decimal>("CurrentScore")
                        .HasColumnType("numeric");

                    b.Property<string>("CurrentStatus")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long>("FailingRecords")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("LastMeasured")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MeasurementCriteria")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MeasurementSchedule")
                        .HasColumnType("text");

                    b.Property<string>("MetricType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<long>("PassingRecords")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("PreviousScore")
                        .HasColumnType("numeric");

                    b.Property<string>("RecommendedActions")
                        .HasColumnType("text");

                    b.Property<decimal?>("ScoreTrend")
                        .HasColumnType("numeric");

                    b.Property<decimal>("ThresholdAcceptable")
                        .HasColumnType("numeric");

                    b.Property<decimal>("ThresholdGood")
                        .HasColumnType("numeric");

                    b.Property<long>("TotalRecordsEvaluated")
                        .HasColumnType("bigint");

                    b.Property<string>("TrendDirection")
                        .HasColumnType("text");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("WarningIssueCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("DataQualityMetrics");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.DataQuality.DuplicateDetection", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<bool>("AutoMergeEnabled")
                        .HasColumnType("boolean");

                    b.Property<decimal>("AutoMergeThreshold")
                        .HasColumnType("numeric");

                    b.Property<int>("AutoMergedCount")
                        .HasColumnType("integer");

                    b.Property<decimal>("AverageProcessingTimeMs")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("CrossOrganizationCheck")
                        .HasColumnType("boolean");

                    b.Property<string>("DetectionRuleName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("DuplicateGroupsFound")
                        .HasColumnType("integer");

                    b.Property<string>("EntityType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("FieldWeights")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastRunDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastRunErrors")
                        .HasColumnType("text");

                    b.Property<string>("LastRunStatus")
                        .HasColumnType("text");

                    b.Property<int>("ManualReviewRequired")
                        .HasColumnType("integer");

                    b.Property<string>("MatchingAlgorithm")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("MatchingCriteria")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("NextScheduledRun")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("RunSchedule")
                        .HasColumnType("text");

                    b.Property<bool>("ScheduledRun")
                        .HasColumnType("boolean");

                    b.Property<decimal>("SimilarityThreshold")
                        .HasColumnType("numeric");

                    b.Property<int>("TotalDuplicatesFound")
                        .HasColumnType("integer");

                    b.Property<int>("TotalRecordsScanned")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("DuplicateDetections");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.DataQuality.DuplicateGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DuplicateDetectionId")
                        .HasColumnType("uuid");

                    b.Property<string>("GroupKey")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RecommendedAction")
                        .HasColumnType("text");

                    b.Property<int>("RecordCount")
                        .HasColumnType("integer");

                    b.Property<string>("RecordIds")
                        .HasColumnType("text");

                    b.Property<string>("ReviewNotes")
                        .HasColumnType("text");

                    b.Property<string>("ReviewedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("ReviewedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SimilarityDetails")
                        .HasColumnType("text");

                    b.Property<decimal>("SimilarityScore")
                        .HasColumnType("numeric");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DuplicateDetectionId");

                    b.ToTable("DuplicateGroup");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.City", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("Population")
                        .HasColumnType("integer");

                    b.Property<Guid>("StateId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("StateId1")
                        .HasColumnType("uuid");

                    b.Property<string>("TimeZone")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("StateId");

                    b.HasIndex("StateId1");

                    b.ToTable("Cities", (string)null);
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Continent", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("Continents", (string)null);
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Country", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("character varying(2)");

                    b.Property<string>("Code3")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<Guid>("ContinentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("CurrencySymbol")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("NumericCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<string>("OfficialName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("PhonePrefix")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<Guid?>("RegionId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("RegionId1")
                        .HasColumnType("uuid");

                    b.Property<string>("TimeZone")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("Code3")
                        .IsUnique();

                    b.HasIndex("ContinentId");

                    b.HasIndex("RegionId");

                    b.HasIndex("RegionId1");

                    b.ToTable("Countries", (string)null);
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.PostalCode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AreaName")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("CityId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CityId1")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("PostalCodeType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("SecondaryCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid>("StateId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("ZoneId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CityId");

                    b.HasIndex("CityId1");

                    b.HasIndex("CountryId");

                    b.HasIndex("StateId");

                    b.HasIndex("ZoneId");

                    b.ToTable("PostalCodes", (string)null);
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Region", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<Guid>("ContinentId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ContinentId1")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ContinentId");

                    b.HasIndex("ContinentId1");

                    b.HasIndex("Code", "ContinentId")
                        .IsUnique();

                    b.ToTable("Regions", (string)null);
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.State", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CountryId1")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CountryId");

                    b.HasIndex("CountryId1");

                    b.HasIndex("Code", "CountryId")
                        .IsUnique();

                    b.ToTable("States", (string)null);
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Zone", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid>("CityId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CityId1")
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid>("CountryId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("StateId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ZoneType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("CityId");

                    b.HasIndex("CityId1");

                    b.HasIndex("CountryId");

                    b.HasIndex("StateId");

                    b.ToTable("Zones", (string)null);
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.ServiceLevelAgreement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CompensationPolicy")
                        .HasColumnType("text");

                    b.Property<string>("CoverageAreas")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DeliveryTimeHours")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EscalationProcedure")
                        .HasColumnType("text");

                    b.Property<bool>("IncludesHolidays")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int>("MaxDimensionsCm")
                        .HasColumnType("integer");

                    b.Property<int>("MaxVolumeM3")
                        .HasColumnType("integer");

                    b.Property<int>("MaxWeightKg")
                        .HasColumnType("integer");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("OnTimeDeliveryPercentage")
                        .HasColumnType("numeric");

                    b.Property<string>("OperatingDays")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("OperatingHours")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<decimal?>("PenaltyPercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal>("ServiceAvailabilityPercentage")
                        .HasColumnType("numeric");

                    b.Property<Guid>("ServiceTypeId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ServiceTypeId");

                    b.ToTable("ServiceLevelAgreements");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.ServiceRestriction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AllowedDays")
                        .HasColumnType("text");

                    b.Property<string>("AllowedZones")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<TimeSpan?>("EndTime")
                        .HasColumnType("interval");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<decimal?>("MaxHeight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxLength")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxVolume")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxWidth")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinInsuranceValue")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<bool>("RequiresIdVerification")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresInsurance")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresSignature")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresSpecialHandling")
                        .HasColumnType("boolean");

                    b.Property<string>("RestrictedDays")
                        .HasColumnType("text");

                    b.Property<string>("RestrictedZones")
                        .HasColumnType("text");

                    b.Property<string>("RestrictionRule")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("RestrictionType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("ServiceTypeId")
                        .HasColumnType("uuid");

                    b.Property<string>("SpecialHandlingInstructions")
                        .HasColumnType("text");

                    b.Property<TimeSpan?>("StartTime")
                        .HasColumnType("interval");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ServiceTypeId");

                    b.ToTable("ServiceRestriction");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.ServiceType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("BasePrice")
                        .HasColumnType("numeric");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("PricingModel")
                        .HasColumnType("text");

                    b.Property<int>("SortOrder")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("ServiceTypes");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.SlaPerformance", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal>("ActualOnTimePercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal>("AverageDeliveryTimeHours")
                        .HasColumnType("numeric");

                    b.Property<int>("ComplaintCount")
                        .HasColumnType("integer");

                    b.Property<string>("CorrectiveActions")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("EscalationCount")
                        .HasColumnType("integer");

                    b.Property<string>("Notes")
                        .HasColumnType("text");

                    b.Property<int>("OnTimeShipments")
                        .HasColumnType("integer");

                    b.Property<decimal?>("PenaltyAmount")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("PeriodEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("PeriodStart")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("ServiceAvailabilityPercentage")
                        .HasColumnType("numeric");

                    b.Property<Guid?>("ServiceLevelAgreementId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("SlaId")
                        .HasColumnType("uuid");

                    b.Property<bool>("SlaMetTarget")
                        .HasColumnType("boolean");

                    b.Property<decimal>("TargetOnTimePercentage")
                        .HasColumnType("numeric");

                    b.Property<int>("TotalShipments")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("ServiceLevelAgreementId");

                    b.ToTable("SlaPerformances");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.SlaZoneOverride", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<int?>("OverrideDeliveryTimeHours")
                        .HasColumnType("integer");

                    b.Property<decimal?>("OverrideOnTimePercentage")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("OverridePenaltyPercentage")
                        .HasColumnType("numeric");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid?>("ServiceLevelAgreementId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("SlaId")
                        .HasColumnType("uuid");

                    b.Property<string>("SpecialInstructions")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("ZoneId")
                        .HasColumnType("uuid");

                    b.Property<string>("ZoneName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ServiceLevelAgreementId");

                    b.ToTable("SlaZoneOverrides");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Transportation.VehicleCapacity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("CapacityType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<decimal>("MaxCapacity")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("OptimalCapacity")
                        .HasColumnType("numeric");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("VehicleTypeId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("VehicleTypeId");

                    b.ToTable("VehicleCapacities");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Transportation.VehicleType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("CostPerHour")
                        .HasColumnType("numeric");

                    b.Property<decimal>("CostPerKm")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("EmissionStandard")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("FuelConsumptionPer100Km")
                        .HasColumnType("numeric");

                    b.Property<string>("FuelType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsEcoFriendly")
                        .HasColumnType("boolean");

                    b.Property<int>("MaxDriverHours")
                        .HasColumnType("integer");

                    b.Property<decimal>("MaxHeightCm")
                        .HasColumnType("numeric");

                    b.Property<decimal>("MaxLengthCm")
                        .HasColumnType("numeric");

                    b.Property<decimal>("MaxVolumeM3")
                        .HasColumnType("numeric");

                    b.Property<decimal>("MaxWeightKg")
                        .HasColumnType("numeric");

                    b.Property<decimal>("MaxWidthCm")
                        .HasColumnType("numeric");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<bool>("RequiresSpecialLicense")
                        .HasColumnType("boolean");

                    b.Property<string>("RestrictedAreas")
                        .HasColumnType("text");

                    b.Property<string>("SpecialLicenseType")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("VehicleTypes");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Transportation.VehicleZoneAccess", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AccessType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("AdditionalCostPerKm")
                        .HasColumnType("numeric");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EffectiveFrom")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("EffectiveTo")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("Reason")
                        .HasColumnType("text");

                    b.Property<string>("SpecialRequirements")
                        .HasColumnType("text");

                    b.Property<string>("TimeRestrictions")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("VehicleTypeId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ZoneId")
                        .HasColumnType("uuid");

                    b.Property<string>("ZoneName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("VehicleTypeId");

                    b.ToTable("VehicleZoneAccesses");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.BusinessRules.ConditionalRule", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.BusinessRules.RuleGroup", null)
                        .WithMany("Rules")
                        .HasForeignKey("RuleGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.BusinessRules.RuleExecution", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.BusinessRules.RuleEngine", null)
                        .WithMany("ExecutionHistory")
                        .HasForeignKey("RuleEngineId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.BusinessRules.RuleGroup", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.BusinessRules.RuleEngine", null)
                        .WithMany("RuleGroups")
                        .HasForeignKey("RuleEngineId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.DataQuality.DuplicateGroup", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.DataQuality.DuplicateDetection", null)
                        .WithMany("DuplicateGroups")
                        .HasForeignKey("DuplicateDetectionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.City", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Country", null)
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.State", null)
                        .WithMany()
                        .HasForeignKey("StateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.State", null)
                        .WithMany("Cities")
                        .HasForeignKey("StateId1");

                    b.OwnsOne("MasterManagement.Domain.ValueObjects.Coordinates", "Coordinates", b1 =>
                        {
                            b1.Property<Guid>("CityId")
                                .HasColumnType("uuid");

                            b1.Property<double>("Latitude")
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("CityId");

                            b1.ToTable("Cities");

                            b1.WithOwner()
                                .HasForeignKey("CityId");
                        });

                    b.Navigation("Coordinates");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Country", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Continent", null)
                        .WithMany()
                        .HasForeignKey("ContinentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Region", null)
                        .WithMany()
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Region", null)
                        .WithMany("Countries")
                        .HasForeignKey("RegionId1");

                    b.OwnsOne("MasterManagement.Domain.ValueObjects.Coordinates", "Coordinates", b1 =>
                        {
                            b1.Property<Guid>("CountryId")
                                .HasColumnType("uuid");

                            b1.Property<double>("Latitude")
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("CountryId");

                            b1.ToTable("Countries");

                            b1.WithOwner()
                                .HasForeignKey("CountryId");
                        });

                    b.Navigation("Coordinates");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.PostalCode", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.Geographic.City", null)
                        .WithMany()
                        .HasForeignKey("CityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.City", null)
                        .WithMany("PostalCodes")
                        .HasForeignKey("CityId1");

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Country", null)
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.State", null)
                        .WithMany()
                        .HasForeignKey("StateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Zone", null)
                        .WithMany("PostalCodes")
                        .HasForeignKey("ZoneId");

                    b.OwnsOne("MasterManagement.Domain.ValueObjects.Coordinates", "Coordinates", b1 =>
                        {
                            b1.Property<Guid>("PostalCodeId")
                                .HasColumnType("uuid");

                            b1.Property<double>("Latitude")
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("PostalCodeId");

                            b1.ToTable("PostalCodes");

                            b1.WithOwner()
                                .HasForeignKey("PostalCodeId");
                        });

                    b.Navigation("Coordinates");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Region", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Continent", null)
                        .WithMany()
                        .HasForeignKey("ContinentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Continent", null)
                        .WithMany("Regions")
                        .HasForeignKey("ContinentId1");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.State", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Country", null)
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Country", null)
                        .WithMany("States")
                        .HasForeignKey("CountryId1");

                    b.OwnsOne("MasterManagement.Domain.ValueObjects.Coordinates", "Coordinates", b1 =>
                        {
                            b1.Property<Guid>("StateId")
                                .HasColumnType("uuid");

                            b1.Property<double>("Latitude")
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("StateId");

                            b1.ToTable("States");

                            b1.WithOwner()
                                .HasForeignKey("StateId");
                        });

                    b.Navigation("Coordinates");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Zone", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.Geographic.City", null)
                        .WithMany()
                        .HasForeignKey("CityId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.City", null)
                        .WithMany("Zones")
                        .HasForeignKey("CityId1");

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.Country", null)
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("MasterManagement.Domain.Entities.Geographic.State", null)
                        .WithMany()
                        .HasForeignKey("StateId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.OwnsOne("MasterManagement.Domain.ValueObjects.Coordinates", "Coordinates", b1 =>
                        {
                            b1.Property<Guid>("ZoneId")
                                .HasColumnType("uuid");

                            b1.Property<double>("Latitude")
                                .HasColumnType("double precision")
                                .HasColumnName("Latitude");

                            b1.Property<double>("Longitude")
                                .HasColumnType("double precision")
                                .HasColumnName("Longitude");

                            b1.HasKey("ZoneId");

                            b1.ToTable("Zones");

                            b1.WithOwner()
                                .HasForeignKey("ZoneId");
                        });

                    b.Navigation("Coordinates");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.ServiceLevelAgreement", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.ServiceManagement.ServiceType", null)
                        .WithMany("SlaAgreements")
                        .HasForeignKey("ServiceTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.ServiceRestriction", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.ServiceManagement.ServiceType", null)
                        .WithMany("Restrictions")
                        .HasForeignKey("ServiceTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.SlaPerformance", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.ServiceManagement.ServiceLevelAgreement", null)
                        .WithMany("PerformanceHistory")
                        .HasForeignKey("ServiceLevelAgreementId");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.SlaZoneOverride", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.ServiceManagement.ServiceLevelAgreement", null)
                        .WithMany("ZoneOverrides")
                        .HasForeignKey("ServiceLevelAgreementId");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Transportation.VehicleCapacity", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.Transportation.VehicleType", null)
                        .WithMany("Capacities")
                        .HasForeignKey("VehicleTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Transportation.VehicleZoneAccess", b =>
                {
                    b.HasOne("MasterManagement.Domain.Entities.Transportation.VehicleType", null)
                        .WithMany("ZoneAccess")
                        .HasForeignKey("VehicleTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.BusinessRules.RuleEngine", b =>
                {
                    b.Navigation("ExecutionHistory");

                    b.Navigation("RuleGroups");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.BusinessRules.RuleGroup", b =>
                {
                    b.Navigation("Rules");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.DataQuality.DuplicateDetection", b =>
                {
                    b.Navigation("DuplicateGroups");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.City", b =>
                {
                    b.Navigation("PostalCodes");

                    b.Navigation("Zones");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Continent", b =>
                {
                    b.Navigation("Regions");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Country", b =>
                {
                    b.Navigation("States");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Region", b =>
                {
                    b.Navigation("Countries");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.State", b =>
                {
                    b.Navigation("Cities");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Geographic.Zone", b =>
                {
                    b.Navigation("PostalCodes");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.ServiceLevelAgreement", b =>
                {
                    b.Navigation("PerformanceHistory");

                    b.Navigation("ZoneOverrides");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.ServiceManagement.ServiceType", b =>
                {
                    b.Navigation("Restrictions");

                    b.Navigation("SlaAgreements");
                });

            modelBuilder.Entity("MasterManagement.Domain.Entities.Transportation.VehicleType", b =>
                {
                    b.Navigation("Capacities");

                    b.Navigation("ZoneAccess");
                });
#pragma warning restore 612, 618
        }
    }
}
