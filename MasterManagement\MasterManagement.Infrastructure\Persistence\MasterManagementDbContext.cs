using Microsoft.EntityFrameworkCore;
using MasterManagement.Domain.Entities.Geographic;
using MasterManagement.Domain.Entities.ServiceManagement;
using MasterManagement.Domain.Entities.Transportation;
using MasterManagement.Domain.Entities.BusinessRules;
using MasterManagement.Domain.Entities.Calendar;
using MasterManagement.Domain.Entities.DataQuality;
using MasterManagement.Domain.Common.Events;


namespace MasterManagement.Infrastructure.Persistence
{
    public class MasterManagementDbContext : DbContext
    {
        // Geographic entities
        public DbSet<Continent> Continents { get; set; }
        public DbSet<Region> Regions { get; set; }
        public DbSet<Country> Countries { get; set; }
        public DbSet<State> States { get; set; }
        public DbSet<City> Cities { get; set; }
        public DbSet<Zone> Zones { get; set; }
        public DbSet<PostalCode> PostalCodes { get; set; }

        // Service Management entities
        public DbSet<ServiceType> ServiceTypes { get; set; }
        public DbSet<ServiceLevelAgreement> ServiceLevelAgreements { get; set; }
        public DbSet<SlaZoneOverride> SlaZoneOverrides { get; set; }
        public DbSet<SlaPerformance> SlaPerformances { get; set; }

        // Transportation entities
        public DbSet<VehicleType> VehicleTypes { get; set; }
        public DbSet<VehicleCapacity> VehicleCapacities { get; set; }
        public DbSet<VehicleZoneAccess> VehicleZoneAccesses { get; set; }

        // Business Rules entities
        public DbSet<RuleEngine> RuleEngines { get; set; }
        public DbSet<RuleGroup> RuleGroups { get; set; }
        public DbSet<ConditionalRule> ConditionalRules { get; set; }

        // Calendar entities
        public DbSet<Holiday> Holidays { get; set; }
        public DbSet<BusinessDay> BusinessDays { get; set; }

        // Data Quality entities
        public DbSet<DataQualityMetric> DataQualityMetrics { get; set; }
        public DbSet<DuplicateDetection> DuplicateDetections { get; set; }

        public MasterManagementDbContext(DbContextOptions<MasterManagementDbContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Continent
            modelBuilder.Entity<Continent>(entity =>
            {
                entity.ToTable("Continents");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
                entity.Property(e => e.Code).HasMaxLength(10).IsRequired();
                entity.HasIndex(e => e.Code).IsUnique();
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.IsActive).IsRequired();
            });

            // Configure Region
            modelBuilder.Entity<Region>(entity =>
            {
                entity.ToTable("Regions");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
                entity.Property(e => e.Code).HasMaxLength(10).IsRequired();
                entity.HasIndex(e => new { e.Code, e.ContinentId }).IsUnique();
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.IsActive).IsRequired();
                entity.HasOne(r => r.Continent)
                    .WithMany(c => c.Regions)
                    .HasForeignKey(e => e.ContinentId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Country
            modelBuilder.Entity<Country>(entity =>
            {
                entity.ToTable("Countries");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
                entity.Property(e => e.Code).HasMaxLength(2).IsRequired();
                entity.HasIndex(e => e.Code).IsUnique();
                entity.Property(e => e.Code3).HasMaxLength(3).IsRequired();
                entity.HasIndex(e => e.Code3).IsUnique();
                entity.Property(e => e.NumericCode).HasMaxLength(3).IsRequired();
                entity.Property(e => e.OfficialName).HasMaxLength(200);
                entity.Property(e => e.Currency).HasMaxLength(10);
                entity.Property(e => e.CurrencySymbol).HasMaxLength(5);
                entity.Property(e => e.PhonePrefix).HasMaxLength(10);
                entity.Property(e => e.TimeZone).HasMaxLength(50);
                entity.Property(e => e.Language).HasMaxLength(50);
                entity.Property(e => e.IsActive).IsRequired();

                entity.OwnsOne(e => e.Coordinates, coords =>
                {
                    coords.Property(c => c.Latitude).HasColumnName("Latitude");
                    coords.Property(c => c.Longitude).HasColumnName("Longitude");
                });

                entity.HasOne(c => c.Continent)
                    .WithMany()
                    .HasForeignKey(e => e.ContinentId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(c => c.Region)
                    .WithMany(r => r.Countries)
                    .HasForeignKey(e => e.RegionId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure State
            modelBuilder.Entity<State>(entity =>
            {
                entity.ToTable("States");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
                entity.Property(e => e.Code).HasMaxLength(10).IsRequired();
                entity.HasIndex(e => new { e.Code, e.CountryId }).IsUnique();
                entity.Property(e => e.Type).HasMaxLength(50);
                entity.Property(e => e.IsActive).IsRequired();

                entity.OwnsOne(e => e.Coordinates, coords =>
                {
                    coords.Property(c => c.Latitude).HasColumnName("Latitude");
                    coords.Property(c => c.Longitude).HasColumnName("Longitude");
                });

                entity.HasOne(s => s.Country)
                    .WithMany(c => c.States)
                    .HasForeignKey(e => e.CountryId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure City
            modelBuilder.Entity<City>(entity =>
            {
                entity.ToTable("Cities");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
                entity.Property(e => e.Type).HasMaxLength(50);
                entity.Property(e => e.IsActive).IsRequired();
                entity.Property(e => e.Population);
                entity.Property(e => e.TimeZone).HasMaxLength(50);

                entity.OwnsOne(e => e.Coordinates, coords =>
                {
                    coords.Property(c => c.Latitude).HasColumnName("Latitude");
                    coords.Property(c => c.Longitude).HasColumnName("Longitude");
                });

                entity.HasOne(c => c.State)
                    .WithMany(s => s.Cities)
                    .HasForeignKey(e => e.StateId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(c => c.Country)
                    .WithMany()
                    .HasForeignKey(e => e.CountryId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure Zone
            modelBuilder.Entity<Zone>(entity =>
            {
                entity.ToTable("Zones");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).HasMaxLength(100).IsRequired();
                entity.Property(e => e.Code).HasMaxLength(20).IsRequired();
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.Property(e => e.ZoneType).HasMaxLength(50);
                entity.Property(e => e.IsActive).IsRequired();
                entity.Property(e => e.OrganizationId);

                entity.OwnsOne(e => e.Coordinates, coords =>
                {
                    coords.Property(c => c.Latitude).HasColumnName("Latitude");
                    coords.Property(c => c.Longitude).HasColumnName("Longitude");
                });

                entity.HasOne(z => z.City)
                    .WithMany(c => c.Zones)
                    .HasForeignKey(e => e.CityId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(z => z.State)
                    .WithMany()
                    .HasForeignKey(e => e.StateId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(z => z.Country)
                    .WithMany()
                    .HasForeignKey(e => e.CountryId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // Configure PostalCode
            modelBuilder.Entity<PostalCode>(entity =>
            {
                entity.ToTable("PostalCodes");
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Code).HasMaxLength(20).IsRequired();
                entity.Property(e => e.SecondaryCode).HasMaxLength(20);
                entity.Property(e => e.AreaName).HasMaxLength(100);
                entity.Property(e => e.PostalCodeType).HasMaxLength(50);
                entity.Property(e => e.IsActive).IsRequired();
                entity.Property(e => e.ZoneId);
                entity.Property(e => e.OrganizationId);

                entity.OwnsOne(e => e.Coordinates, coords =>
                {
                    coords.Property(c => c.Latitude).HasColumnName("Latitude");
                    coords.Property(c => c.Longitude).HasColumnName("Longitude");
                });

                entity.HasOne(p => p.City)
                    .WithMany(c => c.PostalCodes)
                    .HasForeignKey(e => e.CityId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.State)
                    .WithMany()
                    .HasForeignKey(e => e.StateId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Country)
                    .WithMany()
                    .HasForeignKey(e => e.CountryId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Zone)
                    .WithMany(z => z.PostalCodes)
                    .HasForeignKey(e => e.ZoneId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Ignore domain events as they are not persisted to the database
            modelBuilder.Ignore<DomainEvent>();
        }
    }
}
