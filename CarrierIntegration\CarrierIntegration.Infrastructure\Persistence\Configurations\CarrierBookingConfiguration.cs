using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CarrierIntegration.Infrastructure.Persistence.Configurations
{
    public class CarrierBookingConfiguration : IEntityTypeConfiguration<CarrierBooking>
    {
        public void Configure(EntityTypeBuilder<CarrierBooking> builder)
        {
            builder.ToTable("CarrierBookings");

            builder.HasKey(cb => cb.Id);

            builder.Property(cb => cb.Id)
                .ValueGeneratedNever();

            builder.Property(cb => cb.CarrierId)
                .IsRequired();

            builder.Property(cb => cb.BookingReference)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cb => cb.CarrierBookingReference)
                .HasMaxLength(100);

            builder.Property(cb => cb.Status)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(cb => cb.StatusMessage)
                .HasMaxLength(500);

            builder.Property(cb => cb.PickupInstructions)
                .HasMaxLength(1000);

            builder.Property(cb => cb.DeliveryInstructions)
                .HasMaxLength(1000);

            builder.Property(cb => cb.PackageDescription)
                .HasMaxLength(500);

            builder.Property(cb => cb.CommodityCode)
                .HasMaxLength(50);

            builder.Property(cb => cb.HazmatClass)
                .HasMaxLength(50);

            builder.Property(cb => cb.CODPaymentMethod)
                .HasMaxLength(50);

            builder.Property(cb => cb.SpecialServices)
                .HasMaxLength(1000);

            builder.Property(cb => cb.AccessorialServices)
                .HasMaxLength(1000);

            builder.Property(cb => cb.CostBreakdown)
                .HasMaxLength(2000);

            builder.Property(cb => cb.CarrierServiceCode)
                .HasMaxLength(50);

            builder.Property(cb => cb.CarrierServiceName)
                .HasMaxLength(200);

            builder.Property(cb => cb.TrackingNumber)
                .HasMaxLength(100);

            builder.Property(cb => cb.LabelUrl)
                .HasMaxLength(500);

            builder.Property(cb => cb.DocumentsUrl)
                .HasMaxLength(500);

            builder.Property(cb => cb.ManifestNumber)
                .HasMaxLength(100);

            builder.Property(cb => cb.BillOfLadingNumber)
                .HasMaxLength(100);

            builder.Property(cb => cb.ProNumber)
                .HasMaxLength(100);

            builder.Property(cb => cb.EquipmentType)
                .HasMaxLength(100);

            builder.Property(cb => cb.EquipmentNumber)
                .HasMaxLength(100);

            builder.Property(cb => cb.DriverName)
                .HasMaxLength(200);

            builder.Property(cb => cb.DriverPhone)
                .HasMaxLength(50);

            builder.Property(cb => cb.VehicleInfo)
                .HasMaxLength(500);

            builder.Property(cb => cb.CarrierNotes)
                .HasMaxLength(2000);

            builder.Property(cb => cb.InternalNotes)
                .HasMaxLength(2000);

            builder.Property(cb => cb.CustomerReference)
                .HasMaxLength(100);

            builder.Property(cb => cb.ShipperReference)
                .HasMaxLength(100);

            builder.Property(cb => cb.PoNumber)
                .HasMaxLength(100);

            builder.Property(cb => cb.InvoiceNumber)
                .HasMaxLength(100);

            builder.Property(cb => cb.CancellationReason)
                .HasMaxLength(500);

            builder.Property(cb => cb.CancelledBy)
                .HasMaxLength(100);

            builder.Property(cb => cb.CompletionNotes)
                .HasMaxLength(1000);

            builder.Property(cb => cb.ApiRequestData)
                .HasColumnType("text");

            builder.Property(cb => cb.ApiResponseData)
                .HasColumnType("text");

            builder.Property(cb => cb.LastError)
                .HasMaxLength(2000);

            builder.Property(cb => cb.OrganizationId)
                .IsRequired();

            builder.Property(cb => cb.CreatedAt)
                .IsRequired();

            builder.Property(cb => cb.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cb => cb.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(cb => cb.DeletedBy)
                .HasMaxLength(100);

            // Configure value objects
            builder.OwnsOne(cb => cb.PickupAddress, address =>
            {
                address.Property(x => x.Street1)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("PickupAddressStreet1");

                address.Property(x => x.Street2)
                    .HasMaxLength(200)
                    .HasColumnName("PickupAddressStreet2");

                address.Property(x => x.City)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("PickupAddressCity");

                address.Property(x => x.State)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("PickupAddressState");

                address.Property(x => x.PostalCode)
                    .IsRequired()
                    .HasMaxLength(20)
                    .HasColumnName("PickupAddressPostalCode");

                address.Property(x => x.Country)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("PickupAddressCountry");

                address.Property(x => x.Latitude)
                    .HasPrecision(10, 7)
                    .HasColumnName("PickupAddressLatitude");

                address.Property(x => x.Longitude)
                    .HasPrecision(10, 7)
                    .HasColumnName("PickupAddressLongitude");
            });

            builder.OwnsOne(cb => cb.DeliveryAddress, address =>
            {
                address.Property(x => x.Street1)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("DeliveryAddressStreet1");

                address.Property(x => x.Street2)
                    .HasMaxLength(200)
                    .HasColumnName("DeliveryAddressStreet2");

                address.Property(x => x.City)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("DeliveryAddressCity");

                address.Property(x => x.State)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("DeliveryAddressState");

                address.Property(x => x.PostalCode)
                    .IsRequired()
                    .HasMaxLength(20)
                    .HasColumnName("DeliveryAddressPostalCode");

                address.Property(x => x.Country)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("DeliveryAddressCountry");

                address.Property(x => x.Latitude)
                    .HasPrecision(10, 7)
                    .HasColumnName("DeliveryAddressLatitude");

                address.Property(x => x.Longitude)
                    .HasPrecision(10, 7)
                    .HasColumnName("DeliveryAddressLongitude");
            });

            builder.OwnsOne(cb => cb.PickupContact, contact =>
            {
                contact.Property(x => x.Name)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("PickupContactName");

                contact.Property(x => x.Email)
                    .HasMaxLength(200)
                    .HasColumnName("PickupContactEmail");

                contact.Property(x => x.Phone)
                    .HasMaxLength(50)
                    .HasColumnName("PickupContactPhone");

                contact.Property(x => x.Title)
                    .HasMaxLength(100)
                    .HasColumnName("PickupContactTitle");

                contact.Property(x => x.Department)
                    .HasMaxLength(100)
                    .HasColumnName("PickupContactDepartment");
            });

            builder.OwnsOne(cb => cb.DeliveryContact, contact =>
            {
                contact.Property(x => x.Name)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("DeliveryContactName");

                contact.Property(x => x.Email)
                    .HasMaxLength(200)
                    .HasColumnName("DeliveryContactEmail");

                contact.Property(x => x.Phone)
                    .HasMaxLength(50)
                    .HasColumnName("DeliveryContactPhone");

                contact.Property(x => x.Title)
                    .HasMaxLength(100)
                    .HasColumnName("DeliveryContactTitle");

                contact.Property(x => x.Department)
                    .HasMaxLength(100)
                    .HasColumnName("DeliveryContactDepartment");
            });

            builder.OwnsOne(cb => cb.TotalWeight, weight =>
            {
                weight.Property(w => w.Value)
                    .IsRequired()
                    .HasPrecision(18, 3)
                    .HasColumnName("TotalWeightValue");

                weight.Property(w => w.Unit)
                    .IsRequired()
                    .HasMaxLength(10)
                    .HasColumnName("TotalWeightUnit");
            });

            builder.OwnsOne(cb => cb.TotalDimensions, dimensions =>
            {
                dimensions.Property(d => d.Length)
                    .HasPrecision(18, 2)
                    .HasColumnName("TotalDimensionsLength");

                dimensions.Property(d => d.Width)
                    .HasPrecision(18, 2)
                    .HasColumnName("TotalDimensionsWidth");

                dimensions.Property(d => d.Height)
                    .HasPrecision(18, 2)
                    .HasColumnName("TotalDimensionsHeight");

                dimensions.Property(d => d.Unit)
                    .HasMaxLength(10)
                    .HasColumnName("TotalDimensionsUnit");
            });

            builder.OwnsOne(cb => cb.DeclaredValue, money =>
            {
                money.Property(m => m.Amount)
                    .HasPrecision(18, 2)
                    .HasColumnName("DeclaredValueAmount");

                money.Property(m => m.Currency)
                    .HasConversion<int>()
                    .HasColumnName("DeclaredValueCurrency");
            });

            builder.OwnsOne(cb => cb.CODAmount, money =>
            {
                money.Property(m => m.Amount)
                    .HasPrecision(18, 2)
                    .HasColumnName("CODAmount");

                money.Property(m => m.Currency)
                    .HasConversion<int>()
                    .HasColumnName("CODCurrency");
            });

            builder.OwnsOne(cb => cb.EstimatedCost, money =>
            {
                money.Property(m => m.Amount)
                    .HasPrecision(18, 2)
                    .HasColumnName("EstimatedCostAmount");

                money.Property(m => m.Currency)
                    .HasConversion<int>()
                    .HasColumnName("EstimatedCostCurrency");
            });

            builder.OwnsOne(cb => cb.ActualCost, money =>
            {
                money.Property(m => m.Amount)
                    .HasPrecision(18, 2)
                    .HasColumnName("ActualCostAmount");

                money.Property(m => m.Currency)
                    .HasConversion<int>()
                    .HasColumnName("ActualCostCurrency");
            });

            // Configure indexes
            builder.HasIndex(cb => new { cb.OrganizationId, cb.BookingReference })
                .IsUnique()
                .HasDatabaseName("IX_CarrierBookings_OrganizationId_BookingReference");

            builder.HasIndex(cb => cb.CarrierId)
                .HasDatabaseName("IX_CarrierBookings_CarrierId");

            builder.HasIndex(cb => cb.Status)
                .HasDatabaseName("IX_CarrierBookings_Status");

            builder.HasIndex(cb => cb.BookingDate)
                .HasDatabaseName("IX_CarrierBookings_BookingDate");

            builder.HasIndex(cb => cb.TrackingNumber)
                .HasDatabaseName("IX_CarrierBookings_TrackingNumber");

            builder.HasIndex(cb => cb.OrderId)
                .HasDatabaseName("IX_CarrierBookings_OrderId");

            builder.HasIndex(cb => cb.ShipmentId)
                .HasDatabaseName("IX_CarrierBookings_ShipmentId");

            builder.HasIndex(cb => cb.CreatedAt)
                .HasDatabaseName("IX_CarrierBookings_CreatedAt");

            // Ignore domain events for EF Core
            builder.Ignore(cb => cb.DomainEvents);
        }
    }
}
