using System;
using System.Collections.Generic;
using System.Linq;
using MasterManagement.Domain.Common;
using MasterManagement.Domain.Exceptions;
using MasterManagement.Domain.Events.Geographic;

namespace MasterManagement.Domain.Entities.Geographic
{
    public class Region : AggregateRoot
    {
        public string Name { get; private set; }
        public string Code { get; private set; }
        public string Description { get; private set; }
        public Guid ContinentId { get; private set; }
        public bool IsActive { get; private set; }
        
        // Navigation properties
        public Continent Continent { get; private set; }
        
        private readonly List<Country> _countries = new();
        public IReadOnlyCollection<Country> Countries => _countries.AsReadOnly();

        private Region() { }

        public Region(string name, string code, Guid continentId, string description = "")
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Region name cannot be empty");

            if (string.IsNullOrWhiteSpace(code))
                throw new DomainException("Region code cannot be empty");

            if (continentId == Guid.Empty)
                throw new DomainException("Continent ID cannot be empty");

            Name = name;
            Code = code.ToUpperInvariant();
            ContinentId = continentId;
            Description = description ?? string.Empty;
            IsActive = true;

            AddDomainEvent(new RegionCreatedEvent(Id, Name, Code, ContinentId));
        }

        public void UpdateDetails(string name, string description)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            Description = description ?? Description;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new RegionUpdatedEvent(Id, Name, Code));
        }

        public void AddCountry(Country country)
        {
            if (country == null)
                throw new DomainException("Country cannot be null");

            if (_countries.Any(c => c.Code == country.Code))
                throw new DomainException($"Country with code {country.Code} already exists");

            _countries.Add(country);
        }

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
