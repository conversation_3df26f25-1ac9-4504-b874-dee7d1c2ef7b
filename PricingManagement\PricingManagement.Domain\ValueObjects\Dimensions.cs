using PricingManagement.Domain.Exceptions;
using System;
using System.Collections.Generic;

namespace PricingManagement.Domain.ValueObjects
{
    public enum DimensionUnit
    {
        Inches = 1,
        Centimeters = 2,
        Feet = 3,
        Meters = 4
    }

    public class Dimensions : IEquatable<Dimensions>
    {
        public decimal Length { get; }
        public decimal Width { get; }
        public decimal Height { get; }
        public DimensionUnit Unit { get; }

        public Dimensions(decimal length, decimal width, decimal height, DimensionUnit unit)
        {
            if (length <= 0 || width <= 0 || height <= 0)
                throw new DomainException("Dimensions must be positive");

            Length = Math.Round(length, 2);
            Width = Math.Round(width, 2);
            Height = Math.Round(height, 2);
            Unit = unit;
        }

        public decimal Volume => Length * Width * Height;

        public decimal LongestSide => Math.Max(Math.Max(Length, Width), Height);

        public decimal Girth => 2 * (Width + Height);

        public decimal LengthPlusGirth => Length + Girth;

        public Dimensions ConvertTo(DimensionUnit targetUnit)
        {
            if (Unit == targetUnit)
                return this;

            var conversionFactor = GetConversionFactor(Unit, targetUnit);
            
            return new Dimensions(
                Length * conversionFactor,
                Width * conversionFactor,
                Height * conversionFactor,
                targetUnit);
        }

        private static decimal GetConversionFactor(DimensionUnit from, DimensionUnit to)
        {
            // Convert to inches first, then to target unit
            var toInches = from switch
            {
                DimensionUnit.Inches => 1m,
                DimensionUnit.Centimeters => 1m / 2.54m,
                DimensionUnit.Feet => 12m,
                DimensionUnit.Meters => 1m / 0.0254m,
                _ => throw new DomainException($"Unknown dimension unit: {from}")
            };

            var fromInches = to switch
            {
                DimensionUnit.Inches => 1m,
                DimensionUnit.Centimeters => 2.54m,
                DimensionUnit.Feet => 1m / 12m,
                DimensionUnit.Meters => 0.0254m,
                _ => throw new DomainException($"Unknown dimension unit: {to}")
            };

            return toInches * fromInches;
        }

        public Weight CalculateDimensionalWeight(decimal divisor, WeightUnit weightUnit)
        {
            var volumeInCubicInches = ConvertTo(DimensionUnit.Inches).Volume;
            var dimensionalWeight = volumeInCubicInches / divisor;
            
            return new Weight(dimensionalWeight, weightUnit);
        }

        public bool Equals(Dimensions? other)
        {
            if (other is null) return false;
            var otherConverted = other.ConvertTo(Unit);
            return Math.Abs(Length - otherConverted.Length) < 0.01m &&
                   Math.Abs(Width - otherConverted.Width) < 0.01m &&
                   Math.Abs(Height - otherConverted.Height) < 0.01m;
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as Dimensions);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Length, Width, Height, Unit);
        }

        public static bool operator ==(Dimensions? left, Dimensions? right)
        {
            return EqualityComparer<Dimensions>.Default.Equals(left, right);
        }

        public static bool operator !=(Dimensions? left, Dimensions? right)
        {
            return !(left == right);
        }

        public override string ToString()
        {
            return $"{Length} x {Width} x {Height} {Unit}";
        }
    }
}
