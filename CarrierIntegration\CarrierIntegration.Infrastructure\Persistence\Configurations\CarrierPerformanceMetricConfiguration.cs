using CarrierIntegration.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CarrierIntegration.Infrastructure.Persistence.Configurations
{
    public class CarrierPerformanceMetricConfiguration : IEntityTypeConfiguration<CarrierPerformanceMetric>
    {
        public void Configure(EntityTypeBuilder<CarrierPerformanceMetric> builder)
        {
            builder.ToTable("CarrierPerformanceMetrics");

            builder.HasKey(cpm => cpm.Id);

            builder.Property(cpm => cpm.Id)
                .ValueGeneratedNever();

            builder.Property(cpm => cpm.CarrierId)
                .IsRequired();

            builder.Property(cpm => cpm.PeriodStart)
                .IsRequired();

            builder.Property(cpm => cpm.PeriodEnd)
                .IsRequired();

            builder.Property(cpm => cpm.MetricType)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cpm => cpm.Value)
                .IsRequired()
                .HasPrecision(18, 4);

            builder.Property(cpm => cpm.Unit)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(cpm => cpm.AverageTransitTime)
                .HasPrecision(18, 2);

            builder.Property(cpm => cpm.OnTimeDeliveryRate)
                .HasPrecision(5, 2);

            builder.Property(cpm => cpm.ExceptionRate)
                .HasPrecision(5, 2);

            builder.Property(cpm => cpm.DamageClaimRate)
                .HasPrecision(5, 2);

            builder.Property(cpm => cpm.CostPerformanceIndex)
                .HasPrecision(18, 4);

            builder.Property(cpm => cpm.CustomerSatisfactionScore)
                .HasPrecision(3, 1);

            builder.Property(cpm => cpm.Notes)
                .HasMaxLength(2000);

            builder.Property(cpm => cpm.CalculationMethod)
                .HasMaxLength(500);

            builder.Property(cpm => cpm.CalculatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cpm => cpm.OrganizationId)
                .IsRequired();

            builder.Property(cpm => cpm.CreatedAt)
                .IsRequired();

            builder.Property(cpm => cpm.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cpm => cpm.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(cpm => cpm.DeletedBy)
                .HasMaxLength(100);

            // Configure value objects
            builder.OwnsOne(cpm => cpm.AverageCost, money =>
            {
                money.Property(m => m.Amount)
                    .HasPrecision(18, 2)
                    .HasColumnName("AverageCostAmount");

                money.Property(m => m.Currency)
                    .HasConversion<int>()
                    .HasColumnName("AverageCostCurrency");
            });

            builder.OwnsOne(cpm => cpm.TotalRevenue, money =>
            {
                money.Property(m => m.Amount)
                    .HasPrecision(18, 2)
                    .HasColumnName("TotalRevenueAmount");

                money.Property(m => m.Currency)
                    .HasConversion<int>()
                    .HasColumnName("TotalRevenueCurrency");
            });

            // Configure indexes
            builder.HasIndex(cpm => new { cpm.CarrierId, cpm.PeriodStart, cpm.PeriodEnd, cpm.MetricType })
                .IsUnique()
                .HasDatabaseName("IX_CarrierPerformanceMetrics_Carrier_Period_Type");

            builder.HasIndex(cpm => cpm.OrganizationId)
                .HasDatabaseName("IX_CarrierPerformanceMetrics_OrganizationId");

            builder.HasIndex(cpm => cpm.MetricType)
                .HasDatabaseName("IX_CarrierPerformanceMetrics_MetricType");

            builder.HasIndex(cpm => cpm.PeriodStart)
                .HasDatabaseName("IX_CarrierPerformanceMetrics_PeriodStart");

            builder.HasIndex(cpm => cpm.CalculatedAt)
                .HasDatabaseName("IX_CarrierPerformanceMetrics_CalculatedAt");

            builder.HasIndex(cpm => cpm.CreatedAt)
                .HasDatabaseName("IX_CarrierPerformanceMetrics_CreatedAt");

            // Ignore domain events for EF Core
            builder.Ignore(cpm => cpm.DomainEvents);
        }
    }
}
