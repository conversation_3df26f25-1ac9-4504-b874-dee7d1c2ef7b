using PricingManagement.Application.DTOs;
using PricingManagement.Domain.ValueObjects;

namespace PricingManagement.Application.Services
{
    public interface IDiscountService
    {
        Task<IEnumerable<DiscountDto>> CalculateDiscountsAsync(RateCalculationRequestDto request, Money baseAmount);
        Task<DiscountDto> CalculateVolumeDiscountAsync(RateCalculationRequestDto request, Money baseAmount);
        Task<DiscountDto> CalculateCustomerDiscountAsync(RateCalculationRequestDto request, Money baseAmount);
        Task<DiscountDto> CalculatePromotionalDiscountAsync(RateCalculationRequestDto request, Money baseAmount, string? promotionCode = null);
        Task<DiscountDto> CalculateEarlyPaymentDiscountAsync(RateCalculationRequestDto request, Money baseAmount);
        Task<bool> ValidateDiscountEligibilityAsync(RateCalculationRequestDto request, DiscountDto discount);
        Task<bool> RequiresApprovalAsync(DiscountDto discount, Money baseAmount);
    }
}
