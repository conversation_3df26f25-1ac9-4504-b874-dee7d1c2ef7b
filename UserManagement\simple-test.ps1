# Simple test for UserManagement API
$baseUrl = "http://localhost:7002"

Write-Host "Testing UserManagement API at $baseUrl" -ForegroundColor Green

# Test health endpoint
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/organizations/health" -Method GET
    Write-Host "Health Check: SUCCESS - $response" -ForegroundColor Green
} catch {
    Write-Host "Health Check: FAILED - $($_.Exception.Message)" -ForegroundColor Red
}

# Test create organization with invalid user
$createRequest = @{
    Name = "Test Org"
    BusinessRegistrationNumber = "TEST123"
    Type = 0
    HeadquartersAddress = @{
        Street = "123 Test St"
        City = "Test City"
        State = "Test State"
        PostalCode = "12345"
        Country = "USA"
    }
    AdminUserId = "00000000-0000-0000-0000-000000000000"
}

try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/organizations" -Method POST -Body ($createRequest | ConvertTo-Json) -ContentType "application/json"
    Write-Host "Create Organization: SUCCESS - $response" -ForegroundColor Green
} catch {
    Write-Host "Create Organization: FAILED (Expected) - $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "Test Complete!" -ForegroundColor Green
