using System;

namespace PricingManagement.Domain.Common
{
    public abstract class DomainEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; protected set; }

        protected DomainEvent()
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
        }

        protected DomainEvent(Guid? organizationId) : this()
        {
            OrganizationId = organizationId;
        }
    }
}
