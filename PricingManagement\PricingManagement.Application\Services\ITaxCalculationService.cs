using PricingManagement.Application.DTOs;
using PricingManagement.Domain.ValueObjects;

namespace PricingManagement.Application.Services
{
    public interface ITaxCalculationService
    {
        Task<IEnumerable<TaxDto>> CalculateTaxesAsync(RateCalculationRequestDto request, Money taxableAmount);
        Task<TaxDto> CalculateSalesTaxAsync(RateCalculationRequestDto request, Money taxableAmount);
        Task<TaxDto> CalculateVATAsync(RateCalculationRequestDto request, Money taxableAmount);
        Task<TaxDto> CalculateGSTAsync(RateCalculationRequestDto request, Money taxableAmount);
        Task<TaxDto> CalculateExciseTaxAsync(RateCalculationRequestDto request, Money taxableAmount);
        Task<TaxDto> CalculateCustomsDutyAsync(RateCalculationRequestDto request, Money taxableAmount);
        Task<decimal> GetTaxRateAsync(string jurisdiction, string taxType);
        Task<bool> IsTaxExemptAsync(Guid? customerId, string jurisdiction);
        Task<string> DetermineJurisdictionAsync(string address);
        Task<bool> ValidateTaxExemptionCertificateAsync(Guid customerId, string certificateNumber);
    }
}
