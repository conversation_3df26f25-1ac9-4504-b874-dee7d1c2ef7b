using System;

namespace PricingManagement.Domain.Exceptions
{
    public class DomainException : Exception
    {
        public DomainException(string message) : base(message)
        {
        }

        public DomainException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }

    public class PricingRuleNotFoundException : DomainException
    {
        public PricingRuleNotFoundException(Guid ruleId) 
            : base($"Pricing rule with ID {ruleId} was not found.")
        {
        }
    }

    public class ContractNotFoundException : DomainException
    {
        public ContractNotFoundException(Guid contractId) 
            : base($"Contract with ID {contractId} was not found.")
        {
        }
    }

    public class QuoteNotFoundException : DomainException
    {
        public QuoteNotFoundException(Guid quoteId) 
            : base($"Quote with ID {quoteId} was not found.")
        {
        }
    }

    public class InvalidPricingCalculationException : DomainException
    {
        public InvalidPricingCalculationException(string message) 
            : base($"Invalid pricing calculation: {message}")
        {
        }
    }

    public class ExpiredQuoteException : DomainException
    {
        public ExpiredQuoteException(Guid quoteId) 
            : base($"Quote with ID {quoteId} has expired.")
        {
        }
    }

    public class InvalidCurrencyException : DomainException
    {
        public InvalidCurrencyException(string currency) 
            : base($"Invalid currency: {currency}")
        {
        }
    }

    public class InvalidDiscountException : DomainException
    {
        public InvalidDiscountException(string message) 
            : base($"Invalid discount: {message}")
        {
        }
    }
}
