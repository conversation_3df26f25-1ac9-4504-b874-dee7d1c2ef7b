# Test script for UserManagement Organization API
param(
    [string]$BaseUrl = "https://localhost:7002",
    [switch]$SkipCertificateCheck
)

# Skip certificate validation if requested (for development)
if ($SkipCertificateCheck) {
    [System.Net.ServicePointManager]::ServerCertificateValidationCallback = { $true }
    if ($PSVersionTable.PSVersion.Major -ge 6) {
        $PSDefaultParameterValues['Invoke-RestMethod:SkipCertificateCheck'] = $true
        $PSDefaultParameterValues['Invoke-WebRequest:SkipCertificateCheck'] = $true
    }
}

Write-Host "Testing UserManagement Organization API at $BaseUrl" -ForegroundColor Green

# Test 1: Health Check
Write-Host "`n1. Testing Health Endpoint..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$BaseUrl/api/v1/organizations/health" -Method GET
    Write-Host "✓ Health Check: $healthResponse" -ForegroundColor Green
} catch {
    Write-Host "✗ Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Create Organization (This will likely fail due to missing admin user)
Write-Host "`n2. Testing Create Organization Endpoint..." -ForegroundColor Yellow

$createOrgRequest = @{
    Name = "Test Organization $(Get-Date -Format 'yyyyMMdd-HHmmss')"
    BusinessRegistrationNumber = "TEST-$(Get-Random -Minimum 1000 -Maximum 9999)"
    Type = 0  # Business
    HeadquartersAddress = @{
        Street = "123 Test Street"
        City = "Test City"
        State = "Test State"
        PostalCode = "12345"
        Country = "Test Country"
        Latitude = 40.7128
        Longitude = -74.0060
    }
    AdminUserId = "00000000-0000-0000-0000-000000000000"  # This will fail as user doesn't exist
}

try {
    $createResponse = Invoke-RestMethod -Uri "$BaseUrl/api/v1/organizations" -Method POST -Body ($createOrgRequest | ConvertTo-Json) -ContentType "application/json"
    Write-Host "✓ Create Organization: $createResponse" -ForegroundColor Green
} catch {
    $errorDetails = $_.ErrorDetails.Message | ConvertFrom-Json -ErrorAction SilentlyContinue
    if ($errorDetails) {
        Write-Host "✗ Create Organization Failed (Expected): $($errorDetails.title)" -ForegroundColor Yellow
        Write-Host "  Details: $($errorDetails.detail)" -ForegroundColor Yellow
    } else {
        Write-Host "✗ Create Organization Failed: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Test 3: Test Get Organization (will return not implemented)
Write-Host "`n3. Testing Get Organization Endpoint..." -ForegroundColor Yellow
try {
    $getResponse = Invoke-RestMethod -Uri "$BaseUrl/api/v1/organizations/00000000-0000-0000-0000-000000000000" -Method GET
    Write-Host "✓ Get Organization: $getResponse" -ForegroundColor Green
} catch {
    Write-Host "✗ Get Organization Failed (Expected): $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`nAPI Testing Complete!" -ForegroundColor Green
Write-Host "Note: Some failures are expected due to missing test data (users, etc.)" -ForegroundColor Cyan
