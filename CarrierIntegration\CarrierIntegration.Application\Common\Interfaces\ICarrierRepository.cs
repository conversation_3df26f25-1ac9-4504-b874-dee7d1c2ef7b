using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Common.Interfaces
{
    public interface ICarrierRepository
    {
        Task<Carrier?> GetByIdAsync(Guid id);
        Task<Carrier?> GetByCodeAsync(string code, Guid organizationId);
        Task<IReadOnlyList<Carrier>> GetAllAsync(Guid organizationId);
        Task<IReadOnlyList<Carrier>> GetActiveCarriersAsync(Guid organizationId);
        Task<IReadOnlyList<Carrier>> GetCarriersByTypeAsync(CarrierType type, Guid organizationId);
        Task<IReadOnlyList<Carrier>> GetCarriersWithCapabilityAsync(string capability, Guid organizationId);
        Task<(IReadOnlyList<Carrier> carriers, int totalCount)> GetPagedAsync(
            Guid organizationId, 
            int pageNumber, 
            int pageSize, 
            string? searchTerm = null,
            CarrierType? type = null,
            CarrierStatus? status = null);
        Task<Carrier> AddAsync(Carrier carrier);
        Task UpdateAsync(Carrier carrier);
        Task DeleteAsync(Carrier carrier);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> CodeExistsAsync(string code, Guid organizationId, Guid? excludeId = null);
    }
}
