using System;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Common.Interfaces;
using UserManagement.Application.Common.Models.IntegrationEvents;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.ValueObjects;

namespace UserManagement.Application.Organizations.Commands.CreateOrganization
{
    public class CreateOrganizationCommandHandler : IRequestHandler<CreateOrganizationCommand, Guid>
    {
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly ISubscriptionPlanRepository _subscriptionPlanRepository;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<CreateOrganizationCommandHandler> _logger;

        public CreateOrganizationCommandHandler(
            IOrganizationRepository organizationRepository,
            IUserRepository userRepository,
            IRoleRepository roleRepository,
            ISubscriptionPlanRepository subscriptionPlanRepository,
            IMessageBroker messageBroker,
            ILogger<CreateOrganizationCommandHandler> logger)
        {
            _organizationRepository = organizationRepository;
            _userRepository = userRepository;
            _roleRepository = roleRepository;
            _subscriptionPlanRepository = subscriptionPlanRepository;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        public async Task<Guid> Handle(CreateOrganizationCommand request, CancellationToken cancellationToken)
        {
            // Get the admin user
            var adminUser = await _userRepository.GetByIdAsync(request.AdminUserId);
            if (adminUser == null)
            {
                throw new ApplicationException($"Admin user with ID {request.AdminUserId} not found");
            }

            // Get the default subscription plan (Starter plan)
            var defaultPlan = await _subscriptionPlanRepository.GetByNameAsync("Starter");
            if (defaultPlan == null)
            {
                throw new ApplicationException("Default subscription plan not found");
            }
            // Create the address value object
            var address = new Address(
                request.HeadquartersAddress.Street,
                request.HeadquartersAddress.City,
                request.HeadquartersAddress.State,
                request.HeadquartersAddress.PostalCode,
                request.HeadquartersAddress.Country,
                request.HeadquartersAddress.Latitude,
                request.HeadquartersAddress.Longitude);

            // Create the organization
            var organization = new Organization(
                request.Name,
                request.BusinessRegistrationNumber,
                request.Type,
                address);

            // Assign the default subscription plan
            organization.UpdateSubscription(defaultPlan);

            // Add the admin user to the organization
            organization.AddUser(adminUser);
            adminUser.AssignToOrganization(organization);

            // Save the organization first to get its ID
            await _organizationRepository.AddAsync(organization);

            // Create and assign admin role if it doesn't exist
            var adminrole = await _roleRepository.GetByNameAsync("organizationadmin");
            if (adminrole == null)
            {
                adminrole = new Role(
                    Guid.NewGuid(),
                    "organizationadmin",
                    "administrator of the organization",
                    false,
                    organization.Id);
                await _roleRepository.AddAsync(adminrole);
            }

            // Assign admin role to the user
            adminUser.AddRole(adminrole);

            // Update the user with the new role
            await _userRepository.UpdateAsync(adminUser);

            // Publish integration event
            await _messageBroker.PublishAsync("organization.created", new OrganizationCreatedIntegrationEvent
            {
                OrganizationId = organization.Id,
                Name = organization.Name,
                Type = organization.Type,
                AdminUserId = adminUser.Id
            });

            _logger.LogInformation("Organization {OrganizationId} created with name {Name}", organization.Id, organization.Name);

            return organization.Id;
        }
    }
}
