using System;
using System.Collections.Generic;

namespace CarrierIntegration.Application.DTOs
{
    public class CarrierContractDto
    {
        public Guid Id { get; set; }
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public string ContractNumber { get; set; } = null!;
        public string ContractName { get; set; } = null!;
        public string ContractType { get; set; } = null!;
        public string Status { get; set; } = null!;
        public DateTime EffectiveDate { get; set; }
        public DateTime ExpirationDate { get; set; }
        public DateTime? TerminationDate { get; set; }
        public string? TerminationReason { get; set; }
        public bool AutoRenewal { get; set; }
        public int? AutoRenewalDays { get; set; }
        public string? RenewalTerms { get; set; }

        // Financial Terms
        public MoneyDto? MinimumCommitment { get; set; }
        public MoneyDto? MaximumLiability { get; set; }
        public string PaymentTerms { get; set; } = null!;
        public int PaymentDays { get; set; }
        public string Currency { get; set; } = "USD";
        public decimal? VolumeDiscountThreshold { get; set; }
        public decimal? VolumeDiscountRate { get; set; }
        public string? InvoicingFrequency { get; set; }
        public string? BillingMethod { get; set; }

        // Service Level Agreements
        public decimal? OnTimeDeliveryTarget { get; set; }
        public decimal? OnTimeDeliveryPenalty { get; set; }
        public decimal? DamageClaimLimit { get; set; }
        public int? ClaimResolutionDays { get; set; }
        public decimal? ServiceLevelCredit { get; set; }
        public string? PerformanceIncentives { get; set; }
        public string? QualityStandards { get; set; }

        // Coverage and Restrictions
        public string? GeographicCoverage { get; set; }
        public string? ServiceTypes { get; set; }
        public string? ExcludedServices { get; set; }
        public string? WeightLimitations { get; set; }
        public string? DimensionLimitations { get; set; }
        public string? CommodityRestrictions { get; set; }
        public string? SpecialHandlingTerms { get; set; }

        // Legal and Compliance
        public string? LiabilityTerms { get; set; }
        public string? InsuranceRequirements { get; set; }
        public string? ComplianceRequirements { get; set; }
        public string? DisputeResolution { get; set; }
        public string? GoverningLaw { get; set; }
        public string? ForceMateure { get; set; }

        // Contract Management
        public string? ContractOwner { get; set; }
        public string? CarrierRepresentative { get; set; }
        public string? LegalReviewStatus { get; set; }
        public DateTime? LastReviewDate { get; set; }
        public DateTime? NextReviewDate { get; set; }
        public string? ReviewNotes { get; set; }
        public string? Amendments { get; set; }
        public string? AttachedDocuments { get; set; }
        public string? Notes { get; set; }

        // Performance Tracking
        public decimal CurrentVolumeCommitment { get; set; }
        public decimal ActualVolume { get; set; }
        public decimal CommitmentPercentage { get; set; }
        public decimal CurrentSpend { get; set; }
        public decimal AveragePerformanceScore { get; set; }
        public int TotalShipments { get; set; }
        public int OnTimeShipments { get; set; }
        public int ClaimsCount { get; set; }
        public MoneyDto? TotalClaimsValue { get; set; }

        // Audit fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = null!;
        public DateTime? UpdatedAt { get; set; }
        public string? UpdatedBy { get; set; }
    }

    public class CarrierRelationshipScoreDto
    {
        public Guid Id { get; set; }
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public DateTime CalculationDate { get; set; }
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }

        // Overall Relationship Score
        public decimal OverallScore { get; set; }
        public string ScoreGrade { get; set; } = null!;
        public string ScoreTrend { get; set; } = null!;

        // Component Scores
        public decimal PerformanceScore { get; set; }
        public decimal ReliabilityScore { get; set; }
        public decimal CostEffectivenessScore { get; set; }
        public decimal ServiceQualityScore { get; set; }
        public decimal ComplianceScore { get; set; }
        public decimal CommunicationScore { get; set; }
        public decimal InnovationScore { get; set; }
        public decimal StrategicValueScore { get; set; }

        // Performance Metrics
        public decimal OnTimeDeliveryRate { get; set; }
        public decimal DamageRate { get; set; }
        public decimal ClaimResolutionTime { get; set; }
        public decimal CustomerSatisfactionScore { get; set; }
        public decimal ServiceLevelCompliance { get; set; }

        // Financial Metrics
        public decimal CostCompetitiveness { get; set; }
        public decimal VolumeCommitmentCompliance { get; set; }
        public decimal PaymentTermsCompliance { get; set; }
        public decimal TotalSpend { get; set; }
        public decimal CostSavingsAchieved { get; set; }

        // Relationship Metrics
        public int TotalShipments { get; set; }
        public int SuccessfulShipments { get; set; }
        public int FailedShipments { get; set; }
        public int ExceptionShipments { get; set; }
        public int EscalationsCount { get; set; }
        public int IssuesResolvedCount { get; set; }
        public decimal AverageIssueResolutionTime { get; set; }

        // Strategic Metrics
        public decimal CapacityUtilization { get; set; }
        public decimal ServiceExpansionRate { get; set; }
        public decimal TechnologyAdoptionScore { get; set; }
        public decimal SustainabilityScore { get; set; }
        public decimal RiskScore { get; set; }

        // Recommendations and Actions
        public string? StrengthAreas { get; set; }
        public string? ImprovementAreas { get; set; }
        public string? RecommendedActions { get; set; }
        public string? RiskFactors { get; set; }
        public string? OpportunityAreas { get; set; }
        public string? NextReviewDate { get; set; }

        // Calculation Metadata
        public string CalculationMethod { get; set; } = null!;
        public string DataSources { get; set; } = null!;
        public decimal ConfidenceLevel { get; set; }
        public string? Notes { get; set; }
    }

    public class CarrierSlaComplianceDto
    {
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
        public decimal OverallComplianceScore { get; set; }
        public List<SlaMetricComplianceDto> Metrics { get; set; } = new();
        public List<SlaViolationDto> Violations { get; set; } = new();
        public decimal PenaltiesApplied { get; set; }
        public decimal CreditsEarned { get; set; }
    }

    public class SlaMetricComplianceDto
    {
        public string MetricName { get; set; } = null!;
        public decimal Target { get; set; }
        public decimal Actual { get; set; }
        public decimal CompliancePercentage { get; set; }
        public string Status { get; set; } = null!; // Met, Missed, Exceeded
        public decimal? Penalty { get; set; }
        public decimal? Credit { get; set; }
    }

    public class SlaViolationDto
    {
        public DateTime Date { get; set; }
        public string MetricName { get; set; } = null!;
        public string Description { get; set; } = null!;
        public string Severity { get; set; } = null!;
        public decimal Impact { get; set; }
        public string Status { get; set; } = null!;
        public string? Resolution { get; set; }
    }

    public class CarrierRelationshipInsightsDto
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateTime GeneratedAt { get; set; }
        public CarrierRelationshipSummaryDto Summary { get; set; } = null!;
        public List<CarrierRelationshipScoreDto> TopPerformers { get; set; } = new();
        public List<CarrierRelationshipScoreDto> UnderPerformers { get; set; } = new();
        public List<RelationshipTrendDto> Trends { get; set; } = new();
        public List<RecommendationDto> Recommendations { get; set; } = new();
        public List<AlertDto> Alerts { get; set; } = new();
        public Dictionary<string, object> KeyMetrics { get; set; } = new();
    }

    public class CarrierRelationshipSummaryDto
    {
        public int TotalCarriers { get; set; }
        public int ActiveContracts { get; set; }
        public int ExpiringContracts { get; set; }
        public decimal AverageRelationshipScore { get; set; }
        public decimal TotalSpend { get; set; }
        public decimal AveragePerformanceScore { get; set; }
        public int TotalShipments { get; set; }
        public decimal OverallSlaCompliance { get; set; }
    }

    public class RelationshipTrendDto
    {
        public string MetricName { get; set; } = null!;
        public List<TrendDataDto> Data { get; set; } = new();
        public string TrendDirection { get; set; } = null!; // Improving, Declining, Stable
        public decimal ChangePercentage { get; set; }
    }

    public class ContractRenewalRecommendationDto
    {
        public Guid ContractId { get; set; }
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public string ContractNumber { get; set; } = null!;
        public DateTime ExpirationDate { get; set; }
        public int DaysUntilExpiration { get; set; }
        public string RecommendedAction { get; set; } = null!; // Renew, Renegotiate, Terminate
        public string Reasoning { get; set; } = null!;
        public decimal CurrentPerformanceScore { get; set; }
        public decimal CostCompetitiveness { get; set; }
        public List<string> NegotiationPoints { get; set; } = new();
        public List<string> RiskFactors { get; set; } = new();
        public string Priority { get; set; } = null!;
    }

    // CarrierServiceDto and CarrierAccountDto are defined in CarrierDtos.cs to avoid duplicates

    // Additional supporting DTOs
    public class OptimizationConfigurationDto
    {
        public Dictionary<string, decimal> Weights { get; set; } = new();
        public List<string> Constraints { get; set; } = new();
        public string OptimizationGoal { get; set; } = null!;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    public class ShipmentOptimizationRequestDto
    {
        public Guid ShipmentId { get; set; }
        public AddressDto Origin { get; set; } = null!;
        public AddressDto Destination { get; set; } = null!;
        public List<PackageDto> Packages { get; set; } = new();
        public DateTime ShipDate { get; set; }
        public string? ServiceLevel { get; set; }
        public Dictionary<string, object>? Requirements { get; set; }
    }

    public class ShipmentOptimizationResultDto
    {
        public Guid ShipmentId { get; set; }
        public Guid RecommendedCarrierId { get; set; }
        public string RecommendedCarrierName { get; set; } = null!;
        public string RecommendedService { get; set; } = null!;
        public decimal EstimatedCost { get; set; }
        public decimal PotentialSavings { get; set; }
        public decimal OptimizationScore { get; set; }
        public string Reasoning { get; set; } = null!;
    }

    public class BatchOptimizationRequest
    {
        public Guid OrganizationId { get; set; }
        public List<ShipmentOptimizationRequestDto> Shipments { get; set; } = new();
        public OptimizationCriteriaDto OptimizationCriteria { get; set; } = null!;
        public string GroupingStrategy { get; set; } = "Route";
        public int MaxBatchSize { get; set; } = 100;
        public TimeSpan OptimizationTimeout { get; set; } = TimeSpan.FromMinutes(5);
    }
}
