# Test database connectivity for UserManagement service
param(
    [string]$ConnectionString = "Host=**************;port=5432;Database=UserManagement;User Id=postgres;Password=************"
)

Write-Host "Testing UserManagement Database Connection..." -ForegroundColor Green

try {
    # Load PostgreSQL .NET provider
    Add-Type -Path "C:\Program Files\dotnet\shared\Microsoft.NETCore.App\*\System.Data.Common.dll" -ErrorAction SilentlyContinue
    
    # Try to load Npgsql if available
    try {
        Add-Type -AssemblyName "Npgsql" -ErrorAction Stop
        $useNpgsql = $true
    } catch {
        Write-Host "Npgsql not available, using basic connection test" -ForegroundColor Yellow
        $useNpgsql = $false
    }
    
    if ($useNpgsql) {
        # Test with Npgsql
        $connection = New-Object Npgsql.NpgsqlConnection($ConnectionString)
        $connection.Open()
        
        # Test basic query
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT version();"
        $version = $command.ExecuteScalar()
        
        Write-Host "Database Connection: SUCCESS" -ForegroundColor Green
        Write-Host "PostgreSQL Version: $version" -ForegroundColor Green
        
        # Test if UserManagement database exists
        $command.CommandText = "SELECT datname FROM pg_database WHERE datname = 'UserManagement';"
        $dbExists = $command.ExecuteScalar()
        
        if ($dbExists) {
            Write-Host "UserManagement Database: EXISTS" -ForegroundColor Green

            # Test if Organizations table exists
            $command.CommandText = "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'Organizations');"
            $tableExists = $command.ExecuteScalar()

            if ($tableExists) {
                Write-Host "Organizations Table: EXISTS" -ForegroundColor Green

                # Count organizations
                $command.CommandText = "SELECT COUNT(*) FROM Organizations;"
                $orgCount = $command.ExecuteScalar()
                Write-Host "Organizations Count: $orgCount" -ForegroundColor Green
            } else {
                Write-Host "Organizations Table: NOT FOUND" -ForegroundColor Red
                Write-Host "  Run database migrations to create tables" -ForegroundColor Yellow
            }
        } else {
            Write-Host "UserManagement Database: NOT FOUND" -ForegroundColor Red
        }
        
        $connection.Close()
    } else {
        # Basic connection test without Npgsql
        Write-Host "Limited database test (Npgsql not available)" -ForegroundColor Yellow
        Write-Host "Connection string format appears valid" -ForegroundColor Green
    }
    
} catch {
    Write-Host "Database Connection: FAILED" -ForegroundColor Red
    Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
    
    # Provide troubleshooting tips
    Write-Host "`nTroubleshooting Tips:" -ForegroundColor Yellow
    Write-Host "1. Verify PostgreSQL server is running on **************:5432" -ForegroundColor Yellow
    Write-Host "2. Check if UserManagement database exists" -ForegroundColor Yellow
    Write-Host "3. Verify credentials: postgres/************" -ForegroundColor Yellow
    Write-Host "4. Run database migrations: dotnet ef database update" -ForegroundColor Yellow
}

Write-Host "`nDatabase Test Complete!" -ForegroundColor Green
