using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PricingManagement.Application.Services
{
    public interface IPricingEngine
    {
        Task<PricingResultDto> CalculateRateAsync(RateCalculationRequestDto request);
        Task<IEnumerable<CarrierRateComparisonDto>> CompareCarrierRatesAsync(RateCalculationRequestDto request);
        Task<PricingResultDto> ApplyContractPricingAsync(RateCalculationRequestDto request, Guid contractId);
        Task<PricingResultDto> ApplyPromotionalPricingAsync(RateCalculationRequestDto request, string promotionCode);
        Task<PricingSimulationResultDto> SimulatePricingAsync(PricingSimulationRequestDto request);
        Task<IEnumerable<PricingRuleDto>> GetApplicableRulesAsync(RateCalculationRequestDto request);
        Task<bool> ValidateRateCalculationAsync(RateCalculationRequestDto request);
    }

    public class RateCalculationRequestDto
    {
        public Guid OrganizationId { get; set; }
        public Guid? CustomerId { get; set; }
        public Guid? ShipperId { get; set; }
        public string? CustomerSegment { get; set; }
        public string? ShipperType { get; set; }
        public string ServiceType { get; set; } = string.Empty;
        public string OriginAddress { get; set; } = string.Empty;
        public string DestinationAddress { get; set; } = string.Empty;
        public string? OriginZoneId { get; set; }
        public string? DestinationZoneId { get; set; }
        public decimal? Distance { get; set; }
        public Weight PackageWeight { get; set; } = new Weight(1, WeightUnit.Pounds);
        public Dimensions? PackageDimensions { get; set; }
        public decimal? DeclaredValue { get; set; }
        public List<string> SpecialServices { get; set; } = new();
        public CurrencyCode Currency { get; set; } = CurrencyCode.USD;
        public DateTime ShipDate { get; set; } = DateTime.UtcNow;
        public bool IncludeSurcharges { get; set; } = true;
        public bool IncludeDiscounts { get; set; } = true;
        public bool IncludeTaxes { get; set; } = true;
        public Dictionary<string, object> AdditionalAttributes { get; set; } = new();
    }

    public class PricingResultDto
    {
        public Money BaseRate { get; set; } = Money.Zero(CurrencyCode.USD);
        public List<SurchargeDto> Surcharges { get; set; } = new();
        public List<DiscountDto> Discounts { get; set; } = new();
        public List<TaxDto> Taxes { get; set; } = new();
        public Money TotalSurcharges { get; set; } = Money.Zero(CurrencyCode.USD);
        public Money TotalDiscounts { get; set; } = Money.Zero(CurrencyCode.USD);
        public Money TotalTaxes { get; set; } = Money.Zero(CurrencyCode.USD);
        public Money TotalAmount { get; set; } = Money.Zero(CurrencyCode.USD);
        public Weight BillableWeight { get; set; } = new Weight(1, WeightUnit.Pounds);
        public Weight? DimensionalWeight { get; set; }
        public List<string> AppliedRules { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public string? TransitTime { get; set; }
        public DateTime CalculatedAt { get; set; } = DateTime.UtcNow;
        public string CalculationId { get; set; } = Guid.NewGuid().ToString();
    }

    public class CarrierRateComparisonDto
    {
        public string CarrierName { get; set; } = string.Empty;
        public string ServiceLevel { get; set; } = string.Empty;
        public PricingResultDto PricingResult { get; set; } = new();
        public string? TransitTime { get; set; }
        public decimal? DeliveryCommitment { get; set; }
        public bool IsRecommended { get; set; }
        public string? RecommendationReason { get; set; }
        public Dictionary<string, object> CarrierSpecificData { get; set; } = new();
    }

    public class PricingSimulationRequestDto
    {
        public RateCalculationRequestDto BaseRequest { get; set; } = new();
        public List<PricingScenarioDto> Scenarios { get; set; } = new();
        public bool IncludeMarginAnalysis { get; set; }
        public bool IncludeCompetitiveAnalysis { get; set; }
        public bool IncludeVolumeImpact { get; set; }
    }

    public class PricingScenarioDto
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    public class PricingSimulationResultDto
    {
        public PricingResultDto BaseResult { get; set; } = new();
        public List<ScenarioResultDto> ScenarioResults { get; set; } = new();
        public MarginAnalysisDto? MarginAnalysis { get; set; }
        public CompetitiveAnalysisDto? CompetitiveAnalysis { get; set; }
        public VolumeImpactAnalysisDto? VolumeImpactAnalysis { get; set; }
    }

    public class ScenarioResultDto
    {
        public string ScenarioName { get; set; } = string.Empty;
        public PricingResultDto Result { get; set; } = new();
        public decimal VariancePercentage { get; set; }
        public Money VarianceAmount { get; set; } = Money.Zero(CurrencyCode.USD);
    }

    public class MarginAnalysisDto
    {
        public Money EstimatedCost { get; set; } = Money.Zero(CurrencyCode.USD);
        public Money EstimatedMargin { get; set; } = Money.Zero(CurrencyCode.USD);
        public decimal MarginPercentage { get; set; }
        public string MarginCategory { get; set; } = string.Empty;
    }

    public class CompetitiveAnalysisDto
    {
        public Money AverageMarketRate { get; set; } = Money.Zero(CurrencyCode.USD);
        public Money CompetitivePosition { get; set; } = Money.Zero(CurrencyCode.USD);
        public decimal CompetitiveIndex { get; set; }
        public string CompetitiveRating { get; set; } = string.Empty;
    }

    public class VolumeImpactAnalysisDto
    {
        public List<VolumeScenarioDto> VolumeScenarios { get; set; } = new();
        public decimal OptimalVolumeThreshold { get; set; }
        public Money RevenueImpact { get; set; } = Money.Zero(CurrencyCode.USD);
    }

    public class VolumeScenarioDto
    {
        public decimal VolumeMultiplier { get; set; }
        public Money AdjustedRate { get; set; } = Money.Zero(CurrencyCode.USD);
        public Money RevenueProjection { get; set; } = Money.Zero(CurrencyCode.USD);
    }
}
