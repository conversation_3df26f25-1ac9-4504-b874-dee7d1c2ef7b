using System;
using System.Collections.Generic;

namespace CarrierIntegration.Application.DTOs
{
    public class CarrierApiResponse<T>
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public T? Data { get; set; }
        public string? ErrorCode { get; set; }
        public List<string>? Errors { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string? RequestId { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    public class CarrierBookingRequestDto
    {
        public string? BookingReference { get; set; }
        public Guid? OrderId { get; set; }
        public Guid? ShipmentId { get; set; }
        public string ServiceCode { get; set; } = null!;
        public DateTime ShipDate { get; set; }
        public DateTime? DeliveryDate { get; set; }

        // Addresses
        public AddressDto PickupAddress { get; set; } = null!;
        public AddressDto DeliveryAddress { get; set; } = null!;

        // Contacts
        public ContactInfoDto PickupContact { get; set; } = null!;
        public ContactInfoDto DeliveryContact { get; set; } = null!;

        // Shipment Details
        public List<PackageDto> Packages { get; set; } = new();
        public WeightDto TotalWeight { get; set; } = null!;
        public DimensionsDto? TotalDimensions { get; set; }
        public MoneyDto? DeclaredValue { get; set; }
        public string? CommodityDescription { get; set; }

        // Service Options
        public bool RequiresSignature { get; set; }
        public bool IsResidential { get; set; }
        public bool IsHazmat { get; set; }
        public bool SaturdayDelivery { get; set; }
        public string? SpecialInstructions { get; set; }
        public List<string>? AdditionalServices { get; set; }

        // Billing
        public string PaymentMethod { get; set; } = "Sender";
        public string? AccountNumber { get; set; }
        public AddressDto? BillingAddress { get; set; }

        // References
        public string? CustomerReference { get; set; }
        public string? InvoiceNumber { get; set; }
        public string? PurchaseOrderNumber { get; set; }
        public Dictionary<string, string>? CustomFields { get; set; }
    }

    public class CarrierBookingResponseDto
    {
        public string BookingReference { get; set; } = null!;
        public string? CarrierBookingNumber { get; set; }
        public string? TrackingNumber { get; set; }
        public List<string>? TrackingNumbers { get; set; }
        public string Status { get; set; } = null!;
        public DateTime BookingDate { get; set; }
        public DateTime? EstimatedDeliveryDate { get; set; }
        public MoneyDto? TotalCost { get; set; }
        public List<CarrierChargeDto>? Charges { get; set; }
        public List<LabelDto>? Labels { get; set; }
        public string? PickupConfirmationNumber { get; set; }
        public DateTime? PickupDate { get; set; }
        public string? ServiceCode { get; set; }
        public string? ServiceName { get; set; }
        public Dictionary<string, object>? CarrierSpecificData { get; set; }
        public List<string>? Warnings { get; set; }
    }

    public class CarrierTrackingDto
    {
        public string TrackingNumber { get; set; } = null!;
        public string Status { get; set; } = null!;
        public string StatusDescription { get; set; } = null!;
        public DateTime LastUpdated { get; set; }
        public DateTime? EstimatedDeliveryDate { get; set; }
        public DateTime? ActualDeliveryDate { get; set; }
        public AddressDto? CurrentLocation { get; set; }
        public AddressDto? DestinationAddress { get; set; }
        public List<TrackingEventDto> Events { get; set; } = new();
        public string? DeliverySignature { get; set; }
        public string? DeliveredTo { get; set; }
        public bool IsDelivered { get; set; }
        public bool HasException { get; set; }
        public string? ExceptionReason { get; set; }
        public WeightDto? ActualWeight { get; set; }
        public Dictionary<string, object>? CarrierSpecificData { get; set; }
    }

    public class TrackingEventDto
    {
        public DateTime Timestamp { get; set; }
        public string Status { get; set; } = null!;
        public string Description { get; set; } = null!;
        public AddressDto? Location { get; set; }
        public string? LocationCode { get; set; }
        public string? LocationName { get; set; }
        public string? EventType { get; set; }
        public string? ReasonCode { get; set; }
        public string? ReasonDescription { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierChargeDto
    {
        public string ChargeType { get; set; } = null!;
        public string Description { get; set; } = null!;
        public MoneyDto Amount { get; set; } = null!;
        public bool IsTaxable { get; set; }
        public string? TaxType { get; set; }
        public MoneyDto? TaxAmount { get; set; }
        public string? ChargeCode { get; set; }
    }

    public class LabelDto
    {
        public string Format { get; set; } = null!; // PDF, PNG, ZPL, etc.
        public string Content { get; set; } = null!; // Base64 encoded
        public string? FileName { get; set; }
        public string? ContentType { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
        public string? LabelType { get; set; } // Shipping, Return, etc.
    }

    public class PackageDto
    {
        public string? PackageId { get; set; }
        public WeightDto Weight { get; set; } = null!;
        public DimensionsDto Dimensions { get; set; } = null!;
        public string PackageType { get; set; } = "Package";
        public MoneyDto? DeclaredValue { get; set; }
        public string? Description { get; set; }
        public bool IsHazmat { get; set; }
        public string? HazmatClass { get; set; }
        public string? TrackingNumber { get; set; }
        public Dictionary<string, object>? CustomAttributes { get; set; }
    }

    public class CarrierRateRequestDto
    {
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public List<PackageDto> Packages { get; set; } = new();
        public DateTime ShipDate { get; set; }
        public List<string>? ServiceCodes { get; set; }
        public bool IncludeInsurance { get; set; }
        public MoneyDto? DeclaredValue { get; set; }
        public bool SaturdayDelivery { get; set; }
        public bool ResidentialDelivery { get; set; }
        public string? AccountNumber { get; set; }
        public Dictionary<string, object>? Options { get; set; }
    }

    public class CarrierRateResponseDto
    {
        public List<CarrierRateDto> Rates { get; set; } = new();
        public List<string>? Warnings { get; set; }
        public List<string>? Errors { get; set; }
        public string? RequestId { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class CarrierRateDto
    {
        public string ServiceCode { get; set; } = null!;
        public string ServiceName { get; set; } = null!;
        public string ServiceLevel { get; set; } = null!;
        public MoneyDto TotalCost { get; set; } = null!;
        public MoneyDto BaseRate { get; set; } = null!;
        public List<CarrierChargeDto> Charges { get; set; } = new();
        public int EstimatedTransitDays { get; set; }
        public DateTime? EstimatedDeliveryDate { get; set; }
        public bool IsGuaranteed { get; set; }
        public string? GuaranteeType { get; set; }
        public bool RequiresSignature { get; set; }
        public bool SaturdayDelivery { get; set; }
        public List<string>? IncludedServices { get; set; }
        public List<string>? AvailableOptions { get; set; }
        public Dictionary<string, object>? CarrierSpecificData { get; set; }
    }

    public class AddressValidationRequestDto
    {
        public AddressDto Address { get; set; } = null!;
        public bool ValidateResidential { get; set; } = true;
        public bool ReturnSuggestions { get; set; } = true;
    }

    public class AddressValidationResponseDto
    {
        public bool IsValid { get; set; }
        public AddressDto? ValidatedAddress { get; set; }
        public List<AddressDto>? SuggestedAddresses { get; set; }
        public bool IsResidential { get; set; }
        public string? ValidationLevel { get; set; }
        public List<string>? ValidationMessages { get; set; }
        public List<string>? Warnings { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierServiceAvailabilityDto
    {
        public string ServiceCode { get; set; } = null!;
        public string ServiceName { get; set; } = null!;
        public bool IsAvailable { get; set; }
        public string? UnavailableReason { get; set; }
        public DateTime? NextAvailableDate { get; set; }
        public List<string>? Restrictions { get; set; }
        public Dictionary<string, object>? ServiceDetails { get; set; }
    }

    public class CarrierCapabilityDto
    {
        public string CapabilityType { get; set; } = null!;
        public string Description { get; set; } = null!;
        public bool IsSupported { get; set; }
        public List<string>? SupportedValues { get; set; }
        public Dictionary<string, object>? Limitations { get; set; }
    }

    // Cost Optimization DTOs
    public class CostOptimizationOpportunityDto
    {
        public string OpportunityType { get; set; } = null!;
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public MoneyDto EstimatedSavings { get; set; } = null!;
        public decimal SavingsPercentage { get; set; }
        public string Priority { get; set; } = null!;
        public string ImplementationEffort { get; set; } = null!;
        public string Category { get; set; } = null!;
        public List<string> ActionSteps { get; set; } = new();
        public string? Timeline { get; set; }
        public List<string>? Prerequisites { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    // Supporting DTOs are defined in CommonDtos.cs to avoid duplicates
}
