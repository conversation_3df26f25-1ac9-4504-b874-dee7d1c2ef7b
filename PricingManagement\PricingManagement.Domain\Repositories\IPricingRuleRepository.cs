using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PricingManagement.Domain.Repositories
{
    public interface IPricingRuleRepository
    {
        Task<PricingRule?> GetByIdAsync(Guid id);
        Task<PricingRule?> GetByIdAsync(Guid id, Guid organizationId);
        Task<IEnumerable<PricingRule>> GetByOrganizationAsync(Guid organizationId);
        Task<IEnumerable<PricingRule>> GetActiveRulesAsync(Guid organizationId);
        Task<IEnumerable<PricingRule>> GetActiveRulesByTypeAsync(Guid organizationId, PricingRuleType ruleType);
        Task<IEnumerable<PricingRule>> GetApplicableRulesAsync(
            Guid organizationId,
            string? serviceType = null,
            string? originZone = null,
            string? destinationZone = null,
            decimal? weight = null,
            decimal? distance = null,
            decimal? value = null,
            string? customerSegment = null,
            string? shipperType = null);
        Task<IEnumerable<PricingRule>> GetRulesByPriorityAsync(Guid organizationId, int minPriority, int maxPriority);
        Task<IEnumerable<PricingRule>> GetExpiringRulesAsync(Guid organizationId, DateTime beforeDate);
        Task<bool> ExistsAsync(Guid id, Guid organizationId);
        Task<bool> NameExistsAsync(string name, Guid organizationId, Guid? excludeId = null);
        Task AddAsync(PricingRule pricingRule);
        Task UpdateAsync(PricingRule pricingRule);
        Task DeleteAsync(PricingRule pricingRule);
        Task<int> CountByOrganizationAsync(Guid organizationId);
        Task<int> CountActiveByOrganizationAsync(Guid organizationId);
    }
}
