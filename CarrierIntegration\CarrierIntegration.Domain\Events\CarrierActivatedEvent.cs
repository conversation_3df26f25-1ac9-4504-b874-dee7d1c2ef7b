using CarrierIntegration.Domain.Common;
using System;

namespace CarrierIntegration.Domain.Events
{
    public class CarrierActivatedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid CarrierId { get; }
        public string CarrierName { get; }

        public CarrierActivatedEvent(Guid carrierId, Guid? organizationId, string carrierName)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            CarrierId = carrierId;
            CarrierName = carrierName;
        }
    }
}
