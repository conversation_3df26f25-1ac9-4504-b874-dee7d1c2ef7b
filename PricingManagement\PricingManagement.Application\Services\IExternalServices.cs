using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PricingManagement.Application.Services
{
    // External service interfaces for integration with other TriTrackz services
    
    public interface IIdentityService
    {
        Task<bool> ValidateTokenAsync(string token);
        Task<UserInfoDto> GetUserInfoAsync(Guid userId);
    }

    public interface IUserManagementService
    {
        Task<UserProfileDto> GetUserProfileAsync(Guid userId);
        Task<OrganizationDto> GetOrganizationAsync(Guid organizationId);
    }

    public interface IMasterManagementService
    {
        Task<ZoneDto> GetZoneAsync(string zoneId);
        Task<ServiceTypeDto> GetServiceTypeAsync(string serviceTypeId);
        Task<decimal> GetDistanceAsync(string originZone, string destinationZone);
    }

    public interface ICustomerManagementService
    {
        Task<CustomerDto> GetCustomerAsync(Guid customerId);
        Task<string> GetCustomerSegmentAsync(Guid customerId);
    }

    public interface IShipperManagementService
    {
        Task<ShipperDto> GetShipperAsync(Guid shipperId);
        Task<string> GetShipperTypeAsync(Guid shipperId);
    }

    public interface IOrderManagementService
    {
        Task<Guid> CreateOrderFromQuoteAsync(Guid quoteId, CreateOrderFromQuoteDto request);
    }

    public interface IShipmentManagementService
    {
        Task<ShipmentDto> GetShipmentAsync(Guid shipmentId);
        Task NotifyShipmentPricingUpdateAsync(Guid shipmentId, decimal newPrice);
    }

    // DTOs for external service responses
    public class UserInfoDto
    {
        public Guid Id { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public Guid OrganizationId { get; set; }
        public List<string> Roles { get; set; } = new();
        public List<string> Permissions { get; set; } = new();
    }

    public class UserProfileDto
    {
        public Guid Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public Guid OrganizationId { get; set; }
    }

    public class OrganizationDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    public class ZoneDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public string Region { get; set; } = string.Empty;
    }

    public class ServiceTypeDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
    }

    public class CustomerDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Segment { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    public class ShipperDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    public class ShipmentDto
    {
        public Guid Id { get; set; }
        public string ShipmentNumber { get; set; } = string.Empty;
        public Guid CustomerId { get; set; }
        public Guid ShipperId { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    public class CreateOrderFromQuoteDto
    {
        public Guid QuoteId { get; set; }
        public Guid CustomerId { get; set; }
        public string? SpecialInstructions { get; set; }
        public DateTime? RequestedPickupDate { get; set; }
        public DateTime? RequestedDeliveryDate { get; set; }
    }
}
