using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PricingManagement.Infrastructure.Persistence;

namespace PricingManagement.Infrastructure.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static async Task SeedDataAsync(this IServiceProvider serviceProvider)
        {
            using var scope = serviceProvider.CreateScope();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<DataSeeder>>();
            
            try
            {
                var seeder = scope.ServiceProvider.GetRequiredService<DataSeeder>();
                await seeder.SeedAsync();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while seeding the database");
                throw;
            }
        }
    }
}
