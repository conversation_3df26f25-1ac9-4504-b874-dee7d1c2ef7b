# Comprehensive test script for all UserManagement APIs
param(
    [string]$BaseUrl = "https://localhost:7002",
    [switch]$SkipCertificateCheck
)

# Skip certificate validation if requested (for development)
if ($SkipCertificateCheck) {
    [System.Net.ServicePointManager]::ServerCertificateValidationCallback = { $true }
    if ($PSVersionTable.PSVersion.Major -ge 6) {
        $PSDefaultParameterValues['Invoke-RestMethod:SkipCertificateCheck'] = $true
        $PSDefaultParameterValues['Invoke-WebRequest:SkipCertificateCheck'] = $true
    }
}

Write-Host "Testing All UserManagement APIs at $BaseUrl" -ForegroundColor Green

# Function to test an endpoint
function Test-Endpoint {
    param(
        [string]$Name,
        [string]$Url,
        [string]$Method = "GET",
        [object]$Body = $null,
        [bool]$ExpectFailure = $false
    )
    
    Write-Host "`nTesting $Name..." -ForegroundColor Yellow
    try {
        $params = @{
            Uri = $Url
            Method = $Method
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json)
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-RestMethod @params
        if ($ExpectFailure) {
            Write-Host "✗ $Name: Expected failure but got success" -ForegroundColor Red
        } else {
            Write-Host "✓ $Name: Success" -ForegroundColor Green
        }
        return $response
    } catch {
        if ($ExpectFailure) {
            Write-Host "✓ $Name: Failed as expected - $($_.Exception.Message)" -ForegroundColor Yellow
        } else {
            Write-Host "✗ $Name: Failed - $($_.Exception.Message)" -ForegroundColor Red
        }
        return $null
    }
}

# Test Organization APIs
Write-Host "`n=== ORGANIZATION APIs ===" -ForegroundColor Cyan
Test-Endpoint "Organizations Health" "$BaseUrl/api/v1/organizations/health"
Test-Endpoint "Get Organization" "$BaseUrl/api/v1/organizations/00000000-0000-0000-0000-000000000000" -ExpectFailure $true

# Test User APIs
Write-Host "`n=== USER APIs ===" -ForegroundColor Cyan
Test-Endpoint "Users Health" "$BaseUrl/api/v1/users/health" -ExpectFailure $true

# Test Auth APIs
Write-Host "`n=== AUTH APIs ===" -ForegroundColor Cyan
Test-Endpoint "Auth Health" "$BaseUrl/api/v1/auth/health" -ExpectFailure $true

# Test Team APIs
Write-Host "`n=== TEAM APIs ===" -ForegroundColor Cyan
Test-Endpoint "Teams Health" "$BaseUrl/api/v1/teams/health" -ExpectFailure $true

# Test Branch APIs
Write-Host "`n=== BRANCH APIs ===" -ForegroundColor Cyan
Test-Endpoint "Branches Health" "$BaseUrl/api/v1/branches/health" -ExpectFailure $true

# Test Security APIs
Write-Host "`n=== SECURITY APIs ===" -ForegroundColor Cyan
Test-Endpoint "Security Health" "$BaseUrl/api/v1/security/health" -ExpectFailure $true

# Test GDPR APIs
Write-Host "`n=== GDPR APIs ===" -ForegroundColor Cyan
Test-Endpoint "GDPR Health" "$BaseUrl/api/v1/gdpr/health" -ExpectFailure $true

# Test Subscription Plans APIs
Write-Host "`n=== SUBSCRIPTION PLANS APIs ===" -ForegroundColor Cyan
Test-Endpoint "Subscription Plans Health" "$BaseUrl/api/v1/subscriptionplans/health" -ExpectFailure $true

# Test Two Factor Auth APIs
Write-Host "`n=== TWO FACTOR AUTH APIs ===" -ForegroundColor Cyan
Test-Endpoint "Two Factor Auth Health" "$BaseUrl/api/v1/twofactorauth/health" -ExpectFailure $true

Write-Host "`n=== API TESTING COMPLETE ===" -ForegroundColor Green
Write-Host "Summary: Most endpoints are expected to fail as they may not have health endpoints implemented." -ForegroundColor Cyan
Write-Host "The main goal is to verify the service is running and responding." -ForegroundColor Cyan
