using System;
using System.Collections.Generic;

namespace CarrierIntegration.Application.DTOs
{
    public class CarrierAnalyticsDto
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string AnalyticsType { get; set; } = null!;
        public string Granularity { get; set; } = null!;
        public DateTime GeneratedAt { get; set; }
        public object? Metrics { get; set; }
        public object? Summary { get; set; }
        public object? Trends { get; set; }
        public List<AlertDto>? Alerts { get; set; }
        public List<PredictionDto>? Predictions { get; set; }
        public List<ComparisonDto>? Comparisons { get; set; }
    }

    public class CarrierDashboardDto
    {
        public Guid OrganizationId { get; set; }
        public string DashboardType { get; set; } = null!;
        public DateTime AsOfDate { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<DashboardWidgetDto>? Widgets { get; set; }
        public List<AlertDto>? Alerts { get; set; }
        public object? Trends { get; set; }
        public object? Forecasts { get; set; }
    }

    public class PredictiveAnalyticsDto
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public string PredictionType { get; set; } = null!;
        public int ForecastDays { get; set; }
        public decimal ConfidenceLevel { get; set; }
        public DateTime GeneratedAt { get; set; }
        public List<ForecastDataPointDto>? Forecasts { get; set; }
        public List<ScenarioDto>? Scenarios { get; set; }
        public object? ModelMetrics { get; set; }
    }

    public class CarrierPerformanceInsightsDto
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateRangeDto AnalysisPeriod { get; set; } = null!;
        public DateTime GeneratedAt { get; set; }
        public decimal OverallScore { get; set; }
        public decimal OnTimeDeliveryRate { get; set; }
        public decimal AverageTransitTime { get; set; }
        public decimal ExceptionRate { get; set; }
        public decimal CustomerSatisfactionScore { get; set; }
        public List<CarrierRankingDto>? TopPerformingCarriers { get; set; }
        public List<AlertDto>? PerformanceAlerts { get; set; }
        public List<RecommendationDto>? ImprovementRecommendations { get; set; }
    }

    public class CarrierCostAnalysisDto
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateRangeDto AnalysisPeriod { get; set; } = null!;
        public DateTime GeneratedAt { get; set; }
        public decimal TotalCost { get; set; }
        public decimal AverageCostPerShipment { get; set; }
        public int TotalShipments { get; set; }
        public Dictionary<string, decimal>? CostBreakdown { get; set; }
        public List<TrendDataDto>? CostTrends { get; set; }
        public List<CostSavingOpportunityDto>? CostSavingOpportunities { get; set; }
        public List<CarrierCostComparisonDto>? CarrierCostComparison { get; set; }
    }

    public class CarrierVolumeAnalysisDto
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateRangeDto AnalysisPeriod { get; set; } = null!;
        public DateTime GeneratedAt { get; set; }
        public int TotalShipments { get; set; }
        public decimal TotalWeight { get; set; }
        public int TotalPackages { get; set; }
        public Dictionary<string, int>? VolumeByCarrier { get; set; }
        public Dictionary<string, int>? VolumeByServiceLevel { get; set; }
        public Dictionary<string, int>? VolumeByRoute { get; set; }
        public List<TrendDataDto>? VolumeTrends { get; set; }
        public decimal CapacityUtilization { get; set; }
    }

    public class CarrierBenchmarkDto
    {
        public Guid OrganizationId { get; set; }
        public List<Guid> CarrierIds { get; set; } = new();
        public string BenchmarkType { get; set; } = null!;
        public DateRangeDto BenchmarkPeriod { get; set; } = null!;
        public DateTime GeneratedAt { get; set; }
        public List<CarrierBenchmarkResultDto>? Results { get; set; }
        public object? IndustryBenchmarks { get; set; }
    }

    public class CarrierComparisonDto
    {
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public Dictionary<string, decimal> Metrics { get; set; } = new();
        public string PerformanceGrade { get; set; } = null!;
        public int Ranking { get; set; }
    }

    // Supporting DTOs
    public class AlertDto
    {
        public string Type { get; set; } = null!;
        public string Severity { get; set; } = null!;
        public string Message { get; set; } = null!;
        public DateTime CreatedAt { get; set; }
        public Dictionary<string, object>? Data { get; set; }
    }

    public class PredictionDto
    {
        public string MetricName { get; set; } = null!;
        public string PredictionType { get; set; } = null!;
        public int ForecastPeriod { get; set; }
        public decimal PredictedValue { get; set; }
        public decimal ConfidenceLevel { get; set; }
        public decimal UpperBound { get; set; }
        public decimal LowerBound { get; set; }
        public string Methodology { get; set; } = null!;
    }

    public class ComparisonDto
    {
        public string MetricName { get; set; } = null!;
        public string ComparisonType { get; set; } = null!;
        public decimal CurrentValue { get; set; }
        public decimal ComparisonValue { get; set; }
        public decimal PercentChange { get; set; }
        public string Trend { get; set; } = null!;
        public string Significance { get; set; } = null!;
    }

    public class DashboardWidgetDto
    {
        public string Type { get; set; } = null!;
        public string Title { get; set; } = null!;
        public object Data { get; set; } = null!;
        public Dictionary<string, object>? Configuration { get; set; }
    }

    public class ForecastDataPointDto
    {
        public DateTime Date { get; set; }
        public decimal Value { get; set; }
        public decimal UpperBound { get; set; }
        public decimal LowerBound { get; set; }
        public decimal Confidence { get; set; }
    }

    public class ScenarioDto
    {
        public string Name { get; set; } = null!;
        public string Description { get; set; } = null!;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public List<ForecastDataPointDto> Forecast { get; set; } = new();
    }

    public class CarrierRankingDto
    {
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public decimal Score { get; set; }
        public int Rank { get; set; }
        public Dictionary<string, decimal> Metrics { get; set; } = new();
    }

    public class RecommendationDto
    {
        public string Type { get; set; } = null!;
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public string Priority { get; set; } = null!;
        public decimal ExpectedImpact { get; set; }
        public string ActionRequired { get; set; } = null!;
    }

    public class TrendDataDto
    {
        public DateTime Date { get; set; }
        public decimal Value { get; set; }
        public string? Label { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    public class CostSavingOpportunityDto
    {
        public string Type { get; set; } = null!;
        public string Description { get; set; } = null!;
        public decimal EstimatedSavings { get; set; }
        public string Implementation { get; set; } = null!;
        public string Priority { get; set; } = null!;
    }

    public class CarrierCostComparisonDto
    {
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public decimal TotalCost { get; set; }
        public decimal AverageCostPerShipment { get; set; }
        public int ShipmentCount { get; set; }
        public decimal CostPerformanceIndex { get; set; }
    }

    public class CarrierBenchmarkResultDto
    {
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public Dictionary<string, decimal> Metrics { get; set; } = new();
        public Dictionary<string, decimal> BenchmarkComparison { get; set; } = new();
        public string OverallRating { get; set; } = null!;
    }

    public class MetricDto
    {
        public string MetricName { get; set; } = null!;
        public decimal Value { get; set; }
        public DateTime Date { get; set; }
        public string? Unit { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    public class DateRangeDto
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    // Performance Metrics DTOs
    public class PerformanceMetricsDto
    {
        public string MetricName { get; set; } = null!;
        public decimal Value { get; set; }
        public string Unit { get; set; } = null!;
        public DateTime MeasuredAt { get; set; }
    }

    public class CostMetricsDto
    {
        public string MetricName { get; set; } = null!;
        public decimal Value { get; set; }
        public string Currency { get; set; } = null!;
        public DateTime MeasuredAt { get; set; }
    }

    public class VolumeMetricsDto
    {
        public string MetricName { get; set; } = null!;
        public decimal Value { get; set; }
        public string Unit { get; set; } = null!;
        public DateTime MeasuredAt { get; set; }
    }

    public class QualityMetricsDto
    {
        public string MetricName { get; set; } = null!;
        public decimal Value { get; set; }
        public string Unit { get; set; } = null!;
        public DateTime MeasuredAt { get; set; }
    }
}
