using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Common;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Repositories;

namespace PricingManagement.Application.Contracts.Queries.GetContractById
{
    public class GetContractByIdQueryHandler : IRequestHandler<GetContractByIdQuery, OperationResultDto<ContractDto>>
    {
        private readonly IContractRepository _contractRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILogger<GetContractByIdQueryHandler> _logger;

        public GetContractByIdQueryHandler(
            IContractRepository contractRepository,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILogger<GetContractByIdQueryHandler> logger)
        {
            _contractRepository = contractRepository;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<OperationResultDto<ContractDto>> Handle(GetContractByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_currentUserService.OrganizationId.HasValue)
                {
                    return OperationResultDto<ContractDto>.Failure("Organization context is required");
                }

                var organizationId = _currentUserService.OrganizationId.Value;

                var contract = await _contractRepository.GetByIdAsync(request.Id, organizationId);
                if (contract == null)
                {
                    return OperationResultDto<ContractDto>.Failure("Contract not found");
                }

                var contractDto = _mapper.Map<ContractDto>(contract);

                return OperationResultDto<ContractDto>.Success(contractDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving contract {ContractId}", request.Id);
                return OperationResultDto<ContractDto>.Failure("An error occurred while retrieving the contract");
            }
        }
    }
}
