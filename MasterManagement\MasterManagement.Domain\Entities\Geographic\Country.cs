using System;
using System.Collections.Generic;
using System.Linq;
using MasterManagement.Domain.Common;
using MasterManagement.Domain.Exceptions;
using MasterManagement.Domain.Events.Geographic;
using MasterManagement.Domain.ValueObjects;

namespace MasterManagement.Domain.Entities.Geographic
{
    public class Country : AggregateRoot
    {
        public string Name { get; private set; }
        public string Code { get; private set; } // ISO 3166-1 alpha-2
        public string Code3 { get; private set; } // ISO 3166-1 alpha-3
        public string NumericCode { get; private set; } // ISO 3166-1 numeric
        public string OfficialName { get; private set; }
        public string Currency { get; private set; }
        public string CurrencySymbol { get; private set; }
        public string PhonePrefix { get; private set; }
        public string TimeZone { get; private set; }
        public string Language { get; private set; }
        public Guid ContinentId { get; private set; }
        public Guid? RegionId { get; private set; }
        public bool IsActive { get; private set; }
        public Coordinates? Coordinates { get; private set; }
        
        // Navigation properties
        public Continent Continent { get; private set; }
        public Region Region { get; private set; }
        
        private readonly List<State> _states = new();
        public IReadOnlyCollection<State> States => _states.AsReadOnly();

        private Country() { }

        public Country(
            string name, 
            string code, 
            string code3, 
            string numericCode,
            string officialName,
            string currency,
            string currencySymbol,
            string phonePrefix,
            string timeZone,
            string language,
            Guid continentId,
            Guid? regionId = null,
            Coordinates? coordinates = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Country name cannot be empty");

            if (string.IsNullOrWhiteSpace(code) || code.Length != 2)
                throw new DomainException("Country code must be 2 characters");

            if (string.IsNullOrWhiteSpace(code3) || code3.Length != 3)
                throw new DomainException("Country code3 must be 3 characters");

            if (continentId == Guid.Empty)
                throw new DomainException("Continent ID cannot be empty");

            Name = name;
            Code = code.ToUpperInvariant();
            Code3 = code3.ToUpperInvariant();
            NumericCode = numericCode;
            OfficialName = officialName ?? name;
            Currency = currency ?? string.Empty;
            CurrencySymbol = currencySymbol ?? string.Empty;
            PhonePrefix = phonePrefix ?? string.Empty;
            TimeZone = timeZone ?? string.Empty;
            Language = language ?? string.Empty;
            ContinentId = continentId;
            RegionId = regionId;
            IsActive = true;
            Coordinates = coordinates;

            AddDomainEvent(new CountryCreatedEvent(Id, Name, Code, ContinentId));
        }

        public void UpdateDetails(
            string name, 
            string officialName, 
            string currency, 
            string currencySymbol,
            string phonePrefix,
            string timeZone,
            string language,
            Coordinates? coordinates)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            OfficialName = officialName ?? OfficialName;
            Currency = currency ?? Currency;
            CurrencySymbol = currencySymbol ?? CurrencySymbol;
            PhonePrefix = phonePrefix ?? PhonePrefix;
            TimeZone = timeZone ?? TimeZone;
            Language = language ?? Language;
            Coordinates = coordinates ?? Coordinates;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new CountryUpdatedEvent(Id, Name, Code));
        }

        public void AddState(State state)
        {
            if (state == null)
                throw new DomainException("State cannot be null");

            if (_states.Any(s => s.Code == state.Code))
                throw new DomainException($"State with code {state.Code} already exists");

            _states.Add(state);
        }

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
