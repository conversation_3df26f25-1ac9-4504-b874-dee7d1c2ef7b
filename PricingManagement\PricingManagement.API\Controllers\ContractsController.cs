using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Contracts.Commands.CreateContract;
using PricingManagement.Application.Contracts.Queries.GetContracts;
using PricingManagement.Application.Contracts.Queries.GetContractById;

namespace PricingManagement.API.Controllers
{
    /// <summary>
    /// Contract management endpoints
    /// </summary>
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [Authorize]
    public class ContractsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<ContractsController> _logger;

        public ContractsController(IMediator mediator, ILogger<ContractsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get all contracts with optional filtering and pagination
        /// </summary>
        /// <param name="query">Query parameters for filtering and pagination</param>
        /// <returns>Paginated list of contracts</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResultDto<ContractDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<PagedResultDto<ContractDto>>> GetContracts([FromQuery] GetContractsQuery query)
        {
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get a specific contract by ID
        /// </summary>
        /// <param name="id">Contract ID</param>
        /// <returns>Contract details</returns>
        [HttpGet("{id:guid}")]
        [ProducesResponseType(typeof(OperationResultDto<ContractDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<ContractDto>>> GetContract(Guid id)
        {
            var result = await _mediator.Send(new GetContractByIdQuery(id));
            
            if (!result.IsSuccess)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Create a new contract
        /// </summary>
        /// <param name="command">Contract creation data</param>
        /// <returns>Created contract</returns>
        [HttpPost]
        [ProducesResponseType(typeof(OperationResultDto<ContractDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<ContractDto>>> CreateContract([FromBody] CreateContractCommand command)
        {
            var result = await _mediator.Send(command);
            
            if (!result.IsSuccess)
            {
                return BadRequest(result);
            }

            return CreatedAtAction(
                nameof(GetContract),
                new { id = result.Data!.Id },
                result);
        }

        /// <summary>
        /// Activate a contract
        /// </summary>
        /// <param name="id">Contract ID</param>
        /// <returns>Operation result</returns>
        [HttpPost("{id:guid}/activate")]
        [ProducesResponseType(typeof(OperationResultDto<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<bool>>> ActivateContract(Guid id)
        {
            try
            {
                // TODO: Implement ActivateContractCommand
                return BadRequest("Activate contract functionality not yet implemented");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating contract {ContractId}", id);
                return StatusCode(500, new { message = "An error occurred while activating the contract" });
            }
        }

        /// <summary>
        /// Suspend a contract
        /// </summary>
        /// <param name="id">Contract ID</param>
        /// <param name="reason">Suspension reason</param>
        /// <returns>Operation result</returns>
        [HttpPost("{id:guid}/suspend")]
        [ProducesResponseType(typeof(OperationResultDto<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<bool>>> SuspendContract(Guid id, [FromBody] string reason)
        {
            try
            {
                // TODO: Implement SuspendContractCommand
                return BadRequest("Suspend contract functionality not yet implemented");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error suspending contract {ContractId}", id);
                return StatusCode(500, new { message = "An error occurred while suspending the contract" });
            }
        }

        /// <summary>
        /// Terminate a contract
        /// </summary>
        /// <param name="id">Contract ID</param>
        /// <param name="reason">Termination reason</param>
        /// <returns>Operation result</returns>
        [HttpPost("{id:guid}/terminate")]
        [ProducesResponseType(typeof(OperationResultDto<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<bool>>> TerminateContract(Guid id, [FromBody] string reason)
        {
            try
            {
                // TODO: Implement TerminateContractCommand
                return BadRequest("Terminate contract functionality not yet implemented");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error terminating contract {ContractId}", id);
                return StatusCode(500, new { message = "An error occurred while terminating the contract" });
            }
        }

        /// <summary>
        /// Get active contract for a customer
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <returns>Active contract for customer</returns>
        [HttpGet("customer/{customerId:guid}/active")]
        [ProducesResponseType(typeof(OperationResultDto<ContractDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<ContractDto>>> GetActiveContractForCustomer(Guid customerId)
        {
            try
            {
                // TODO: Implement GetActiveContractForCustomerQuery
                return BadRequest("Get active contract functionality not yet implemented");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active contract for customer {CustomerId}", customerId);
                return StatusCode(500, new { message = "An error occurred while retrieving the active contract" });
            }
        }

        /// <summary>
        /// Get active contract for a shipper
        /// </summary>
        /// <param name="shipperId">Shipper ID</param>
        /// <returns>Active contract for shipper</returns>
        [HttpGet("shipper/{shipperId:guid}/active")]
        [ProducesResponseType(typeof(OperationResultDto<ContractDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<ContractDto>>> GetActiveContractForShipper(Guid shipperId)
        {
            try
            {
                // TODO: Implement GetActiveContractForShipperQuery
                return BadRequest("Get active contract functionality not yet implemented");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active contract for shipper {ShipperId}", shipperId);
                return StatusCode(500, new { message = "An error occurred while retrieving the active contract" });
            }
        }

        /// <summary>
        /// Get contracts expiring soon
        /// </summary>
        /// <param name="days">Number of days ahead to check for expiring contracts</param>
        /// <returns>List of expiring contracts</returns>
        [HttpGet("expiring")]
        [ProducesResponseType(typeof(List<ContractDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<List<ContractDto>>> GetExpiringContracts([FromQuery] int days = 30)
        {
            try
            {
                // TODO: Implement GetExpiringContractsQuery
                return BadRequest("Get expiring contracts functionality not yet implemented");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting expiring contracts");
                return StatusCode(500, new { message = "An error occurred while retrieving expiring contracts" });
            }
        }
    }
}
