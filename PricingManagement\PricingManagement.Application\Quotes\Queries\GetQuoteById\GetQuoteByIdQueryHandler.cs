using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Common;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Repositories;

namespace PricingManagement.Application.Quotes.Queries.GetQuoteById
{
    public class GetQuoteByIdQueryHandler : IRequestHandler<GetQuoteByIdQuery, OperationResultDto<QuoteDto>>
    {
        private readonly IQuoteRepository _quoteRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILogger<GetQuoteByIdQueryHandler> _logger;

        public GetQuoteByIdQueryHandler(
            IQuoteRepository quoteRepository,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILogger<GetQuoteByIdQueryHandler> logger)
        {
            _quoteRepository = quoteRepository;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<OperationResultDto<QuoteDto>> Handle(GetQuoteByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_currentUserService.OrganizationId.HasValue)
                {
                    return OperationResultDto<QuoteDto>.Failure("Organization context is required");
                }

                var organizationId = _currentUserService.OrganizationId.Value;

                var quote = await _quoteRepository.GetByIdAsync(request.Id, organizationId);
                if (quote == null)
                {
                    return OperationResultDto<QuoteDto>.Failure("Quote not found");
                }

                var quoteDto = _mapper.Map<QuoteDto>(quote);

                return OperationResultDto<QuoteDto>.Success(quoteDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving quote {QuoteId}", request.Id);
                return OperationResultDto<QuoteDto>.Failure("An error occurred while retrieving the quote");
            }
        }
    }
}
