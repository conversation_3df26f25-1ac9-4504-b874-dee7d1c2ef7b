using Microsoft.AspNetCore.Mvc;
using ShipperManagement.Application.DTOs;
using ShipperManagement.Application.Shippers.Commands.CreateShipper;
using ShipperManagement.Application.Shippers.Queries.GetShipper;
using ShipperManagement.Application.Shippers.Queries.GetShippers;
using System.ComponentModel.DataAnnotations;

namespace ShipperManagement.API.Controllers
{
    /// <summary>
    /// Shipper management endpoints
    /// </summary>
    [ApiVersion("1.0")]
    public class ShippersController : BaseApiController
    {
        /// <summary>
        /// Get all shippers with optional filtering and pagination
        /// </summary>
        /// <param name="searchCriteria">Search and filter criteria</param>
        /// <returns>Paginated list of shippers</returns>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetShippers([FromQuery] ShipperSearchDto searchCriteria)
        {
            var query = new GetShippersQuery(searchCriteria);
            var result = await Mediator.Send(query);
            return HandleResult(result);
        }

        /// <summary>
        /// Get a specific shipper by ID
        /// </summary>
        /// <param name="id">Shipper ID</param>
        /// <returns>Shipper details</returns>
        [HttpGet("{id:guid}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetShipper([Required] Guid id)
        {
            var query = new GetShipperQuery(id);
            var result = await Mediator.Send(query);
            return HandleResult(result);
        }

        /// <summary>
        /// Create a new shipper
        /// </summary>
        /// <param name="createShipperDto">Shipper creation data</param>
        /// <returns>Created shipper ID</returns>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> CreateShipper([FromBody] CreateShipperDto createShipperDto)
        {
            var command = new CreateShipperCommand { Shipper = createShipperDto };
            var result = await Mediator.Send(command);

            if (result.IsSuccess)
            {
                return CreatedAtAction(nameof(GetShipper), new { id = result.Value }, result.Value);
            }

            return HandleResult(result);
        }

        /// <summary>
        /// Update an existing shipper
        /// </summary>
        /// <param name="id">Shipper ID</param>
        /// <param name="updateShipperDto">Shipper update data</param>
        /// <returns>Success result</returns>
        [HttpPut("{id:guid}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> UpdateShipper([Required] Guid id, [FromBody] UpdateShipperDto updateShipperDto)
        {
            // TODO: Implement UpdateShipperCommand
            return Ok(new { message = "Update shipper endpoint - to be implemented" });
        }

        /// <summary>
        /// Change shipper status
        /// </summary>
        /// <param name="id">Shipper ID</param>
        /// <param name="status">New status</param>
        /// <param name="reason">Reason for status change</param>
        /// <returns>Success result</returns>
        [HttpPatch("{id:guid}/status")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> ChangeShipperStatus(
            [Required] Guid id,
            [FromQuery, Required] string status,
            [FromQuery] string? reason = null)
        {
            // TODO: Implement ChangeShipperStatusCommand
            return Ok(new { message = "Change shipper status endpoint - to be implemented" });
        }

        /// <summary>
        /// Delete a shipper (soft delete)
        /// </summary>
        /// <param name="id">Shipper ID</param>
        /// <returns>Success result</returns>
        [HttpDelete("{id:guid}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> DeleteShipper([Required] Guid id)
        {
            // TODO: Implement DeleteShipperCommand
            return Ok(new { message = "Delete shipper endpoint - to be implemented" });
        }

        /// <summary>
        /// Get shipper statistics
        /// </summary>
        /// <returns>Shipper statistics</returns>
        [HttpGet("statistics")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> GetShipperStatistics()
        {
            // TODO: Implement GetShipperStatisticsQuery
            return Ok(new { message = "Shipper statistics endpoint - to be implemented" });
        }

        /// <summary>
        /// Export shippers to CSV
        /// </summary>
        /// <param name="searchCriteria">Export criteria</param>
        /// <returns>CSV file</returns>
        [HttpGet("export")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<IActionResult> ExportShippers([FromQuery] ShipperSearchDto searchCriteria)
        {
            // TODO: Implement ExportShippersQuery
            return Ok(new { message = "Export shippers endpoint - to be implemented" });
        }
    }
}
