using CarrierIntegration.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CarrierIntegration.Infrastructure.Persistence.Configurations
{
    public class CarrierRelationshipScoreConfiguration : IEntityTypeConfiguration<CarrierRelationshipScore>
    {
        public void Configure(EntityTypeBuilder<CarrierRelationshipScore> builder)
        {
            builder.ToTable("CarrierRelationshipScores");

            builder.<PERSON><PERSON>ey(crs => crs.Id);

            builder.Property(crs => crs.Id)
                .ValueGeneratedOnAdd();

            builder.Property(crs => crs.CalculationMethod)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(crs => crs.DataSources)
                .IsRequired()
                .HasMaxLength(500);

            builder.Property(crs => crs.ScoreGrade)
                .IsRequired()
                .HasMaxLength(10);

            builder.Property(crs => crs.ScoreTrend)
                .IsRequired()
                .HasMaxLength(20);

            // Decimal properties with precision
            builder.Property(crs => crs.OverallScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.PerformanceScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.ReliabilityScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.CostEffectivenessScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.ServiceQualityScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.ComplianceScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.CommunicationScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.InnovationScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.StrategicValueScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.OnTimeDeliveryRate)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.DamageRate)
                .HasColumnType("decimal(5,4)");

            builder.Property(crs => crs.ClaimResolutionTime)
                .HasColumnType("decimal(8,2)");

            builder.Property(crs => crs.CustomerSatisfactionScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.ServiceLevelCompliance)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.CostCompetitiveness)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.VolumeCommitmentCompliance)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.PaymentTermsCompliance)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.TotalSpend)
                .HasColumnType("decimal(18,2)");

            builder.Property(crs => crs.CostSavingsAchieved)
                .HasColumnType("decimal(18,2)");

            builder.Property(crs => crs.AverageIssueResolutionTime)
                .HasColumnType("decimal(8,2)");

            builder.Property(crs => crs.CapacityUtilization)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.ServiceExpansionRate)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.TechnologyAdoptionScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.SustainabilityScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.RiskScore)
                .HasColumnType("decimal(5,2)");

            builder.Property(crs => crs.ConfidenceLevel)
                .HasColumnType("decimal(5,2)");

            // Text properties
            builder.Property(crs => crs.StrengthAreas)
                .HasMaxLength(1000);

            builder.Property(crs => crs.ImprovementAreas)
                .HasMaxLength(1000);

            builder.Property(crs => crs.RecommendedActions)
                .HasMaxLength(2000);

            builder.Property(crs => crs.RiskFactors)
                .HasMaxLength(1000);

            builder.Property(crs => crs.OpportunityAreas)
                .HasMaxLength(1000);

            builder.Property(crs => crs.NextReviewDate)
                .HasMaxLength(50);

            builder.Property(crs => crs.Notes)
                .HasMaxLength(2000);

            // Indexes
            builder.HasIndex(crs => new { crs.OrganizationId, crs.CarrierId, crs.CalculationDate })
                .HasDatabaseName("IX_CarrierRelationshipScores_OrgId_CarrierId_CalcDate");

            builder.HasIndex(crs => crs.CarrierId)
                .HasDatabaseName("IX_CarrierRelationshipScores_CarrierId");

            builder.HasIndex(crs => crs.CalculationDate)
                .HasDatabaseName("IX_CarrierRelationshipScores_CalculationDate");

            builder.HasIndex(crs => crs.OverallScore)
                .HasDatabaseName("IX_CarrierRelationshipScores_OverallScore");

            // Relationships
            builder.HasOne(crs => crs.Carrier)
                .WithMany(c => c.RelationshipScores)
                .HasForeignKey(crs => crs.CarrierId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
