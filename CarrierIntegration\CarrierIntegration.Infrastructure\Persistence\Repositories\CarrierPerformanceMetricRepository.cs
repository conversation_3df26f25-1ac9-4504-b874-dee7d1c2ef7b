using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Persistence.Repositories
{
    public class CarrierPerformanceMetricRepository : BaseRepository<CarrierPerformanceMetric>, ICarrierPerformanceMetricRepository
    {
        public CarrierPerformanceMetricRepository(CarrierIntegrationDbContext context) : base(context)
        {
        }

        public async Task<List<CarrierPerformanceMetric>> GetByCarrierIdAsync(Guid carrierId)
        {
            return await _context.CarrierPerformanceMetrics
                .Where(cpm => cpm.CarrierId == carrierId)
                .OrderByDescending(cpm => cpm.MeasuredAt)
                .ToListAsync();
        }

        public async Task<List<CarrierPerformanceMetric>> GetByOrganizationIdAsync(Guid organizationId)
        {
            return await _context.CarrierPerformanceMetrics
                .Include(cpm => cpm.Carrier)
                .Where(cpm => cpm.OrganizationId == organizationId)
                .OrderByDescending(cpm => cpm.MeasuredAt)
                .ToListAsync();
        }

        public async Task<List<CarrierPerformanceMetric>> GetByPeriodAsync(Guid organizationId, DateTime startDate, DateTime endDate)
        {
            return await _context.CarrierPerformanceMetrics
                .Include(cpm => cpm.Carrier)
                .Where(cpm => cpm.OrganizationId == organizationId && 
                             cpm.MeasuredAt >= startDate && 
                             cpm.MeasuredAt <= endDate)
                .OrderByDescending(cpm => cpm.MeasuredAt)
                .ToListAsync();
        }

        public async Task<List<CarrierPerformanceMetric>> GetByOrganizationAndPeriodAsync(Guid organizationId, DateTime startDate, DateTime endDate, Guid? carrierId = null)
        {
            var query = _context.CarrierPerformanceMetrics
                .Include(cpm => cpm.Carrier)
                .Where(cpm => cpm.OrganizationId == organizationId && 
                             cpm.MeasuredAt >= startDate && 
                             cpm.MeasuredAt <= endDate);

            if (carrierId.HasValue)
                query = query.Where(cpm => cpm.CarrierId == carrierId.Value);

            return await query
                .OrderByDescending(cpm => cpm.MeasuredAt)
                .ToListAsync();
        }

        public async Task<List<CarrierPerformanceMetric>> GetByMetricTypeAsync(Guid organizationId, string metricType)
        {
            return await _context.CarrierPerformanceMetrics
                .Include(cpm => cpm.Carrier)
                .Where(cpm => cpm.OrganizationId == organizationId && cpm.MetricType == metricType)
                .OrderByDescending(cpm => cpm.MeasuredAt)
                .ToListAsync();
        }

        public async Task<CarrierPerformanceMetric?> GetLatestMetricAsync(Guid carrierId, string metricType)
        {
            return await _context.CarrierPerformanceMetrics
                .Include(cpm => cpm.Carrier)
                .Where(cpm => cpm.CarrierId == carrierId && cpm.MetricType == metricType)
                .OrderByDescending(cpm => cpm.MeasuredAt)
                .FirstOrDefaultAsync();
        }
    }
}
