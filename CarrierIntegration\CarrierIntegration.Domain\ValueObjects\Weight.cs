using CarrierIntegration.Domain.Common;
using System;
using System.Collections.Generic;

namespace CarrierIntegration.Domain.ValueObjects
{
    public class Weight : ValueObject
    {
        public decimal Value { get; private set; }
        public string Unit { get; private set; } // LBS, KG, OZ, G

        private Weight() { } // For EF Core

        public Weight(decimal value, string unit = "LBS")
        {
            if (value < 0)
                throw new DomainException("Weight cannot be negative");
            if (string.IsNullOrWhiteSpace(unit))
                throw new DomainException("Unit is required");

            var validUnits = new[] { "LBS", "KG", "OZ", "G" };
            if (!Array.Exists(validUnits, u => u.Equals(unit, StringComparison.OrdinalIgnoreCase)))
                throw new DomainException($"Invalid unit. Valid units are: {string.Join(", ", validUnits)}");

            Value = Math.Round(value, 3);
            Unit = unit.ToUpperInvariant();
        }

        public Weight ConvertTo(string targetUnit)
        {
            if (Unit.Equals(targetUnit, StringComparison.OrdinalIgnoreCase))
                return this;

            var conversionFactor = GetConversionFactor(Unit, targetUnit);
            return new Weight(Value * conversionFactor, targetUnit);
        }

        private static decimal GetConversionFactor(string fromUnit, string toUnit)
        {
            var from = fromUnit.ToUpperInvariant();
            var to = toUnit.ToUpperInvariant();

            return (from, to) switch
            {
                ("LBS", "KG") => 0.453592m,
                ("KG", "LBS") => 2.20462m,
                ("LBS", "OZ") => 16m,
                ("OZ", "LBS") => 0.0625m,
                ("KG", "G") => 1000m,
                ("G", "KG") => 0.001m,
                ("OZ", "G") => 28.3495m,
                ("G", "OZ") => 0.035274m,
                _ when from == to => 1m,
                _ => throw new DomainException($"Conversion from {from} to {to} is not supported")
            };
        }

        public Weight Add(Weight other)
        {
            var otherConverted = other.ConvertTo(Unit);
            return new Weight(Value + otherConverted.Value, Unit);
        }

        public Weight Subtract(Weight other)
        {
            var otherConverted = other.ConvertTo(Unit);
            return new Weight(Value - otherConverted.Value, Unit);
        }

        public bool IsGreaterThan(Weight other)
        {
            var otherConverted = other.ConvertTo(Unit);
            return Value > otherConverted.Value;
        }

        public bool IsLessThan(Weight other)
        {
            var otherConverted = other.ConvertTo(Unit);
            return Value < otherConverted.Value;
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Value;
            yield return Unit;
        }

        public override string ToString()
        {
            return $"{Value} {Unit}";
        }
    }
}
