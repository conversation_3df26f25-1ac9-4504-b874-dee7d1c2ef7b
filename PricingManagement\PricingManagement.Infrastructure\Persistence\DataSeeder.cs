using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using System.Text.Json;

namespace PricingManagement.Infrastructure.Persistence
{
    public class DataSeeder
    {
        private readonly PricingManagementDbContext _context;
        private readonly ILogger<DataSeeder> _logger;

        public DataSeeder(PricingManagementDbContext context, ILogger<DataSeeder> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task SeedAsync()
        {
            try
            {
                _logger.LogInformation("Starting data seeding for Pricing Management");

                // Ensure database is created
                await _context.Database.EnsureCreatedAsync();

                // Check if data already exists
                if (await _context.PricingRules.AnyAsync())
                {
                    _logger.LogInformation("Data already exists, skipping seeding");
                    return;
                }

                // Seed data for multiple organizations
                var organizationIds = new[]
                {
                    Guid.Parse("11111111-1111-1111-1111-111111111111"), // Demo Organization 1
                    Guid.Parse("*************-2222-2222-************"), // Demo Organization 2
                    Guid.Parse("*************-3333-3333-************")  // Demo Organization 3
                };

                foreach (var orgId in organizationIds)
                {
                    await SeedOrganizationDataAsync(orgId);
                }

                await _context.SaveChangesAsync();
                _logger.LogInformation("Data seeding completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during data seeding");
                throw;
            }
        }

        private async Task SeedOrganizationDataAsync(Guid organizationId)
        {
            _logger.LogInformation("Seeding data for organization {OrganizationId}", organizationId);

            // Seed Pricing Rules
            await SeedPricingRulesAsync(organizationId);

            // Seed Contracts
            await SeedContractsAsync(organizationId);

            // Seed Quotes
            await SeedQuotesAsync(organizationId);
        }

        private async Task SeedPricingRulesAsync(Guid organizationId)
        {
            var pricingRules = new List<PricingRule>
            {
                // Base Rate Rules
                new PricingRule(
                    "Standard Ground Base Rate",
                    "Base rate for standard ground shipping",
                    PricingRuleType.BaseRate,
                    1,
                    DateTime.UtcNow.AddDays(-30),
                    DateTime.UtcNow.AddYears(1),
                    organizationId,
                    "System")
                {
                    RuleConfiguration = JsonSerializer.Serialize(new { baseRate = 15.00m }),
                    ServiceTypes = JsonSerializer.Serialize(new[] { "Ground", "Standard" }),
                    Tags = JsonSerializer.Serialize(new[] { "base", "ground", "standard" })
                },

                // Weight-Based Rules
                new PricingRule(
                    "Weight-Based Pricing",
                    "Pricing based on package weight",
                    PricingRuleType.WeightBased,
                    2,
                    DateTime.UtcNow.AddDays(-30),
                    DateTime.UtcNow.AddYears(1),
                    organizationId,
                    "System")
                {
                    RuleConfiguration = JsonSerializer.Serialize(new { ratePerPound = 2.50m }),
                    ServiceTypes = JsonSerializer.Serialize(new[] { "Ground", "Express", "Overnight" }),
                    Tags = JsonSerializer.Serialize(new[] { "weight", "variable" })
                },

                // Distance-Based Rules
                new PricingRule(
                    "Distance-Based Pricing",
                    "Pricing based on shipping distance",
                    PricingRuleType.DistanceBased,
                    3,
                    DateTime.UtcNow.AddDays(-30),
                    DateTime.UtcNow.AddYears(1),
                    organizationId,
                    "System")
                {
                    RuleConfiguration = JsonSerializer.Serialize(new { ratePerMile = 0.75m }),
                    ServiceTypes = JsonSerializer.Serialize(new[] { "Ground", "Express" }),
                    Tags = JsonSerializer.Serialize(new[] { "distance", "mileage" })
                },

                // Zone-Based Rules
                new PricingRule(
                    "Zone-Based Pricing",
                    "Pricing based on origin and destination zones",
                    PricingRuleType.ZoneBased,
                    4,
                    DateTime.UtcNow.AddDays(-30),
                    DateTime.UtcNow.AddYears(1),
                    organizationId,
                    "System")
                {
                    RuleConfiguration = JsonSerializer.Serialize(new
                    {
                        zoneRates = new Dictionary<string, decimal>
                        {
                            ["Zone1-Zone1"] = 12.50m,
                            ["Zone1-Zone2"] = 18.75m,
                            ["Zone1-Zone3"] = 25.00m,
                            ["Zone2-Zone2"] = 15.00m,
                            ["Zone2-Zone3"] = 22.50m,
                            ["Zone3-Zone3"] = 20.00m
                        }
                    }),
                    ServiceTypes = JsonSerializer.Serialize(new[] { "Ground", "Express" }),
                    Tags = JsonSerializer.Serialize(new[] { "zone", "regional" })
                },

                // Service Level Rules
                new PricingRule(
                    "Express Service Premium",
                    "Premium pricing for express services",
                    PricingRuleType.ServiceLevel,
                    5,
                    DateTime.UtcNow.AddDays(-30),
                    DateTime.UtcNow.AddYears(1),
                    organizationId,
                    "System")
                {
                    RuleConfiguration = JsonSerializer.Serialize(new
                    {
                        serviceLevelRates = new Dictionary<string, decimal>
                        {
                            ["Express"] = 35.00m,
                            ["Overnight"] = 65.00m,
                            ["Same Day"] = 125.00m
                        }
                    }),
                    ServiceTypes = JsonSerializer.Serialize(new[] { "Express", "Overnight", "Same Day" }),
                    Tags = JsonSerializer.Serialize(new[] { "premium", "express", "fast" })
                }
            };

            // Add tiered pricing to weight-based rule
            var weightRule = pricingRules.First(r => r.RuleType == PricingRuleType.WeightBased);
            weightRule.AddTier(0, 10, 2.50m, "per_unit", "System");
            weightRule.AddTier(10, 50, 2.25m, "per_unit", "System");
            weightRule.AddTier(50, 100, 2.00m, "per_unit", "System");
            weightRule.AddTier(100, decimal.MaxValue, 1.75m, "per_unit", "System");

            await _context.PricingRules.AddRangeAsync(pricingRules);
        }

        private async Task SeedContractsAsync(Guid organizationId)
        {
            var contracts = new List<Contract>
            {
                new Contract(
                    $"C{DateTime.UtcNow:yyyyMMdd}-001",
                    "Enterprise Customer Contract",
                    "Volume discount contract for enterprise customers",
                    DateTime.UtcNow.AddDays(-60),
                    DateTime.UtcNow.AddYears(2),
                    CurrencyCode.USD,
                    organizationId,
                    "System")
                {
                    CustomerId = Guid.Parse("AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA"),
                    CustomerName = "Enterprise Corp",
                    Terms = "Net 30 payment terms with 2% early payment discount",
                    PaymentTerms = "Net 30",
                    MinimumCommitment = 50000m,
                    CommitmentType = "Annual Revenue",
                    AutoRenewal = true,
                    AutoRenewalPeriod = 12,
                    ServiceTypes = JsonSerializer.Serialize(new[] { "Ground", "Express", "Overnight" })
                },

                new Contract(
                    $"C{DateTime.UtcNow:yyyyMMdd}-002",
                    "Regional Shipper Agreement",
                    "Preferred rates for regional shipping partner",
                    DateTime.UtcNow.AddDays(-30),
                    DateTime.UtcNow.AddYears(1),
                    CurrencyCode.USD,
                    organizationId,
                    "System")
                {
                    ShipperId = Guid.Parse("BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB"),
                    ShipperName = "Regional Express LLC",
                    Terms = "Net 15 payment terms",
                    PaymentTerms = "Net 15",
                    MinimumCommitment = 25000m,
                    CommitmentType = "Annual Volume",
                    AutoRenewal = false,
                    ServiceTypes = JsonSerializer.Serialize(new[] { "Ground", "Express" })
                }
            };

            // Add rates to enterprise contract
            var enterpriseContract = contracts.First();
            enterpriseContract.AddRate("Ground", "Zone1", "Zone2", 0, 50, new Money(15.00m, CurrencyCode.USD), "Fixed", null, null, "System");
            enterpriseContract.AddRate("Express", "Zone1", "Zone2", 0, 50, new Money(25.00m, CurrencyCode.USD), "Fixed", null, null, "System");

            // Add discounts to enterprise contract
            enterpriseContract.AddDiscount(DiscountType.VolumeDiscount, "Volume discount for 1000+ lbs", 10m, null, 1000, null, null, null, "System");
            enterpriseContract.AddDiscount(DiscountType.Loyalty, "Loyalty discount", 5m, null, null, null, null, null, "System");

            // Add surcharges to regional contract
            var regionalContract = contracts.Last();
            regionalContract.AddSurcharge(SurchargeType.FuelSurcharge, "Fuel surcharge", new Money(5.00m, CurrencyCode.USD), "Per shipment", null, null, "System");

            await _context.Contracts.AddRangeAsync(contracts);
        }

        private async Task SeedQuotesAsync(Guid organizationId)
        {
            var quotes = new List<Quote>
            {
                new Quote(
                    $"Q{DateTime.UtcNow:yyyyMMdd}-001",
                    DateTime.UtcNow.AddDays(30),
                    CurrencyCode.USD,
                    organizationId,
                    "System")
                {
                    CustomerId = Guid.Parse("AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA"),
                    CustomerName = "Enterprise Corp",
                    OriginAddress = "123 Main St, Los Angeles, CA 90210",
                    DestinationAddress = "456 Oak Ave, New York, NY 10001",
                    ServiceType = "Express",
                    PackageWeight = new Weight(25.5m, WeightUnit.Pounds),
                    PackageDimensions = new Dimensions(12, 8, 6, DimensionUnit.Inches),
                    DeclaredValue = 500m,
                    Notes = "Fragile items - handle with care"
                },

                new Quote(
                    $"Q{DateTime.UtcNow:yyyyMMdd}-002",
                    DateTime.UtcNow.AddDays(15),
                    CurrencyCode.USD,
                    organizationId,
                    "System")
                {
                    ShipperId = Guid.Parse("BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB"),
                    ShipperName = "Regional Express LLC",
                    OriginAddress = "789 Industrial Blvd, Chicago, IL 60601",
                    DestinationAddress = "321 Commerce St, Dallas, TX 75201",
                    ServiceType = "Ground",
                    PackageWeight = new Weight(150m, WeightUnit.Pounds),
                    PackageDimensions = new Dimensions(24, 18, 12, DimensionUnit.Inches),
                    DeclaredValue = 2000m,
                    Notes = "Business delivery - dock available"
                }
            };

            // Add line items and pricing to first quote
            var quote1 = quotes.First();
            quote1.AddLineItem("Express Shipping", 1, new Money(35.00m, CurrencyCode.USD), "Shipping", "System");
            quote1.AddSurcharge(SurchargeType.FuelSurcharge, "Fuel surcharge", new Money(3.50m, CurrencyCode.USD), "Current fuel rates", "System");
            quote1.AddDiscount(DiscountType.Promotional, "New customer discount", new Money(5.00m, CurrencyCode.USD), 10m, "Promotional offer", "System");
            quote1.Send("System");

            // Add line items and pricing to second quote
            var quote2 = quotes.Last();
            quote2.AddLineItem("Ground Shipping", 1, new Money(45.00m, CurrencyCode.USD), "Shipping", "System");
            quote2.AddSurcharge(SurchargeType.Oversize, "Oversize surcharge", new Money(15.00m, CurrencyCode.USD), "Package exceeds standard dimensions", "System");

            await _context.Quotes.AddRangeAsync(quotes);
        }
    }
}
