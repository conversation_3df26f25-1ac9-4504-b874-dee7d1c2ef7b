using PricingManagement.Domain.Common;
using PricingManagement.Domain.Enums;
using System;

namespace PricingManagement.Domain.Events
{
    public class PricingRuleCreatedEvent : DomainEvent
    {
        public Guid PricingRuleId { get; }
        public string Name { get; }
        public PricingRuleType RuleType { get; }

        public PricingRuleCreatedEvent(Guid pricingRuleId, string name, PricingRuleType ruleType, Guid? organizationId)
            : base(organizationId)
        {
            PricingRuleId = pricingRuleId;
            Name = name;
            RuleType = ruleType;
        }
    }

    public class PricingRuleUpdatedEvent : DomainEvent
    {
        public Guid PricingRuleId { get; }
        public string Name { get; }

        public PricingRuleUpdatedEvent(Guid pricingRuleId, string name, Guid? organizationId)
            : base(organizationId)
        {
            PricingRuleId = pricingRuleId;
            Name = name;
        }
    }

    public class PricingRuleActivatedEvent : DomainEvent
    {
        public Guid PricingRuleId { get; }
        public string Name { get; }

        public PricingRuleActivatedEvent(Guid pricingRuleId, string name, Guid? organizationId)
            : base(organizationId)
        {
            PricingRuleId = pricingRuleId;
            Name = name;
        }
    }

    public class PricingRuleDeactivatedEvent : DomainEvent
    {
        public Guid PricingRuleId { get; }
        public string Name { get; }

        public PricingRuleDeactivatedEvent(Guid pricingRuleId, string name, Guid? organizationId)
            : base(organizationId)
        {
            PricingRuleId = pricingRuleId;
            Name = name;
        }
    }

    public class PricingRuleSuspendedEvent : DomainEvent
    {
        public Guid PricingRuleId { get; }
        public string Name { get; }
        public string Reason { get; }

        public PricingRuleSuspendedEvent(Guid pricingRuleId, string name, string reason, Guid? organizationId)
            : base(organizationId)
        {
            PricingRuleId = pricingRuleId;
            Name = name;
            Reason = reason;
        }
    }

    public class PricingRuleExpiredEvent : DomainEvent
    {
        public Guid PricingRuleId { get; }
        public string Name { get; }

        public PricingRuleExpiredEvent(Guid pricingRuleId, string name, Guid? organizationId)
            : base(organizationId)
        {
            PricingRuleId = pricingRuleId;
            Name = name;
        }
    }
}
