using PricingManagement.Domain.Common;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.Events;
using PricingManagement.Domain.Exceptions;
using PricingManagement.Domain.ValueObjects;
using System;
using System.Collections.Generic;

namespace PricingManagement.Domain.Entities
{
    public class Contract : BaseEntity
    {
        public string ContractNumber { get; private set; }
        public string Name { get; private set; }
        public string Description { get; private set; }
        public Guid? CustomerId { get; private set; }
        public Guid? ShipperId { get; private set; }
        public string? CustomerName { get; private set; }
        public string? ShipperName { get; private set; }
        public ContractStatus Status { get; private set; }
        public DateTime EffectiveDate { get; private set; }
        public DateTime ExpirationDate { get; private set; }
        public CurrencyCode Currency { get; private set; }
        public string? Terms { get; private set; }
        public string? PaymentTerms { get; private set; }
        public decimal? MinimumCommitment { get; private set; }
        public decimal? MaximumCommitment { get; private set; }
        public string? CommitmentType { get; private set; } // "volume", "revenue", "shipments"
        public decimal? CommitmentPeriod { get; private set; } // in months
        public bool AutoRenewal { get; private set; }
        public int? AutoRenewalPeriod { get; private set; } // in months
        public string? NotificationPeriod { get; private set; } // days before expiration
        public string? ServiceTypes { get; private set; } // JSON array
        public string? GeographicScope { get; private set; } // JSON object
        public string? SpecialProvisions { get; private set; }
        public string? ContractDocument { get; private set; } // file path or URL
        public DateTime? LastReviewDate { get; private set; }
        public DateTime? NextReviewDate { get; private set; }
        public string? ReviewNotes { get; private set; }
        public int Version { get; private set; }
        public Guid? ParentContractId { get; private set; } // for amendments

        private readonly List<ContractRate> _rates = new();
        public IReadOnlyCollection<ContractRate> Rates => _rates.AsReadOnly();

        private readonly List<ContractDiscount> _discounts = new();
        public IReadOnlyCollection<ContractDiscount> Discounts => _discounts.AsReadOnly();

        private readonly List<ContractSurcharge> _surcharges = new();
        public IReadOnlyCollection<ContractSurcharge> Surcharges => _surcharges.AsReadOnly();

        private readonly List<ContractCommitment> _commitments = new();
        public IReadOnlyCollection<ContractCommitment> Commitments => _commitments.AsReadOnly();

        protected Contract() { }

        public Contract(
            string contractNumber,
            string name,
            string description,
            DateTime effectiveDate,
            DateTime expirationDate,
            CurrencyCode currency,
            Guid organizationId,
            string createdBy) : base(organizationId, createdBy)
        {
            if (string.IsNullOrWhiteSpace(contractNumber))
                throw new DomainException("Contract number cannot be empty");

            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Contract name cannot be empty");

            if (string.IsNullOrWhiteSpace(description))
                throw new DomainException("Contract description cannot be empty");

            if (expirationDate <= effectiveDate)
                throw new DomainException("Expiration date must be after effective date");

            if (effectiveDate < DateTime.UtcNow.Date)
                throw new DomainException("Effective date cannot be in the past");

            ContractNumber = contractNumber;
            Name = name;
            Description = description;
            EffectiveDate = effectiveDate;
            ExpirationDate = expirationDate;
            Currency = currency;
            Status = ContractStatus.Draft;
            Version = 1;
            AutoRenewal = false;

            AddDomainEvent(new ContractCreatedEvent(Id, ContractNumber, Name, OrganizationId));
        }

        public void UpdateDetails(
            string name,
            string description,
            string? terms,
            string? paymentTerms,
            string? specialProvisions,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Contract name cannot be empty");

            if (string.IsNullOrWhiteSpace(description))
                throw new DomainException("Contract description cannot be empty");

            Name = name;
            Description = description;
            Terms = terms;
            PaymentTerms = paymentTerms;
            SpecialProvisions = specialProvisions;
            Update(updatedBy);

            AddDomainEvent(new ContractUpdatedEvent(Id, ContractNumber, Name, OrganizationId));
        }

        public void SetCustomer(Guid customerId, string customerName, string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(customerName))
                throw new DomainException("Customer name cannot be empty");

            CustomerId = customerId;
            CustomerName = customerName;
            Update(updatedBy);
        }

        public void SetShipper(Guid shipperId, string shipperName, string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(shipperName))
                throw new DomainException("Shipper name cannot be empty");

            ShipperId = shipperId;
            ShipperName = shipperName;
            Update(updatedBy);
        }

        public void SetCommitment(
            decimal? minimumCommitment,
            decimal? maximumCommitment,
            string? commitmentType,
            decimal? commitmentPeriod,
            string updatedBy)
        {
            if (minimumCommitment.HasValue && minimumCommitment < 0)
                throw new DomainException("Minimum commitment cannot be negative");

            if (maximumCommitment.HasValue && maximumCommitment < 0)
                throw new DomainException("Maximum commitment cannot be negative");

            if (minimumCommitment.HasValue && maximumCommitment.HasValue && minimumCommitment > maximumCommitment)
                throw new DomainException("Minimum commitment cannot be greater than maximum commitment");

            if (commitmentPeriod.HasValue && commitmentPeriod <= 0)
                throw new DomainException("Commitment period must be positive");

            MinimumCommitment = minimumCommitment;
            MaximumCommitment = maximumCommitment;
            CommitmentType = commitmentType;
            CommitmentPeriod = commitmentPeriod;
            Update(updatedBy);
        }

        public void SetAutoRenewal(bool autoRenewal, int? renewalPeriod, string? notificationPeriod, string updatedBy)
        {
            if (autoRenewal && (!renewalPeriod.HasValue || renewalPeriod <= 0))
                throw new DomainException("Auto renewal period must be positive when auto renewal is enabled");

            AutoRenewal = autoRenewal;
            AutoRenewalPeriod = renewalPeriod;
            NotificationPeriod = notificationPeriod;
            Update(updatedBy);
        }

        public void SetScope(string? serviceTypes, string? geographicScope, string updatedBy)
        {
            ServiceTypes = serviceTypes;
            GeographicScope = geographicScope;
            Update(updatedBy);
        }

        public void AddRate(
            string serviceType,
            string? originZone,
            string? destinationZone,
            decimal? minWeight,
            decimal? maxWeight,
            Money rate,
            string? rateType,
            DateTime? effectiveDate,
            DateTime? expirationDate,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(serviceType))
                throw new DomainException("Service type cannot be empty");

            if (rate.Currency != Currency)
                throw new InvalidCurrencyException($"Rate currency {rate.Currency} does not match contract currency {Currency}");

            if (minWeight.HasValue && minWeight < 0)
                throw new DomainException("Minimum weight cannot be negative");

            if (maxWeight.HasValue && maxWeight < 0)
                throw new DomainException("Maximum weight cannot be negative");

            if (minWeight.HasValue && maxWeight.HasValue && minWeight > maxWeight)
                throw new DomainException("Minimum weight cannot be greater than maximum weight");

            if (effectiveDate.HasValue && expirationDate.HasValue && effectiveDate >= expirationDate)
                throw new DomainException("Rate effective date must be before expiration date");

            var contractRate = new ContractRate(
                serviceType, originZone, destinationZone, minWeight, maxWeight,
                rate, rateType, effectiveDate, expirationDate);

            _rates.Add(contractRate);
            Update(updatedBy);
        }

        public void AddDiscount(
            DiscountType discountType,
            string description,
            decimal? percentage,
            Money? fixedAmount,
            decimal? minVolume,
            decimal? maxVolume,
            DateTime? effectiveDate,
            DateTime? expirationDate,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(description))
                throw new DomainException("Discount description cannot be empty");

            if (percentage.HasValue && (percentage < 0 || percentage > 100))
                throw new InvalidDiscountException("Percentage must be between 0 and 100");

            if (fixedAmount != null && fixedAmount.Currency != Currency)
                throw new InvalidCurrencyException($"Discount currency {fixedAmount.Currency} does not match contract currency {Currency}");

            if (minVolume.HasValue && minVolume < 0)
                throw new DomainException("Minimum volume cannot be negative");

            if (maxVolume.HasValue && maxVolume < 0)
                throw new DomainException("Maximum volume cannot be negative");

            if (minVolume.HasValue && maxVolume.HasValue && minVolume > maxVolume)
                throw new DomainException("Minimum volume cannot be greater than maximum volume");

            var discount = new ContractDiscount(
                discountType, description, percentage, fixedAmount,
                minVolume, maxVolume, effectiveDate, expirationDate);

            _discounts.Add(discount);
            Update(updatedBy);
        }

        public void AddSurcharge(
            SurchargeType surchargeType,
            string description,
            Money amount,
            string? calculationBasis,
            DateTime? effectiveDate,
            DateTime? expirationDate,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(description))
                throw new DomainException("Surcharge description cannot be empty");

            if (amount.Currency != Currency)
                throw new InvalidCurrencyException($"Surcharge currency {amount.Currency} does not match contract currency {Currency}");

            var surcharge = new ContractSurcharge(
                surchargeType, description, amount, calculationBasis,
                effectiveDate, expirationDate);

            _surcharges.Add(surcharge);
            Update(updatedBy);
        }

        public void AddCommitment(
            string commitmentType,
            decimal targetValue,
            decimal? minimumValue,
            decimal? maximumValue,
            string period,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(commitmentType))
                throw new DomainException("Commitment type cannot be empty");

            if (targetValue < 0)
                throw new DomainException("Target value cannot be negative");

            if (minimumValue.HasValue && minimumValue < 0)
                throw new DomainException("Minimum value cannot be negative");

            if (maximumValue.HasValue && maximumValue < 0)
                throw new DomainException("Maximum value cannot be negative");

            if (string.IsNullOrWhiteSpace(period))
                throw new DomainException("Period cannot be empty");

            var commitment = new ContractCommitment(
                commitmentType, targetValue, minimumValue, maximumValue, period);

            _commitments.Add(commitment);
            Update(updatedBy);
        }

        public void Activate(string updatedBy)
        {
            if (Status == ContractStatus.Active)
                throw new DomainException("Contract is already active");

            if (DateTime.UtcNow < EffectiveDate)
                throw new DomainException("Cannot activate contract before effective date");

            if (DateTime.UtcNow > ExpirationDate)
                throw new DomainException("Cannot activate expired contract");

            Status = ContractStatus.Active;
            Update(updatedBy);

            AddDomainEvent(new ContractActivatedEvent(Id, ContractNumber, Name, OrganizationId));
        }

        public void Suspend(string reason, string updatedBy)
        {
            if (Status != ContractStatus.Active)
                throw new DomainException("Only active contracts can be suspended");

            Status = ContractStatus.Suspended;
            ReviewNotes = string.IsNullOrWhiteSpace(ReviewNotes) ? reason : $"{ReviewNotes}\nSuspension reason: {reason}";
            Update(updatedBy);

            AddDomainEvent(new ContractSuspendedEvent(Id, ContractNumber, Name, reason, OrganizationId));
        }

        public void Terminate(string reason, string updatedBy)
        {
            if (Status == ContractStatus.Terminated)
                throw new DomainException("Contract is already terminated");

            Status = ContractStatus.Terminated;
            ReviewNotes = string.IsNullOrWhiteSpace(ReviewNotes) ? reason : $"{ReviewNotes}\nTermination reason: {reason}";
            Update(updatedBy);

            AddDomainEvent(new ContractTerminatedEvent(Id, ContractNumber, Name, reason, OrganizationId));
        }

        public void Expire(string updatedBy)
        {
            if (Status == ContractStatus.Expired)
                return;

            Status = ContractStatus.Expired;
            Update(updatedBy);

            AddDomainEvent(new ContractExpiredEvent(Id, ContractNumber, Name, OrganizationId));
        }

        public void SetReview(DateTime reviewDate, string? notes, DateTime? nextReviewDate, string updatedBy)
        {
            LastReviewDate = reviewDate;
            ReviewNotes = notes;
            NextReviewDate = nextReviewDate;
            Update(updatedBy);
        }

        public void SetDocument(string? contractDocument, string updatedBy)
        {
            ContractDocument = contractDocument;
            Update(updatedBy);
        }

        public bool IsActive()
        {
            return Status == ContractStatus.Active &&
                   DateTime.UtcNow >= EffectiveDate &&
                   DateTime.UtcNow <= ExpirationDate;
        }

        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpirationDate;
        }

        public void CheckAndUpdateExpiration()
        {
            if (IsExpired() && Status == ContractStatus.Active)
            {
                Status = ContractStatus.Expired;
                AddDomainEvent(new ContractExpiredEvent(Id, ContractNumber, Name, OrganizationId));
            }
        }

        public Contract CreateAmendment(string amendmentReason, string createdBy)
        {
            if (Status != ContractStatus.Active)
                throw new DomainException("Only active contracts can be amended");

            var amendment = new Contract(
                $"{ContractNumber}-A{Version + 1}",
                $"{Name} (Amendment {Version + 1})",
                $"Amendment to {Description}. Reason: {amendmentReason}",
                DateTime.UtcNow.Date,
                ExpirationDate,
                Currency,
                OrganizationId,
                createdBy)
            {
                ParentContractId = Id,
                Version = Version + 1,
                CustomerId = CustomerId,
                CustomerName = CustomerName,
                ShipperId = ShipperId,
                ShipperName = ShipperName
            };

            return amendment;
        }
    }

    public class ContractRate
    {
        public string ServiceType { get; private set; }
        public string? OriginZone { get; private set; }
        public string? DestinationZone { get; private set; }
        public decimal? MinWeight { get; private set; }
        public decimal? MaxWeight { get; private set; }
        public Money Rate { get; private set; }
        public string? RateType { get; private set; }
        public DateTime? EffectiveDate { get; private set; }
        public DateTime? ExpirationDate { get; private set; }

        public ContractRate(
            string serviceType,
            string? originZone,
            string? destinationZone,
            decimal? minWeight,
            decimal? maxWeight,
            Money rate,
            string? rateType,
            DateTime? effectiveDate,
            DateTime? expirationDate)
        {
            ServiceType = serviceType;
            OriginZone = originZone;
            DestinationZone = destinationZone;
            MinWeight = minWeight;
            MaxWeight = maxWeight;
            Rate = rate;
            RateType = rateType;
            EffectiveDate = effectiveDate;
            ExpirationDate = expirationDate;
        }

        public bool IsApplicable(DateTime date, string serviceType, string? originZone, string? destinationZone, decimal? weight)
        {
            if (ServiceType != serviceType)
                return false;

            if (!string.IsNullOrEmpty(OriginZone) && OriginZone != originZone)
                return false;

            if (!string.IsNullOrEmpty(DestinationZone) && DestinationZone != destinationZone)
                return false;

            if (weight.HasValue)
            {
                if (MinWeight.HasValue && weight < MinWeight)
                    return false;

                if (MaxWeight.HasValue && weight >= MaxWeight)
                    return false;
            }

            if (EffectiveDate.HasValue && date < EffectiveDate)
                return false;

            if (ExpirationDate.HasValue && date > ExpirationDate)
                return false;

            return true;
        }
    }

    public class ContractDiscount
    {
        public DiscountType DiscountType { get; private set; }
        public string Description { get; private set; }
        public decimal? Percentage { get; private set; }
        public Money? FixedAmount { get; private set; }
        public decimal? MinVolume { get; private set; }
        public decimal? MaxVolume { get; private set; }
        public DateTime? EffectiveDate { get; private set; }
        public DateTime? ExpirationDate { get; private set; }

        public ContractDiscount(
            DiscountType discountType,
            string description,
            decimal? percentage,
            Money? fixedAmount,
            decimal? minVolume,
            decimal? maxVolume,
            DateTime? effectiveDate,
            DateTime? expirationDate)
        {
            DiscountType = discountType;
            Description = description;
            Percentage = percentage;
            FixedAmount = fixedAmount;
            MinVolume = minVolume;
            MaxVolume = maxVolume;
            EffectiveDate = effectiveDate;
            ExpirationDate = expirationDate;
        }

        public bool IsApplicable(DateTime date, decimal? volume)
        {
            if (volume.HasValue)
            {
                if (MinVolume.HasValue && volume < MinVolume)
                    return false;

                if (MaxVolume.HasValue && volume >= MaxVolume)
                    return false;
            }

            if (EffectiveDate.HasValue && date < EffectiveDate)
                return false;

            if (ExpirationDate.HasValue && date > ExpirationDate)
                return false;

            return true;
        }
    }

    public class ContractSurcharge
    {
        public SurchargeType SurchargeType { get; private set; }
        public string Description { get; private set; }
        public Money Amount { get; private set; }
        public string? CalculationBasis { get; private set; }
        public DateTime? EffectiveDate { get; private set; }
        public DateTime? ExpirationDate { get; private set; }

        public ContractSurcharge(
            SurchargeType surchargeType,
            string description,
            Money amount,
            string? calculationBasis,
            DateTime? effectiveDate,
            DateTime? expirationDate)
        {
            SurchargeType = surchargeType;
            Description = description;
            Amount = amount;
            CalculationBasis = calculationBasis;
            EffectiveDate = effectiveDate;
            ExpirationDate = expirationDate;
        }

        public bool IsApplicable(DateTime date)
        {
            if (EffectiveDate.HasValue && date < EffectiveDate)
                return false;

            if (ExpirationDate.HasValue && date > ExpirationDate)
                return false;

            return true;
        }
    }

    public class ContractCommitment
    {
        public string CommitmentType { get; private set; }
        public decimal TargetValue { get; private set; }
        public decimal? MinimumValue { get; private set; }
        public decimal? MaximumValue { get; private set; }
        public string Period { get; private set; }

        public ContractCommitment(
            string commitmentType,
            decimal targetValue,
            decimal? minimumValue,
            decimal? maximumValue,
            string period)
        {
            CommitmentType = commitmentType;
            TargetValue = targetValue;
            MinimumValue = minimumValue;
            MaximumValue = maximumValue;
            Period = period;
        }
    }
}
