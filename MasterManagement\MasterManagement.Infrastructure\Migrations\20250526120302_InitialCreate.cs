﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MasterManagement.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase();

            migrationBuilder.CreateTable(
                name: "BusinessDays",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DayOfWeek = table.Column<int>(type: "integer", nullable: false),
                    IsBusinessDay = table.Column<bool>(type: "boolean", nullable: false),
                    IsHoliday = table.Column<bool>(type: "boolean", nullable: false),
                    HolidayId = table.Column<Guid>(type: "uuid", nullable: true),
                    CountryId = table.Column<Guid>(type: "uuid", nullable: true),
                    StateId = table.Column<Guid>(type: "uuid", nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeliveryAvailable = table.Column<bool>(type: "boolean", nullable: false),
                    PickupAvailable = table.Column<bool>(type: "boolean", nullable: false),
                    OfficeOpen = table.Column<bool>(type: "boolean", nullable: false),
                    CustomerServiceAvailable = table.Column<bool>(type: "boolean", nullable: false),
                    OperatingHours = table.Column<string>(type: "text", nullable: true),
                    DeliveryHours = table.Column<string>(type: "text", nullable: true),
                    PickupHours = table.Column<string>(type: "text", nullable: true),
                    CustomerServiceHours = table.Column<string>(type: "text", nullable: true),
                    HasSpecialArrangements = table.Column<bool>(type: "boolean", nullable: false),
                    SpecialArrangements = table.Column<string>(type: "text", nullable: true),
                    ServiceLevelAdjustment = table.Column<decimal>(type: "numeric", nullable: true),
                    IsOverride = table.Column<bool>(type: "boolean", nullable: false),
                    OverrideReason = table.Column<string>(type: "text", nullable: true),
                    OverrideBy = table.Column<string>(type: "text", nullable: true),
                    OverrideDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BusinessDays", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Continents",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Continents", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DataQualityMetrics",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    EntityType = table.Column<string>(type: "text", nullable: false),
                    MetricType = table.Column<string>(type: "text", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    MeasurementCriteria = table.Column<string>(type: "text", nullable: false),
                    ThresholdGood = table.Column<decimal>(type: "numeric", nullable: false),
                    ThresholdAcceptable = table.Column<decimal>(type: "numeric", nullable: false),
                    Unit = table.Column<string>(type: "text", nullable: false),
                    CurrentScore = table.Column<decimal>(type: "numeric", nullable: false),
                    CurrentStatus = table.Column<string>(type: "text", nullable: false),
                    LastMeasured = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    TotalRecordsEvaluated = table.Column<long>(type: "bigint", nullable: false),
                    PassingRecords = table.Column<long>(type: "bigint", nullable: false),
                    FailingRecords = table.Column<long>(type: "bigint", nullable: false),
                    PreviousScore = table.Column<decimal>(type: "numeric", nullable: true),
                    ScoreTrend = table.Column<decimal>(type: "numeric", nullable: true),
                    TrendDirection = table.Column<string>(type: "text", nullable: true),
                    CommonIssues = table.Column<string>(type: "text", nullable: true),
                    RecommendedActions = table.Column<string>(type: "text", nullable: true),
                    CriticalIssueCount = table.Column<int>(type: "integer", nullable: false),
                    WarningIssueCount = table.Column<int>(type: "integer", nullable: false),
                    AutoMeasurement = table.Column<bool>(type: "boolean", nullable: false),
                    MeasurementSchedule = table.Column<string>(type: "text", nullable: true),
                    AlertOnThresholdBreach = table.Column<bool>(type: "boolean", nullable: false),
                    AlertRecipients = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataQualityMetrics", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DuplicateDetections",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    EntityType = table.Column<string>(type: "text", nullable: false),
                    DetectionRuleName = table.Column<string>(type: "text", nullable: false),
                    MatchingCriteria = table.Column<string>(type: "text", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    SimilarityThreshold = table.Column<decimal>(type: "numeric", nullable: false),
                    MatchingAlgorithm = table.Column<string>(type: "text", nullable: false),
                    FieldWeights = table.Column<string>(type: "text", nullable: true),
                    CrossOrganizationCheck = table.Column<bool>(type: "boolean", nullable: false),
                    AutoMergeEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    AutoMergeThreshold = table.Column<decimal>(type: "numeric", nullable: false),
                    LastRunDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    TotalRecordsScanned = table.Column<int>(type: "integer", nullable: false),
                    DuplicateGroupsFound = table.Column<int>(type: "integer", nullable: false),
                    TotalDuplicatesFound = table.Column<int>(type: "integer", nullable: false),
                    AutoMergedCount = table.Column<int>(type: "integer", nullable: false),
                    ManualReviewRequired = table.Column<int>(type: "integer", nullable: false),
                    AverageProcessingTimeMs = table.Column<decimal>(type: "numeric", nullable: false),
                    LastRunStatus = table.Column<string>(type: "text", nullable: true),
                    LastRunErrors = table.Column<string>(type: "text", nullable: true),
                    ScheduledRun = table.Column<bool>(type: "boolean", nullable: false),
                    RunSchedule = table.Column<string>(type: "text", nullable: true),
                    NextScheduledRun = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DuplicateDetections", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Holidays",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Date = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    HolidayType = table.Column<string>(type: "text", nullable: false),
                    CountryId = table.Column<Guid>(type: "uuid", nullable: true),
                    StateId = table.Column<Guid>(type: "uuid", nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsRecurring = table.Column<bool>(type: "boolean", nullable: false),
                    RecurrencePattern = table.Column<string>(type: "text", nullable: true),
                    AffectsDelivery = table.Column<bool>(type: "boolean", nullable: false),
                    AffectsPickup = table.Column<bool>(type: "boolean", nullable: false),
                    AffectsOfficeOperations = table.Column<bool>(type: "boolean", nullable: false),
                    AffectsCustomerService = table.Column<bool>(type: "boolean", nullable: false),
                    AlternativeArrangements = table.Column<string>(type: "text", nullable: true),
                    AlternativeServiceDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    SpecialInstructions = table.Column<string>(type: "text", nullable: true),
                    CulturalSignificance = table.Column<string>(type: "text", nullable: true),
                    ReligiousAffiliation = table.Column<string>(type: "text", nullable: true),
                    LocalCustoms = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Holidays", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "RuleEngines",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Version = table.Column<string>(type: "text", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false),
                    RuleEngineType = table.Column<string>(type: "text", nullable: false),
                    ExecutionMode = table.Column<string>(type: "text", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    Configuration = table.Column<string>(type: "text", nullable: true),
                    EnableLogging = table.Column<bool>(type: "boolean", nullable: false),
                    EnableCaching = table.Column<bool>(type: "boolean", nullable: false),
                    CacheExpiryMinutes = table.Column<int>(type: "integer", nullable: false),
                    TotalExecutions = table.Column<long>(type: "bigint", nullable: false),
                    SuccessfulExecutions = table.Column<long>(type: "bigint", nullable: false),
                    FailedExecutions = table.Column<long>(type: "bigint", nullable: false),
                    AverageExecutionTimeMs = table.Column<decimal>(type: "numeric", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RuleEngines", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ServiceTypes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Category = table.Column<string>(type: "text", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false),
                    SortOrder = table.Column<int>(type: "integer", nullable: false),
                    BasePrice = table.Column<decimal>(type: "numeric", nullable: true),
                    PricingModel = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ServiceTypes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VehicleTypes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Category = table.Column<string>(type: "text", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    MaxWeightKg = table.Column<decimal>(type: "numeric", nullable: false),
                    MaxVolumeM3 = table.Column<decimal>(type: "numeric", nullable: false),
                    MaxLengthCm = table.Column<decimal>(type: "numeric", nullable: false),
                    MaxWidthCm = table.Column<decimal>(type: "numeric", nullable: false),
                    MaxHeightCm = table.Column<decimal>(type: "numeric", nullable: false),
                    FuelConsumptionPer100Km = table.Column<decimal>(type: "numeric", nullable: false),
                    FuelType = table.Column<string>(type: "text", nullable: false),
                    CostPerKm = table.Column<decimal>(type: "numeric", nullable: false),
                    CostPerHour = table.Column<decimal>(type: "numeric", nullable: false),
                    MaxDriverHours = table.Column<int>(type: "integer", nullable: false),
                    RequiresSpecialLicense = table.Column<bool>(type: "boolean", nullable: false),
                    SpecialLicenseType = table.Column<string>(type: "text", nullable: true),
                    EmissionStandard = table.Column<string>(type: "text", nullable: false),
                    IsEcoFriendly = table.Column<bool>(type: "boolean", nullable: false),
                    RestrictedAreas = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleTypes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Regions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    ContinentId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    ContinentId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Regions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Regions_Continents_ContinentId",
                        column: x => x.ContinentId,
                        principalTable: "Continents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Regions_Continents_ContinentId1",
                        column: x => x.ContinentId1,
                        principalTable: "Continents",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "DuplicateGroup",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    DuplicateDetectionId = table.Column<Guid>(type: "uuid", nullable: false),
                    GroupKey = table.Column<string>(type: "text", nullable: false),
                    RecordCount = table.Column<int>(type: "integer", nullable: false),
                    SimilarityScore = table.Column<decimal>(type: "numeric", nullable: false),
                    Status = table.Column<string>(type: "text", nullable: false),
                    RecordIds = table.Column<string>(type: "text", nullable: true),
                    SimilarityDetails = table.Column<string>(type: "text", nullable: true),
                    RecommendedAction = table.Column<string>(type: "text", nullable: true),
                    ReviewedBy = table.Column<string>(type: "text", nullable: true),
                    ReviewedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ReviewNotes = table.Column<string>(type: "text", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DuplicateGroup", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DuplicateGroup_DuplicateDetections_DuplicateDetectionId",
                        column: x => x.DuplicateDetectionId,
                        principalTable: "DuplicateDetections",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RuleExecution",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    RuleEngineId = table.Column<Guid>(type: "uuid", nullable: false),
                    RuleGroupId = table.Column<Guid>(type: "uuid", nullable: true),
                    ConditionalRuleId = table.Column<Guid>(type: "uuid", nullable: true),
                    ExecutionContext = table.Column<string>(type: "text", nullable: false),
                    ExecutionStartTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExecutionEndTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExecutionTimeMs = table.Column<decimal>(type: "numeric", nullable: false),
                    IsSuccessful = table.Column<bool>(type: "boolean", nullable: false),
                    ErrorMessage = table.Column<string>(type: "text", nullable: true),
                    ErrorStackTrace = table.Column<string>(type: "text", nullable: true),
                    Result = table.Column<string>(type: "text", nullable: true),
                    ExecutionMode = table.Column<string>(type: "text", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    InputData = table.Column<string>(type: "text", nullable: true),
                    OutputData = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    SessionId = table.Column<string>(type: "text", nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RuleExecution", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RuleExecution_RuleEngines_RuleEngineId",
                        column: x => x.RuleEngineId,
                        principalTable: "RuleEngines",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "RuleGroups",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    RuleEngineId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    ExecutionOrder = table.Column<int>(type: "integer", nullable: false),
                    GroupType = table.Column<string>(type: "text", nullable: false),
                    ExecutionMode = table.Column<string>(type: "text", nullable: false),
                    StopOnFirstFailure = table.Column<bool>(type: "boolean", nullable: false),
                    StopOnFirstSuccess = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RuleGroups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_RuleGroups_RuleEngines_RuleEngineId",
                        column: x => x.RuleEngineId,
                        principalTable: "RuleEngines",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ServiceLevelAgreements",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    ServiceTypeId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    DeliveryTimeHours = table.Column<int>(type: "integer", nullable: false),
                    OnTimeDeliveryPercentage = table.Column<decimal>(type: "numeric", nullable: false),
                    ServiceAvailabilityPercentage = table.Column<decimal>(type: "numeric", nullable: false),
                    CoverageAreas = table.Column<string>(type: "text", nullable: false),
                    MaxWeightKg = table.Column<int>(type: "integer", nullable: false),
                    MaxDimensionsCm = table.Column<int>(type: "integer", nullable: false),
                    MaxVolumeM3 = table.Column<int>(type: "integer", nullable: false),
                    PenaltyPercentage = table.Column<decimal>(type: "numeric", nullable: true),
                    EscalationProcedure = table.Column<string>(type: "text", nullable: true),
                    CompensationPolicy = table.Column<string>(type: "text", nullable: true),
                    OperatingHours = table.Column<string>(type: "text", nullable: false),
                    OperatingDays = table.Column<string>(type: "text", nullable: false),
                    IncludesHolidays = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ServiceLevelAgreements", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ServiceLevelAgreements_ServiceTypes_ServiceTypeId",
                        column: x => x.ServiceTypeId,
                        principalTable: "ServiceTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ServiceRestriction",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ServiceTypeId = table.Column<Guid>(type: "uuid", nullable: false),
                    RestrictionType = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    RestrictionRule = table.Column<string>(type: "text", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    ErrorMessage = table.Column<string>(type: "text", nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    MaxWeight = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxLength = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxWidth = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxHeight = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxVolume = table.Column<decimal>(type: "numeric", nullable: true),
                    RestrictedZones = table.Column<string>(type: "text", nullable: true),
                    AllowedZones = table.Column<string>(type: "text", nullable: true),
                    StartTime = table.Column<TimeSpan>(type: "interval", nullable: true),
                    EndTime = table.Column<TimeSpan>(type: "interval", nullable: true),
                    RestrictedDays = table.Column<string>(type: "text", nullable: true),
                    AllowedDays = table.Column<string>(type: "text", nullable: true),
                    RequiresSpecialHandling = table.Column<bool>(type: "boolean", nullable: false),
                    SpecialHandlingInstructions = table.Column<string>(type: "text", nullable: true),
                    RequiresInsurance = table.Column<bool>(type: "boolean", nullable: false),
                    MinInsuranceValue = table.Column<decimal>(type: "numeric", nullable: true),
                    RequiresSignature = table.Column<bool>(type: "boolean", nullable: false),
                    RequiresIdVerification = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ServiceRestriction", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ServiceRestriction_ServiceTypes_ServiceTypeId",
                        column: x => x.ServiceTypeId,
                        principalTable: "ServiceTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VehicleCapacities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    VehicleTypeId = table.Column<Guid>(type: "uuid", nullable: false),
                    CapacityType = table.Column<string>(type: "text", nullable: false),
                    MaxCapacity = table.Column<decimal>(type: "numeric", nullable: false),
                    Unit = table.Column<string>(type: "text", nullable: false),
                    OptimalCapacity = table.Column<decimal>(type: "numeric", nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleCapacities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleCapacities_VehicleTypes_VehicleTypeId",
                        column: x => x.VehicleTypeId,
                        principalTable: "VehicleTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "VehicleZoneAccesses",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    VehicleTypeId = table.Column<Guid>(type: "uuid", nullable: false),
                    ZoneId = table.Column<Guid>(type: "uuid", nullable: false),
                    ZoneName = table.Column<string>(type: "text", nullable: false),
                    AccessType = table.Column<string>(type: "text", nullable: false),
                    TimeRestrictions = table.Column<string>(type: "text", nullable: true),
                    SpecialRequirements = table.Column<string>(type: "text", nullable: true),
                    AdditionalCostPerKm = table.Column<decimal>(type: "numeric", nullable: true),
                    Reason = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleZoneAccesses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_VehicleZoneAccesses_VehicleTypes_VehicleTypeId",
                        column: x => x.VehicleTypeId,
                        principalTable: "VehicleTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Countries",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "character varying(2)", maxLength: 2, nullable: false),
                    Code3 = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    NumericCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    OfficialName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Currency = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    CurrencySymbol = table.Column<string>(type: "character varying(5)", maxLength: 5, nullable: false),
                    PhonePrefix = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    TimeZone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Language = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ContinentId = table.Column<Guid>(type: "uuid", nullable: false),
                    RegionId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Latitude = table.Column<double>(type: "double precision", nullable: true),
                    Longitude = table.Column<double>(type: "double precision", nullable: true),
                    RegionId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Countries", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Countries_Continents_ContinentId",
                        column: x => x.ContinentId,
                        principalTable: "Continents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Countries_Regions_RegionId",
                        column: x => x.RegionId,
                        principalTable: "Regions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_Countries_Regions_RegionId1",
                        column: x => x.RegionId1,
                        principalTable: "Regions",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "ConditionalRules",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    RuleGroupId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    ExecutionOrder = table.Column<int>(type: "integer", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    RuleType = table.Column<string>(type: "text", nullable: false),
                    Condition = table.Column<string>(type: "text", nullable: false),
                    Action = table.Column<string>(type: "text", nullable: false),
                    ElseAction = table.Column<string>(type: "text", nullable: true),
                    IsCritical = table.Column<bool>(type: "boolean", nullable: false),
                    ContinueOnFailure = table.Column<bool>(type: "boolean", nullable: false),
                    MaxRetries = table.Column<int>(type: "integer", nullable: false),
                    TimeoutSeconds = table.Column<int>(type: "integer", nullable: false),
                    EffectiveFrom = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EffectiveTo = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Schedule = table.Column<string>(type: "text", nullable: true),
                    Tags = table.Column<string>(type: "text", nullable: true),
                    Category = table.Column<string>(type: "text", nullable: true),
                    Version = table.Column<string>(type: "text", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ExecutionCount = table.Column<long>(type: "bigint", nullable: false),
                    SuccessCount = table.Column<long>(type: "bigint", nullable: false),
                    FailureCount = table.Column<long>(type: "bigint", nullable: false),
                    AverageExecutionTimeMs = table.Column<decimal>(type: "numeric", nullable: false),
                    LastExecuted = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConditionalRules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ConditionalRules_RuleGroups_RuleGroupId",
                        column: x => x.RuleGroupId,
                        principalTable: "RuleGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SlaPerformances",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SlaId = table.Column<Guid>(type: "uuid", nullable: false),
                    PeriodStart = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PeriodEnd = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    TotalShipments = table.Column<int>(type: "integer", nullable: false),
                    OnTimeShipments = table.Column<int>(type: "integer", nullable: false),
                    ActualOnTimePercentage = table.Column<decimal>(type: "numeric", nullable: false),
                    TargetOnTimePercentage = table.Column<decimal>(type: "numeric", nullable: false),
                    SlaMetTarget = table.Column<bool>(type: "boolean", nullable: false),
                    PenaltyAmount = table.Column<decimal>(type: "numeric", nullable: true),
                    Notes = table.Column<string>(type: "text", nullable: true),
                    CorrectiveActions = table.Column<string>(type: "text", nullable: true),
                    AverageDeliveryTimeHours = table.Column<decimal>(type: "numeric", nullable: false),
                    ServiceAvailabilityPercentage = table.Column<decimal>(type: "numeric", nullable: false),
                    ComplaintCount = table.Column<int>(type: "integer", nullable: false),
                    EscalationCount = table.Column<int>(type: "integer", nullable: false),
                    ServiceLevelAgreementId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SlaPerformances", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SlaPerformances_ServiceLevelAgreements_ServiceLevelAgreemen~",
                        column: x => x.ServiceLevelAgreementId,
                        principalTable: "ServiceLevelAgreements",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SlaZoneOverrides",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    SlaId = table.Column<Guid>(type: "uuid", nullable: false),
                    ZoneId = table.Column<Guid>(type: "uuid", nullable: false),
                    ZoneName = table.Column<string>(type: "text", nullable: false),
                    OverrideDeliveryTimeHours = table.Column<int>(type: "integer", nullable: true),
                    OverrideOnTimePercentage = table.Column<decimal>(type: "numeric", nullable: true),
                    OverridePenaltyPercentage = table.Column<decimal>(type: "numeric", nullable: true),
                    SpecialInstructions = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: false),
                    ServiceLevelAgreementId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SlaZoneOverrides", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SlaZoneOverrides_ServiceLevelAgreements_ServiceLevelAgreeme~",
                        column: x => x.ServiceLevelAgreementId,
                        principalTable: "ServiceLevelAgreements",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "States",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CountryId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Latitude = table.Column<double>(type: "double precision", nullable: true),
                    Longitude = table.Column<double>(type: "double precision", nullable: true),
                    CountryId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_States", x => x.Id);
                    table.ForeignKey(
                        name: "FK_States_Countries_CountryId",
                        column: x => x.CountryId,
                        principalTable: "Countries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_States_Countries_CountryId1",
                        column: x => x.CountryId1,
                        principalTable: "Countries",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Cities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    StateId = table.Column<Guid>(type: "uuid", nullable: false),
                    CountryId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Latitude = table.Column<double>(type: "double precision", nullable: true),
                    Longitude = table.Column<double>(type: "double precision", nullable: true),
                    Population = table.Column<int>(type: "integer", nullable: true),
                    TimeZone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    StateId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Cities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Cities_Countries_CountryId",
                        column: x => x.CountryId,
                        principalTable: "Countries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Cities_States_StateId",
                        column: x => x.StateId,
                        principalTable: "States",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Cities_States_StateId1",
                        column: x => x.StateId1,
                        principalTable: "States",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Zones",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Description = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: false),
                    CityId = table.Column<Guid>(type: "uuid", nullable: false),
                    StateId = table.Column<Guid>(type: "uuid", nullable: false),
                    CountryId = table.Column<Guid>(type: "uuid", nullable: false),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Latitude = table.Column<double>(type: "double precision", nullable: true),
                    Longitude = table.Column<double>(type: "double precision", nullable: true),
                    ZoneType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CityId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Zones", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Zones_Cities_CityId",
                        column: x => x.CityId,
                        principalTable: "Cities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Zones_Cities_CityId1",
                        column: x => x.CityId1,
                        principalTable: "Cities",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Zones_Countries_CountryId",
                        column: x => x.CountryId,
                        principalTable: "Countries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Zones_States_StateId",
                        column: x => x.StateId,
                        principalTable: "States",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "PostalCodes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Code = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    SecondaryCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    CityId = table.Column<Guid>(type: "uuid", nullable: false),
                    StateId = table.Column<Guid>(type: "uuid", nullable: false),
                    CountryId = table.Column<Guid>(type: "uuid", nullable: false),
                    ZoneId = table.Column<Guid>(type: "uuid", nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Latitude = table.Column<double>(type: "double precision", nullable: true),
                    Longitude = table.Column<double>(type: "double precision", nullable: true),
                    AreaName = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PostalCodeType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    CityId1 = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PostalCodes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PostalCodes_Cities_CityId",
                        column: x => x.CityId,
                        principalTable: "Cities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PostalCodes_Cities_CityId1",
                        column: x => x.CityId1,
                        principalTable: "Cities",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PostalCodes_Countries_CountryId",
                        column: x => x.CountryId,
                        principalTable: "Countries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PostalCodes_States_StateId",
                        column: x => x.StateId,
                        principalTable: "States",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_PostalCodes_Zones_ZoneId",
                        column: x => x.ZoneId,
                        principalTable: "Zones",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Cities_CountryId",
                table: "Cities",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_Cities_StateId",
                table: "Cities",
                column: "StateId");

            migrationBuilder.CreateIndex(
                name: "IX_Cities_StateId1",
                table: "Cities",
                column: "StateId1");

            migrationBuilder.CreateIndex(
                name: "IX_ConditionalRules_RuleGroupId",
                table: "ConditionalRules",
                column: "RuleGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_Continents_Code",
                table: "Continents",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Countries_Code",
                table: "Countries",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Countries_Code3",
                table: "Countries",
                column: "Code3",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Countries_ContinentId",
                table: "Countries",
                column: "ContinentId");

            migrationBuilder.CreateIndex(
                name: "IX_Countries_RegionId",
                table: "Countries",
                column: "RegionId");

            migrationBuilder.CreateIndex(
                name: "IX_Countries_RegionId1",
                table: "Countries",
                column: "RegionId1");

            migrationBuilder.CreateIndex(
                name: "IX_DuplicateGroup_DuplicateDetectionId",
                table: "DuplicateGroup",
                column: "DuplicateDetectionId");

            migrationBuilder.CreateIndex(
                name: "IX_PostalCodes_CityId",
                table: "PostalCodes",
                column: "CityId");

            migrationBuilder.CreateIndex(
                name: "IX_PostalCodes_CityId1",
                table: "PostalCodes",
                column: "CityId1");

            migrationBuilder.CreateIndex(
                name: "IX_PostalCodes_CountryId",
                table: "PostalCodes",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_PostalCodes_StateId",
                table: "PostalCodes",
                column: "StateId");

            migrationBuilder.CreateIndex(
                name: "IX_PostalCodes_ZoneId",
                table: "PostalCodes",
                column: "ZoneId");

            migrationBuilder.CreateIndex(
                name: "IX_Regions_Code_ContinentId",
                table: "Regions",
                columns: new[] { "Code", "ContinentId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Regions_ContinentId",
                table: "Regions",
                column: "ContinentId");

            migrationBuilder.CreateIndex(
                name: "IX_Regions_ContinentId1",
                table: "Regions",
                column: "ContinentId1");

            migrationBuilder.CreateIndex(
                name: "IX_RuleExecution_RuleEngineId",
                table: "RuleExecution",
                column: "RuleEngineId");

            migrationBuilder.CreateIndex(
                name: "IX_RuleGroups_RuleEngineId",
                table: "RuleGroups",
                column: "RuleEngineId");

            migrationBuilder.CreateIndex(
                name: "IX_ServiceLevelAgreements_ServiceTypeId",
                table: "ServiceLevelAgreements",
                column: "ServiceTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_ServiceRestriction_ServiceTypeId",
                table: "ServiceRestriction",
                column: "ServiceTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_SlaPerformances_ServiceLevelAgreementId",
                table: "SlaPerformances",
                column: "ServiceLevelAgreementId");

            migrationBuilder.CreateIndex(
                name: "IX_SlaZoneOverrides_ServiceLevelAgreementId",
                table: "SlaZoneOverrides",
                column: "ServiceLevelAgreementId");

            migrationBuilder.CreateIndex(
                name: "IX_States_Code_CountryId",
                table: "States",
                columns: new[] { "Code", "CountryId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_States_CountryId",
                table: "States",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_States_CountryId1",
                table: "States",
                column: "CountryId1");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleCapacities_VehicleTypeId",
                table: "VehicleCapacities",
                column: "VehicleTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleZoneAccesses_VehicleTypeId",
                table: "VehicleZoneAccesses",
                column: "VehicleTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Zones_CityId",
                table: "Zones",
                column: "CityId");

            migrationBuilder.CreateIndex(
                name: "IX_Zones_CityId1",
                table: "Zones",
                column: "CityId1");

            migrationBuilder.CreateIndex(
                name: "IX_Zones_CountryId",
                table: "Zones",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_Zones_StateId",
                table: "Zones",
                column: "StateId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BusinessDays");

            migrationBuilder.DropTable(
                name: "ConditionalRules");

            migrationBuilder.DropTable(
                name: "DataQualityMetrics");

            migrationBuilder.DropTable(
                name: "DuplicateGroup");

            migrationBuilder.DropTable(
                name: "Holidays");

            migrationBuilder.DropTable(
                name: "PostalCodes");

            migrationBuilder.DropTable(
                name: "RuleExecution");

            migrationBuilder.DropTable(
                name: "ServiceRestriction");

            migrationBuilder.DropTable(
                name: "SlaPerformances");

            migrationBuilder.DropTable(
                name: "SlaZoneOverrides");

            migrationBuilder.DropTable(
                name: "VehicleCapacities");

            migrationBuilder.DropTable(
                name: "VehicleZoneAccesses");

            migrationBuilder.DropTable(
                name: "RuleGroups");

            migrationBuilder.DropTable(
                name: "DuplicateDetections");

            migrationBuilder.DropTable(
                name: "Zones");

            migrationBuilder.DropTable(
                name: "ServiceLevelAgreements");

            migrationBuilder.DropTable(
                name: "VehicleTypes");

            migrationBuilder.DropTable(
                name: "RuleEngines");

            migrationBuilder.DropTable(
                name: "Cities");

            migrationBuilder.DropTable(
                name: "ServiceTypes");

            migrationBuilder.DropTable(
                name: "States");

            migrationBuilder.DropTable(
                name: "Countries");

            migrationBuilder.DropTable(
                name: "Regions");

            migrationBuilder.DropTable(
                name: "Continents");
        }
    }
}
