using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Common;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Repositories;
using PricingManagement.Domain.ValueObjects;
using System.Text.Json;

namespace PricingManagement.Application.Contracts.Commands.CreateContract
{
    public class CreateContractCommandHandler : IRequestHandler<CreateContractCommand, OperationResultDto<ContractDto>>
    {
        private readonly IContractRepository _contractRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILogger<CreateContractCommandHandler> _logger;

        public CreateContractCommandHandler(
            IContractRepository contractRepository,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILogger<CreateContractCommandHandler> logger)
        {
            _contractRepository = contractRepository;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<OperationResultDto<ContractDto>> Handle(CreateContractCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_currentUserService.OrganizationId.HasValue)
                {
                    return OperationResultDto<ContractDto>.Failure("Organization context is required");
                }

                var organizationId = _currentUserService.OrganizationId.Value;
                var currentUser = _currentUserService.Username ?? "System";

                // Check if contract number already exists
                if (!string.IsNullOrWhiteSpace(request.ContractNumber))
                {
                    var contractNumberExists = await _contractRepository.ContractNumberExistsAsync(request.ContractNumber, organizationId);
                    if (contractNumberExists)
                    {
                        return OperationResultDto<ContractDto>.Failure($"A contract with number '{request.ContractNumber}' already exists");
                    }
                }
                else
                {
                    // Generate contract number if not provided
                    request.ContractNumber = await _contractRepository.GenerateContractNumberAsync(organizationId);
                }

                // Serialize arrays and objects
                var serviceTypes = request.ServiceTypes.Any() ? JsonSerializer.Serialize(request.ServiceTypes) : null;
                var geographicScope = request.GeographicScope.Any() ? JsonSerializer.Serialize(request.GeographicScope) : null;

                // Create contract
                var contract = new Contract(
                    request.ContractNumber,
                    request.Name,
                    request.Description,
                    request.EffectiveDate,
                    request.ExpirationDate,
                    request.Currency,
                    organizationId,
                    currentUser);

                // Set customer and shipper information
                if (request.CustomerId.HasValue && !string.IsNullOrWhiteSpace(request.CustomerName))
                {
                    contract.SetCustomer(request.CustomerId.Value, request.CustomerName, currentUser);
                }

                if (request.ShipperId.HasValue && !string.IsNullOrWhiteSpace(request.ShipperName))
                {
                    contract.SetShipper(request.ShipperId.Value, request.ShipperName, currentUser);
                }

                // Set contract terms
                contract.SetTerms(
                    request.Terms,
                    request.PaymentTerms,
                    request.MinimumCommitment,
                    request.MaximumCommitment,
                    request.CommitmentType,
                    request.CommitmentPeriod,
                    currentUser);

                // Set renewal settings
                contract.SetRenewalSettings(
                    request.AutoRenewal,
                    request.AutoRenewalPeriod,
                    request.NotificationPeriod,
                    currentUser);

                // Set scope
                contract.SetScope(serviceTypes, geographicScope, request.SpecialProvisions, currentUser);

                // Add rates
                foreach (var rateDto in request.Rates)
                {
                    var rate = new Money(rateDto.Rate.Amount, rateDto.Rate.Currency);
                    contract.AddRate(
                        rateDto.ServiceType,
                        rateDto.OriginZone,
                        rateDto.DestinationZone,
                        rateDto.MinWeight,
                        rateDto.MaxWeight,
                        rate,
                        rateDto.RateType,
                        rateDto.EffectiveDate,
                        rateDto.ExpirationDate,
                        currentUser);
                }

                // Add discounts
                foreach (var discountDto in request.Discounts)
                {
                    Money? fixedAmount = null;
                    if (discountDto.FixedAmount != null)
                    {
                        fixedAmount = new Money(discountDto.FixedAmount.Amount, discountDto.FixedAmount.Currency);
                    }

                    contract.AddDiscount(
                        discountDto.DiscountType,
                        discountDto.Description,
                        discountDto.Percentage,
                        fixedAmount,
                        discountDto.MinVolume,
                        discountDto.MaxVolume,
                        discountDto.EffectiveDate,
                        discountDto.ExpirationDate,
                        currentUser);
                }

                // Add surcharges
                foreach (var surchargeDto in request.Surcharges)
                {
                    var amount = new Money(surchargeDto.Amount.Amount, surchargeDto.Amount.Currency);
                    contract.AddSurcharge(
                        surchargeDto.SurchargeType,
                        surchargeDto.Description,
                        amount,
                        surchargeDto.CalculationBasis,
                        surchargeDto.EffectiveDate,
                        surchargeDto.ExpirationDate,
                        currentUser);
                }

                // Add commitments
                foreach (var commitmentDto in request.Commitments)
                {
                    contract.AddCommitment(
                        commitmentDto.CommitmentType,
                        commitmentDto.TargetValue,
                        commitmentDto.MinimumValue,
                        commitmentDto.MaximumValue,
                        commitmentDto.Period,
                        currentUser);
                }

                // Save contract
                await _contractRepository.AddAsync(contract);

                // Map to DTO
                var contractDto = _mapper.Map<ContractDto>(contract);

                _logger.LogInformation("Created contract {ContractNumber} with name {Name} for organization {OrganizationId}",
                    contract.ContractNumber, contract.Name, organizationId);

                return OperationResultDto<ContractDto>.Success(contractDto, "Contract created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating contract with name {Name}", request.Name);
                return OperationResultDto<ContractDto>.Failure("An error occurred while creating the contract");
            }
        }
    }
}
