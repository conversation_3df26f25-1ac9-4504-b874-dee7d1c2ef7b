using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UserManagement.API.Models.Requests;
using UserManagement.Application.Common.Models;
using UserManagement.Application.Branches.Commands.CreateBranch;
using UserManagement.Application.Branches.Commands.UpdateBranch;
using UserManagement.Application.Branches.Commands.DeleteBranch;
using UserManagement.Application.Branches.Commands.SetHeadquarters;
using UserManagement.Application.Branches.Queries.GetBranchById;
using UserManagement.Application.Branches.Queries.GetBranchesByOrganization;

namespace UserManagement.API.Controllers
{
    [ApiVersion("1.0")]
    //[Authorize]
    public class BranchesController : BaseApiController
    {
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<Guid>> Create([FromBody] CreateBranchRequest request)
        {
            var command = new CreateBranchCommand
            {
                Name = request.Name,
                Address = request.Address,
                PhoneNumber = request.PhoneNumber,
                Email = request.Email,
                OrganizationId = request.OrganizationId,
                IsHeadquarters = request.IsHeadquarters
            };

            var branchId = await Mediator.Send(command);
            return CreatedAtAction(nameof(GetById), new { id = branchId }, branchId);
        }

        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<BranchDto>> GetById(Guid id)
        {
            var query = new GetBranchByIdQuery { BranchId = id };
            var branch = await Mediator.Send(query);
            return Ok(branch);
        }

        [HttpGet("organization/{organizationId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<List<BranchDto>>> GetByOrganization(Guid organizationId, [FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            var query = new GetBranchesByOrganizationQuery
            {
                OrganizationId = organizationId,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var branches = await Mediator.Send(query);
            return Ok(branches);
        }

        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult> Update(Guid id, [FromBody] UpdateBranchRequest request)
        {
            var command = new UpdateBranchCommand
            {
                BranchId = id,
                Name = request.Name,
                Address = request.Address,
                PhoneNumber = request.PhoneNumber,
                Email = request.Email
            };

            await Mediator.Send(command);
            return Ok();
        }

        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult> Delete(Guid id)
        {
            var command = new DeleteBranchCommand { BranchId = id };
            await Mediator.Send(command);
            return NoContent();
        }

        [HttpPut("{id}/headquarters")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult> SetAsHeadquarters(Guid id)
        {
            var command = new SetHeadquartersCommand { BranchId = id };
            await Mediator.Send(command);
            return Ok();
        }
    }
}
