using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PricingManagement.Application.Common;
using PricingManagement.Application.Services;
using PricingManagement.Domain.Repositories;
using PricingManagement.Infrastructure.Persistence;
using PricingManagement.Infrastructure.Repositories;
using PricingManagement.Infrastructure.Services;
using Shared.Messaging;

namespace PricingManagement.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Database
            services.AddDbContext<PricingManagementDbContext>((serviceProvider, options) =>
            {
                options.UseNpgsql(
                    configuration.GetConnectionString("DefaultConnection") ??
                    "Host=localhost;Database=TriTrackzPricingManagement;Username=timescale;Password=timescale",
                    b => b.MigrationsAssembly(typeof(PricingManagementDbContext).Assembly.FullName));
            });

            // Repositories
            services.AddScoped<IPricingRuleRepository, PricingRuleRepository>();
            services.AddScoped<IQuoteRepository, QuoteRepository>();
            services.AddScoped<IContractRepository, ContractRepository>();

            // Infrastructure Services
            services.AddHttpContextAccessor();
            services.AddScoped<ICurrentUserService, CurrentUserService>();

            // External Service Implementations
            services.AddScoped<IIdentityService, IdentityService>();
            services.AddScoped<IUserManagementService, UserManagementService>();
            services.AddScoped<IMasterManagementService, MasterManagementService>();
            services.AddScoped<ICustomerManagementService, CustomerManagementService>();
            services.AddScoped<IShipperManagementService, ShipperManagementService>();
            services.AddScoped<IOrderManagementService, OrderManagementService>();
            services.AddScoped<IShipmentManagementService, ShipmentManagementService>();

            // Application Service Implementations
            services.AddScoped<IPricingEngine, PricingEngine>();
            services.AddScoped<IRateCalculationService, RateCalculationService>();
            services.AddScoped<IQuoteService, QuoteService>();
            services.AddScoped<IContractService, ContractService>();
            services.AddScoped<IDiscountService, DiscountService>();
            services.AddScoped<ISurchargeService, SurchargeService>();
            services.AddScoped<ITaxCalculationService, TaxCalculationService>();
            services.AddScoped<ICurrencyConversionService, CurrencyConversionService>();
            services.AddScoped<IFuelSurchargeService, FuelSurchargeService>();
            services.AddScoped<ICarrierRateService, CarrierRateService>();
            services.AddScoped<IPricingApprovalService, PricingApprovalService>();
            services.AddScoped<IPricingSimulationService, PricingSimulationService>();

            // Data Seeding
            services.AddScoped<DataSeeder>();

            // Caching
            var redisConnectionString = configuration.GetConnectionString("Redis");
            if (!string.IsNullOrEmpty(redisConnectionString))
            {
                services.AddStackExchangeRedisCache(options =>
                {
                    options.Configuration = redisConnectionString;
                });
            }
            else
            {
                services.AddMemoryCache();
            }

            // HTTP Clients for external services
            services.AddHttpClient<IIdentityService, IdentityService>(client =>
            {
                client.BaseAddress = new Uri(configuration["IdentityApi:BaseUrl"] ?? "https://localhost:7001/api/v1");
                client.Timeout = TimeSpan.FromSeconds(30);
            });

            services.AddHttpClient<IUserManagementService, UserManagementService>(client =>
            {
                client.BaseAddress = new Uri(configuration["UserManagementApi:BaseUrl"] ?? "https://localhost:7002/api/v1");
                client.Timeout = TimeSpan.FromSeconds(30);
            });

            services.AddHttpClient<IMasterManagementService, MasterManagementService>(client =>
            {
                client.BaseAddress = new Uri(configuration["MasterManagementApi:BaseUrl"] ?? "https://localhost:7003/api/v1");
                client.Timeout = TimeSpan.FromSeconds(30);
            });

            services.AddHttpClient<ICustomerManagementService, CustomerManagementService>(client =>
            {
                client.BaseAddress = new Uri(configuration["CustomerManagementApi:BaseUrl"] ?? "https://localhost:7004/api/v1");
                client.Timeout = TimeSpan.FromSeconds(30);
            });

            services.AddHttpClient<IShipperManagementService, ShipperManagementService>(client =>
            {
                client.BaseAddress = new Uri(configuration["ShipperManagementApi:BaseUrl"] ?? "https://localhost:7006/api/v1");
                client.Timeout = TimeSpan.FromSeconds(30);
            });

            services.AddHttpClient<IOrderManagementService, OrderManagementService>(client =>
            {
                client.BaseAddress = new Uri(configuration["OrderManagementApi:BaseUrl"] ?? "https://localhost:7007/api/v1");
                client.Timeout = TimeSpan.FromSeconds(30);
            });

            services.AddHttpClient<IShipmentManagementService, ShipmentManagementService>(client =>
            {
                client.BaseAddress = new Uri(configuration["ShipmentManagementApi:BaseUrl"] ?? "https://localhost:7008/api/v1");
                client.Timeout = TimeSpan.FromSeconds(30);
            });

            // External API clients for pricing data
            services.AddHttpClient("CurrencyApi", client =>
            {
                client.BaseAddress = new Uri(configuration["ExternalServices:CurrencyApiUrl"] ?? "https://api.exchangerate-api.com/v4/latest/");
                client.Timeout = TimeSpan.FromSeconds(10);
            });

            services.AddHttpClient("TaxApi", client =>
            {
                client.BaseAddress = new Uri(configuration["ExternalServices:TaxApiUrl"] ?? "https://api.taxjar.com/v2/");
                client.Timeout = TimeSpan.FromSeconds(10);
            });

            services.AddHttpClient("FuelSurchargeApi", client =>
            {
                client.BaseAddress = new Uri(configuration["ExternalServices:FuelSurchargeApiUrl"] ?? "https://api.fuelsurcharge.com/v1/");
                client.Timeout = TimeSpan.FromSeconds(10);
            });

            // Messaging
            services.AddMessaging(configuration.GetValue<string>("RabbitMQ:Host", "localhost"));

            return services;
        }
    }
}
