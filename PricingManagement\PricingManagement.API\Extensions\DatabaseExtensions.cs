using Microsoft.EntityFrameworkCore;
using PricingManagement.Infrastructure.Persistence;

namespace PricingManagement.API.Extensions
{
    public static class DatabaseExtensions
    {
        public static async Task MigrateAndSeedDatabaseAsync(this WebApplication app)
        {
            using var scope = app.Services.CreateScope();
            var services = scope.ServiceProvider;
            var logger = services.GetRequiredService<ILogger<Program>>();

            try
            {
                var context = services.GetRequiredService<PricingManagementDbContext>();
                
                logger.LogInformation("Starting database migration...");
                await context.Database.MigrateAsync();
                logger.LogInformation("Database migration completed successfully");

                logger.LogInformation("Starting database seeding...");
                await SeedDatabaseAsync(context, logger);
                logger.LogInformation("Database seeding completed successfully");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while migrating or seeding the database");
                throw;
            }
        }

        private static async Task SeedDatabaseAsync(PricingManagementDbContext context, ILogger logger)
        {
            // Check if database is already seeded
            if (await context.PricingRules.AnyAsync())
            {
                logger.LogInformation("Database already contains data, skipping seeding");
                return;
            }

            // TODO: Add seed data for pricing rules, sample contracts, etc.
            // This will be implemented in the data seeding phase

            await context.SaveChangesAsync();
            logger.LogInformation("Seed data inserted successfully");
        }
    }
}
