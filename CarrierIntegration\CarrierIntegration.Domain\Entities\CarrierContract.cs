using CarrierIntegration.Domain.Common;
using CarrierIntegration.Domain.ValueObjects;
using System;
using System.Collections.Generic;

namespace CarrierIntegration.Domain.Entities
{
    public class CarrierContract : BaseEntity
    {
        public Guid CarrierId { get; private set; }
        public string ContractNumber { get; private set; }
        public string ContractName { get; private set; }
        public string ContractType { get; private set; } // Master, Spot, Volume, Dedicated
        public string Status { get; private set; } // Draft, Active, Expired, Terminated, Suspended
        public DateTime EffectiveDate { get; private set; }
        public DateTime ExpirationDate { get; private set; }
        public DateTime? TerminationDate { get; private set; }
        public string? TerminationReason { get; private set; }
        public bool AutoRenewal { get; private set; }
        public int? AutoRenewalDays { get; private set; }
        public string? RenewalTerms { get; private set; }
        
        // Financial Terms
        public Money? MinimumCommitment { get; private set; }
        public Money? MaximumLiability { get; private set; }
        public string PaymentTerms { get; private set; } = null!;
        public int PaymentDays { get; private set; }
        public string Currency { get; private set; } = "USD";
        public decimal? VolumeDiscountThreshold { get; private set; }
        public decimal? VolumeDiscountRate { get; private set; }
        public string? InvoicingFrequency { get; private set; }
        public string? BillingMethod { get; private set; }
        
        // Service Level Agreements
        public decimal? OnTimeDeliveryTarget { get; private set; }
        public decimal? OnTimeDeliveryPenalty { get; private set; }
        public decimal? DamageClaimLimit { get; private set; }
        public int? ClaimResolutionDays { get; private set; }
        public decimal? ServiceLevelCredit { get; private set; }
        public string? PerformanceIncentives { get; private set; }
        public string? QualityStandards { get; private set; }
        
        // Coverage and Restrictions
        public string? GeographicCoverage { get; private set; }
        public string? ServiceTypes { get; private set; }
        public string? ExcludedServices { get; private set; }
        public string? WeightLimitations { get; private set; }
        public string? DimensionLimitations { get; private set; }
        public string? CommodityRestrictions { get; private set; }
        public string? SpecialHandlingTerms { get; private set; }
        
        // Legal and Compliance
        public string? LiabilityTerms { get; private set; }
        public string? InsuranceRequirements { get; private set; }
        public string? ComplianceRequirements { get; private set; }
        public string? DisputeResolution { get; private set; }
        public string? GoverningLaw { get; private set; }
        public string? ForceMateure { get; private set; }
        
        // Contract Management
        public string? ContractOwner { get; private set; }
        public string? CarrierRepresentative { get; private set; }
        public string? LegalReviewStatus { get; private set; }
        public DateTime? LastReviewDate { get; private set; }
        public DateTime? NextReviewDate { get; private set; }
        public string? ReviewNotes { get; private set; }
        public string? Amendments { get; private set; }
        public string? AttachedDocuments { get; private set; }
        public string? Notes { get; private set; }
        
        // Performance Tracking
        public decimal CurrentVolumeCommitment { get; private set; }
        public decimal ActualVolume { get; private set; }
        public decimal CommitmentPercentage { get; private set; }
        public decimal CurrentSpend { get; private set; }
        public decimal AveragePerformanceScore { get; private set; }
        public int TotalShipments { get; private set; }
        public int OnTimeShipments { get; private set; }
        public int ClaimsCount { get; private set; }
        public Money? TotalClaimsValue { get; private set; }

        private CarrierContract() { } // For EF Core

        public CarrierContract(
            Guid carrierId,
            Guid organizationId,
            string contractNumber,
            string contractName,
            string contractType,
            DateTime effectiveDate,
            DateTime expirationDate,
            string paymentTerms,
            int paymentDays,
            string createdBy = "System") : base(organizationId, createdBy)
        {
            if (carrierId == Guid.Empty)
                throw new DomainException("Carrier ID is required");
            if (string.IsNullOrWhiteSpace(contractNumber))
                throw new DomainException("Contract number is required");
            if (string.IsNullOrWhiteSpace(contractName))
                throw new DomainException("Contract name is required");
            if (string.IsNullOrWhiteSpace(contractType))
                throw new DomainException("Contract type is required");
            if (effectiveDate >= expirationDate)
                throw new DomainException("Effective date must be before expiration date");
            if (string.IsNullOrWhiteSpace(paymentTerms))
                throw new DomainException("Payment terms are required");
            if (paymentDays <= 0)
                throw new DomainException("Payment days must be greater than zero");

            CarrierId = carrierId;
            ContractNumber = contractNumber.Trim();
            ContractName = contractName.Trim();
            ContractType = contractType.Trim();
            EffectiveDate = effectiveDate;
            ExpirationDate = expirationDate;
            PaymentTerms = paymentTerms.Trim();
            PaymentDays = paymentDays;
            Status = "Draft";
            AutoRenewal = false;
            CurrentVolumeCommitment = 0;
            ActualVolume = 0;
            CommitmentPercentage = 0;
            CurrentSpend = 0;
            AveragePerformanceScore = 0;
            TotalShipments = 0;
            OnTimeShipments = 0;
            ClaimsCount = 0;
        }

        public void UpdateBasicInfo(
            string contractName,
            string contractType,
            DateTime effectiveDate,
            DateTime expirationDate,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(contractName))
                throw new DomainException("Contract name is required");
            if (string.IsNullOrWhiteSpace(contractType))
                throw new DomainException("Contract type is required");
            if (effectiveDate >= expirationDate)
                throw new DomainException("Effective date must be before expiration date");

            ContractName = contractName.Trim();
            ContractType = contractType.Trim();
            EffectiveDate = effectiveDate;
            ExpirationDate = expirationDate;

            Update(updatedBy);
        }

        public void UpdateFinancialTerms(
            Money? minimumCommitment,
            Money? maximumLiability,
            string paymentTerms,
            int paymentDays,
            string currency,
            decimal? volumeDiscountThreshold,
            decimal? volumeDiscountRate,
            string? invoicingFrequency,
            string? billingMethod,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(paymentTerms))
                throw new DomainException("Payment terms are required");
            if (paymentDays <= 0)
                throw new DomainException("Payment days must be greater than zero");
            if (volumeDiscountThreshold.HasValue && volumeDiscountThreshold.Value < 0)
                throw new DomainException("Volume discount threshold cannot be negative");
            if (volumeDiscountRate.HasValue && (volumeDiscountRate.Value < 0 || volumeDiscountRate.Value > 100))
                throw new DomainException("Volume discount rate must be between 0 and 100");

            MinimumCommitment = minimumCommitment;
            MaximumLiability = maximumLiability;
            PaymentTerms = paymentTerms.Trim();
            PaymentDays = paymentDays;
            Currency = currency.Trim();
            VolumeDiscountThreshold = volumeDiscountThreshold;
            VolumeDiscountRate = volumeDiscountRate;
            InvoicingFrequency = invoicingFrequency?.Trim();
            BillingMethod = billingMethod?.Trim();

            Update(updatedBy);
        }

        public void UpdateServiceLevelAgreements(
            decimal? onTimeDeliveryTarget,
            decimal? onTimeDeliveryPenalty,
            decimal? damageClaimLimit,
            int? claimResolutionDays,
            decimal? serviceLevelCredit,
            string? performanceIncentives,
            string? qualityStandards,
            string updatedBy)
        {
            if (onTimeDeliveryTarget.HasValue && (onTimeDeliveryTarget.Value < 0 || onTimeDeliveryTarget.Value > 100))
                throw new DomainException("On-time delivery target must be between 0 and 100");
            if (onTimeDeliveryPenalty.HasValue && onTimeDeliveryPenalty.Value < 0)
                throw new DomainException("On-time delivery penalty cannot be negative");
            if (claimResolutionDays.HasValue && claimResolutionDays.Value <= 0)
                throw new DomainException("Claim resolution days must be greater than zero");

            OnTimeDeliveryTarget = onTimeDeliveryTarget;
            OnTimeDeliveryPenalty = onTimeDeliveryPenalty;
            DamageClaimLimit = damageClaimLimit;
            ClaimResolutionDays = claimResolutionDays;
            ServiceLevelCredit = serviceLevelCredit;
            PerformanceIncentives = performanceIncentives?.Trim();
            QualityStandards = qualityStandards?.Trim();

            Update(updatedBy);
        }

        public void UpdateCoverageAndRestrictions(
            string? geographicCoverage,
            string? serviceTypes,
            string? excludedServices,
            string? weightLimitations,
            string? dimensionLimitations,
            string? commodityRestrictions,
            string? specialHandlingTerms,
            string updatedBy)
        {
            GeographicCoverage = geographicCoverage?.Trim();
            ServiceTypes = serviceTypes?.Trim();
            ExcludedServices = excludedServices?.Trim();
            WeightLimitations = weightLimitations?.Trim();
            DimensionLimitations = dimensionLimitations?.Trim();
            CommodityRestrictions = commodityRestrictions?.Trim();
            SpecialHandlingTerms = specialHandlingTerms?.Trim();

            Update(updatedBy);
        }

        public void UpdateLegalTerms(
            string? liabilityTerms,
            string? insuranceRequirements,
            string? complianceRequirements,
            string? disputeResolution,
            string? governingLaw,
            string? forceMateure,
            string updatedBy)
        {
            LiabilityTerms = liabilityTerms?.Trim();
            InsuranceRequirements = insuranceRequirements?.Trim();
            ComplianceRequirements = complianceRequirements?.Trim();
            DisputeResolution = disputeResolution?.Trim();
            GoverningLaw = governingLaw?.Trim();
            ForceMateure = forceMateure?.Trim();

            Update(updatedBy);
        }

        public void UpdateContractManagement(
            string? contractOwner,
            string? carrierRepresentative,
            DateTime? nextReviewDate,
            string? reviewNotes,
            string? attachedDocuments,
            string? notes,
            string updatedBy)
        {
            ContractOwner = contractOwner?.Trim();
            CarrierRepresentative = carrierRepresentative?.Trim();
            NextReviewDate = nextReviewDate;
            ReviewNotes = reviewNotes?.Trim();
            AttachedDocuments = attachedDocuments?.Trim();
            Notes = notes?.Trim();

            Update(updatedBy);
        }

        public void SetAutoRenewal(bool autoRenewal, int? autoRenewalDays, string? renewalTerms, string updatedBy)
        {
            if (autoRenewal && (!autoRenewalDays.HasValue || autoRenewalDays.Value <= 0))
                throw new DomainException("Auto renewal days must be specified and greater than zero when auto renewal is enabled");

            AutoRenewal = autoRenewal;
            AutoRenewalDays = autoRenewal ? autoRenewalDays : null;
            RenewalTerms = autoRenewal ? renewalTerms?.Trim() : null;

            Update(updatedBy);
        }

        public void Activate(string activatedBy)
        {
            if (Status == "Active")
                throw new DomainException("Contract is already active");
            if (DateTime.UtcNow < EffectiveDate)
                throw new DomainException("Cannot activate contract before effective date");
            if (DateTime.UtcNow > ExpirationDate)
                throw new DomainException("Cannot activate expired contract");

            Status = "Active";
            Update(activatedBy);
        }

        public void Suspend(string reason, string suspendedBy)
        {
            if (Status != "Active")
                throw new DomainException("Only active contracts can be suspended");
            if (string.IsNullOrWhiteSpace(reason))
                throw new DomainException("Suspension reason is required");

            Status = "Suspended";
            Notes = string.IsNullOrWhiteSpace(Notes) ? $"Suspended: {reason}" : $"{Notes}\n\nSuspended: {reason}";
            Update(suspendedBy);
        }

        public void Terminate(string reason, DateTime? terminationDate, string terminatedBy)
        {
            if (Status == "Terminated")
                throw new DomainException("Contract is already terminated");
            if (string.IsNullOrWhiteSpace(reason))
                throw new DomainException("Termination reason is required");

            Status = "Terminated";
            TerminationDate = terminationDate ?? DateTime.UtcNow;
            TerminationReason = reason.Trim();
            Update(terminatedBy);
        }

        public void UpdatePerformanceMetrics(
            decimal volumeCommitment,
            decimal actualVolume,
            decimal currentSpend,
            decimal averagePerformanceScore,
            int totalShipments,
            int onTimeShipments,
            int claimsCount,
            Money? totalClaimsValue,
            string updatedBy)
        {
            if (volumeCommitment < 0)
                throw new DomainException("Volume commitment cannot be negative");
            if (actualVolume < 0)
                throw new DomainException("Actual volume cannot be negative");
            if (currentSpend < 0)
                throw new DomainException("Current spend cannot be negative");
            if (averagePerformanceScore < 0 || averagePerformanceScore > 100)
                throw new DomainException("Average performance score must be between 0 and 100");

            CurrentVolumeCommitment = volumeCommitment;
            ActualVolume = actualVolume;
            CommitmentPercentage = volumeCommitment > 0 ? (actualVolume / volumeCommitment) * 100 : 0;
            CurrentSpend = currentSpend;
            AveragePerformanceScore = averagePerformanceScore;
            TotalShipments = totalShipments;
            OnTimeShipments = onTimeShipments;
            ClaimsCount = claimsCount;
            TotalClaimsValue = totalClaimsValue;

            Update(updatedBy);
        }

        public void MarkForReview(DateTime reviewDate, string reviewedBy)
        {
            LastReviewDate = reviewDate;
            LegalReviewStatus = "Under Review";
            Update(reviewedBy);
        }

        public void CompleteReview(string reviewStatus, string? reviewNotes, DateTime? nextReviewDate, string reviewedBy)
        {
            LegalReviewStatus = reviewStatus;
            ReviewNotes = reviewNotes?.Trim();
            NextReviewDate = nextReviewDate;
            LastReviewDate = DateTime.UtcNow;
            Update(reviewedBy);
        }

        public bool IsActive()
        {
            return Status == "Active" && DateTime.UtcNow >= EffectiveDate && DateTime.UtcNow <= ExpirationDate;
        }

        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpirationDate;
        }

        public bool IsExpiringSoon(int daysThreshold = 30)
        {
            return DateTime.UtcNow.AddDays(daysThreshold) >= ExpirationDate;
        }

        public bool RequiresRenewal()
        {
            return AutoRenewal && IsExpiringSoon(AutoRenewalDays ?? 30);
        }

        public decimal GetOnTimePerformance()
        {
            return TotalShipments > 0 ? (decimal)OnTimeShipments / TotalShipments * 100 : 0;
        }

        public decimal GetClaimsRate()
        {
            return TotalShipments > 0 ? (decimal)ClaimsCount / TotalShipments * 100 : 0;
        }

        public bool MeetsPerformanceTargets()
        {
            if (!OnTimeDeliveryTarget.HasValue) return true;
            return GetOnTimePerformance() >= OnTimeDeliveryTarget.Value;
        }

        public decimal CalculateVolumeDiscountEligibility()
        {
            if (!VolumeDiscountThreshold.HasValue || !VolumeDiscountRate.HasValue)
                return 0;

            return ActualVolume >= VolumeDiscountThreshold.Value ? VolumeDiscountRate.Value : 0;
        }
    }
}
