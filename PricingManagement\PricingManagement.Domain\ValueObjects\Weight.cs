using PricingManagement.Domain.Exceptions;
using System;
using System.Collections.Generic;

namespace PricingManagement.Domain.ValueObjects
{
    public enum WeightUnit
    {
        Pounds = 1,
        Kilograms = 2,
        Ounces = 3,
        Grams = 4
    }

    public class Weight : IEquatable<Weight>
    {
        public decimal Value { get; }
        public WeightUnit Unit { get; }

        public Weight(decimal value, WeightUnit unit)
        {
            if (value < 0)
                throw new DomainException("Weight cannot be negative");

            Value = Math.Round(value, 3);
            Unit = unit;
        }

        public Weight ConvertTo(WeightUnit targetUnit)
        {
            if (Unit == targetUnit)
                return this;

            var valueInKg = Unit switch
            {
                WeightUnit.Kilograms => Value,
                WeightUnit.Pounds => Value * 0.453592m,
                WeightUnit.Ounces => Value * 0.0283495m,
                WeightUnit.Grams => Value * 0.001m,
                _ => throw new DomainException($"Unknown weight unit: {Unit}")
            };

            var convertedValue = targetUnit switch
            {
                WeightUnit.Kilograms => valueInKg,
                WeightUnit.Pounds => valueInKg / 0.453592m,
                WeightUnit.Ounces => valueInKg / 0.0283495m,
                WeightUnit.Grams => valueInKg / 0.001m,
                _ => throw new DomainException($"Unknown weight unit: {targetUnit}")
            };

            return new Weight(convertedValue, targetUnit);
        }

        public Weight Add(Weight other)
        {
            var otherConverted = other.ConvertTo(Unit);
            return new Weight(Value + otherConverted.Value, Unit);
        }

        public bool Equals(Weight? other)
        {
            if (other is null) return false;
            var otherConverted = other.ConvertTo(Unit);
            return Math.Abs(Value - otherConverted.Value) < 0.001m;
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as Weight);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Value, Unit);
        }

        public static bool operator ==(Weight? left, Weight? right)
        {
            return EqualityComparer<Weight>.Default.Equals(left, right);
        }

        public static bool operator !=(Weight? left, Weight? right)
        {
            return !(left == right);
        }

        public static bool operator >(Weight left, Weight right)
        {
            var rightConverted = right.ConvertTo(left.Unit);
            return left.Value > rightConverted.Value;
        }

        public static bool operator <(Weight left, Weight right)
        {
            var rightConverted = right.ConvertTo(left.Unit);
            return left.Value < rightConverted.Value;
        }

        public static bool operator >=(Weight left, Weight right)
        {
            return left > right || left == right;
        }

        public static bool operator <=(Weight left, Weight right)
        {
            return left < right || left == right;
        }

        public override string ToString()
        {
            return $"{Value} {Unit}";
        }
    }
}
