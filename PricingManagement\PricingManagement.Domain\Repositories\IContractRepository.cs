using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PricingManagement.Domain.Repositories
{
    public interface IContractRepository
    {
        Task<Contract?> GetByIdAsync(Guid id);
        Task<Contract?> GetByIdAsync(Guid id, Guid organizationId);
        Task<Contract?> GetByContractNumberAsync(string contractNumber, Guid organizationId);
        Task<IEnumerable<Contract>> GetByOrganizationAsync(Guid organizationId);
        Task<IEnumerable<Contract>> GetByCustomerAsync(Guid customerId, Guid organizationId);
        Task<IEnumerable<Contract>> GetByShipperAsync(Guid shipperId, Guid organizationId);
        Task<IEnumerable<Contract>> GetByStatusAsync(ContractStatus status, Guid organizationId);
        Task<IEnumerable<Contract>> GetActiveContractsAsync(Guid organizationId);
        Task<IEnumerable<Contract>> GetExpiringContractsAsync(Guid organizationId, DateTime beforeDate);
        Task<IEnumerable<Contract>> GetContractsByDateRangeAsync(Guid organizationId, DateTime fromDate, DateTime toDate);
        Task<IEnumerable<Contract>> GetContractsForReviewAsync(Guid organizationId, DateTime beforeDate);
        Task<IEnumerable<Contract>> GetAmendmentsAsync(Guid parentContractId, Guid organizationId);
        Task<Contract?> GetActiveContractForCustomerAsync(Guid customerId, Guid organizationId);
        Task<Contract?> GetActiveContractForShipperAsync(Guid shipperId, Guid organizationId);
        Task<bool> ExistsAsync(Guid id, Guid organizationId);
        Task<bool> ContractNumberExistsAsync(string contractNumber, Guid organizationId);
        Task<string> GenerateContractNumberAsync(Guid organizationId);
        Task AddAsync(Contract contract);
        Task UpdateAsync(Contract contract);
        Task DeleteAsync(Contract contract);
        Task<int> CountByOrganizationAsync(Guid organizationId);
        Task<int> CountByStatusAsync(ContractStatus status, Guid organizationId);
        Task<int> CountActiveByOrganizationAsync(Guid organizationId);
    }
}
