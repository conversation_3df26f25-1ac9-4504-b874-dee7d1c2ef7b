using AutoMapper;
using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Features.CarrierBookings.Commands
{
    public class ConfirmCarrierBookingCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
        public DateTime? ConfirmedPickupDate { get; set; }
        public DateTime? ConfirmedDeliveryDate { get; set; }
        public string? CarrierBookingReference { get; set; }
        public string? TrackingNumber { get; set; }
        public string? LabelUrl { get; set; }
        public MoneyDto? EstimatedCost { get; set; }
        public int? EstimatedTransitDays { get; set; }
        public string? CarrierServiceCode { get; set; }
        public string? CarrierServiceName { get; set; }
        public string? CarrierNotes { get; set; }
        public string UpdatedBy { get; set; } = "System";
    }

    public class ConfirmCarrierBookingCommandValidator : AbstractValidator<ConfirmCarrierBookingCommand>
    {
        private readonly IUnitOfWork _unitOfWork;

        public ConfirmCarrierBookingCommandValidator(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

            RuleFor(x => x.Id)
                .NotEmpty()
                .WithMessage("Booking ID is required")
                .MustAsync(BeValidBookingForConfirmation)
                .WithMessage("Booking does not exist or cannot be confirmed");

            RuleFor(x => x.ConfirmedPickupDate)
                .GreaterThanOrEqualTo(DateTime.Today)
                .When(x => x.ConfirmedPickupDate.HasValue)
                .WithMessage("Confirmed pickup date cannot be in the past");

            RuleFor(x => x.ConfirmedDeliveryDate)
                .GreaterThan(x => x.ConfirmedPickupDate)
                .When(x => x.ConfirmedDeliveryDate.HasValue && x.ConfirmedPickupDate.HasValue)
                .WithMessage("Confirmed delivery date must be after pickup date");

            RuleFor(x => x.EstimatedCost!.Amount)
                .GreaterThan(0)
                .When(x => x.EstimatedCost != null)
                .WithMessage("Estimated cost must be greater than zero");

            RuleFor(x => x.EstimatedTransitDays)
                .GreaterThan(0)
                .When(x => x.EstimatedTransitDays.HasValue)
                .WithMessage("Estimated transit days must be greater than zero");
        }

        private async Task<bool> BeValidBookingForConfirmation(Guid bookingId, CancellationToken cancellationToken)
        {
            var booking = await _unitOfWork.CarrierBookings.GetByIdAsync(bookingId);
            return booking != null && booking.Status == Domain.Enums.BookingStatus.Requested;
        }
    }

    public class ConfirmCarrierBookingCommandHandler : IRequestHandler<ConfirmCarrierBookingCommand, ApiResponseDto<bool>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<ConfirmCarrierBookingCommandHandler> _logger;

        public ConfirmCarrierBookingCommandHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<ConfirmCarrierBookingCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ApiResponseDto<bool>> Handle(ConfirmCarrierBookingCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Confirming carrier booking {BookingId}", request.Id);

                var booking = await _unitOfWork.CarrierBookings.GetByIdAsync(request.Id);
                if (booking == null)
                {
                    return ApiResponseDto<bool>.ErrorResult("Booking not found");
                }

                if (booking.Status != Domain.Enums.BookingStatus.Requested)
                {
                    return ApiResponseDto<bool>.ErrorResult($"Cannot confirm booking with status {booking.Status}");
                }

                // Update carrier response information
                if (!string.IsNullOrWhiteSpace(request.CarrierBookingReference) ||
                    !string.IsNullOrWhiteSpace(request.TrackingNumber) ||
                    !string.IsNullOrWhiteSpace(request.LabelUrl))
                {
                    booking.UpdateCarrierResponse(
                        request.CarrierBookingReference,
                        request.TrackingNumber,
                        request.LabelUrl,
                        null, // DocumentsUrl
                        null, // ManifestNumber
                        null, // BillOfLadingNumber
                        null, // ProNumber
                        request.UpdatedBy);
                }

                // Update cost information
                if (request.EstimatedCost != null || request.EstimatedTransitDays.HasValue)
                {
                    var estimatedCost = request.EstimatedCost != null ? _mapper.Map<Domain.ValueObjects.Money>(request.EstimatedCost) : null;
                    
                    booking.UpdateCostInfo(
                        estimatedCost,
                        null, // ActualCost
                        null, // CostBreakdown
                        request.EstimatedTransitDays,
                        request.UpdatedBy);
                }

                // Update carrier service information
                if (!string.IsNullOrWhiteSpace(request.CarrierServiceCode) || !string.IsNullOrWhiteSpace(request.CarrierServiceName))
                {
                    booking.UpdateCarrierInfo(
                        booking.CarrierAccountId,
                        booking.CarrierServiceId,
                        request.CarrierServiceCode,
                        request.CarrierServiceName,
                        request.UpdatedBy);
                }

                // Update notes if provided
                if (!string.IsNullOrWhiteSpace(request.CarrierNotes))
                {
                    booking.UpdateNotes(
                        request.CarrierNotes,
                        booking.InternalNotes,
                        request.UpdatedBy);
                }

                // Confirm the booking
                booking.Confirm(
                    request.ConfirmedPickupDate,
                    request.ConfirmedDeliveryDate,
                    request.CarrierBookingReference,
                    request.UpdatedBy);

                // Save to database
                await _unitOfWork.CarrierBookings.UpdateAsync(booking);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully confirmed carrier booking {BookingId} with reference {BookingReference}",
                    booking.Id, booking.BookingReference);

                return ApiResponseDto<bool>.SuccessResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming carrier booking {BookingId}", request.Id);
                return ApiResponseDto<bool>.ErrorResult($"Failed to confirm carrier booking: {ex.Message}");
            }
        }
    }
}
