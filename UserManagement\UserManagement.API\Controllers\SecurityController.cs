using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using UserManagement.API.Models.Requests;
using UserManagement.Application.Common.Models;
using UserManagement.Application.Security.Commands.UpdateSecuritySettings;
using UserManagement.Application.Security.Commands.ManageIpRestrictions;
using UserManagement.Application.Security.Commands.ConfigureMultiFactorAuth;
using UserManagement.Application.Security.Commands.ManageUserSessions;
using UserManagement.Application.Security.Commands.UpdatePasswordPolicy;
using UserManagement.Application.Security.Queries.GetSecurityAuditLogs;
using UserManagement.Application.Security.Queries.GetSecuritySettings;
using UserManagement.Application.Security.Queries.GetIpRestrictions;
using UserManagement.Application.Security.Queries.GetMultiFactorAuthConfig;
using UserManagement.Application.Security.Queries.GetUserSessions;
using UserManagement.Application.Security.Queries.GetPasswordPolicy;

namespace UserManagement.API.Controllers
{
    [ApiVersion("1.0")]
    [Authorize]
    public class SecurityController : BaseApiController
    {
        [HttpGet("organizations/{organizationId}/audit-logs")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<List<SecurityAuditLogDto>>> GetAuditLogs(
            Guid organizationId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string eventType = null,
            [FromQuery] Guid? userId = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var query = new GetSecurityAuditLogsQuery
            {
                OrganizationId = organizationId,
                StartDate = startDate,
                EndDate = endDate,
                EventType = eventType,
                UserId = userId,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var logs = await Mediator.Send(query);
            return Ok(logs);
        }

        [HttpGet("organizations/{organizationId}/settings")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<SecuritySettingsDto>> GetSecuritySettings(Guid organizationId)
        {
            var query = new GetSecuritySettingsQuery { OrganizationId = organizationId };
            var settings = await Mediator.Send(query);
            return Ok(settings);
        }

        [HttpPut("organizations/{organizationId}/settings")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult> UpdateSecuritySettings(
            Guid organizationId,
            [FromBody] UpdateSecuritySettingsRequest request)
        {
            var command = new UpdateSecuritySettingsCommand
            {
                OrganizationId = organizationId,
                RequireMfa = request.RequireMfa,
                SessionTimeoutMinutes = request.SessionTimeoutMinutes,
                MaxLoginAttempts = request.MaxLoginAttempts,
                LockoutDurationMinutes = request.LockoutDurationMinutes,
                PasswordExpiryDays = request.PasswordExpiryDays,
                EnableIpRestrictions = request.EnableIpRestrictions,
                EnableAuditLogging = request.EnableAuditLogging,
                EnableRiskBasedAuthentication = request.EnableRiskBasedAuthentication
            };

            await Mediator.Send(command);
            return Ok();
        }

        [HttpGet("organizations/{organizationId}/ip-restrictions")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<IpRestrictionsDto>> GetIpRestrictions(Guid organizationId)
        {
            var query = new GetIpRestrictionsQuery { OrganizationId = organizationId };
            var restrictions = await Mediator.Send(query);
            return Ok(restrictions);
        }

        [HttpPut("organizations/{organizationId}/ip-restrictions")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult> ManageIpRestrictions(
            Guid organizationId,
            [FromBody] ManageIpRestrictionsRequest request)
        {
            var command = new ManageIpRestrictionsCommand
            {
                OrganizationId = organizationId,
                AllowedIpRanges = request.AllowedIpRanges,
                BlockedIpRanges = request.BlockedIpRanges,
                DefaultPolicy = request.DefaultPolicy
            };

            await Mediator.Send(command);
            return Ok();
        }

        [HttpGet("organizations/{organizationId}/mfa-config")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<MultiFactorAuthConfigDto>> GetMultiFactorAuthConfig(Guid organizationId)
        {
            var query = new GetMultiFactorAuthConfigQuery { OrganizationId = organizationId };
            var config = await Mediator.Send(query);
            return Ok(config);
        }

        [HttpPut("organizations/{organizationId}/mfa-config")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult> ConfigureMultiFactorAuth(
            Guid organizationId,
            [FromBody] ConfigureMultiFactorAuthRequest request)
        {
            var command = new ConfigureMultiFactorAuthCommand
            {
                OrganizationId = organizationId,
                RequireMfa = request.RequireMfa,
                AllowedMethods = request.AllowedMethods,
                RememberDeviceDays = request.RememberDeviceDays,
                CodeExpirationMinutes = request.CodeExpirationMinutes
            };

            await Mediator.Send(command);
            return Ok();
        }

        [HttpGet("users/{userId}/sessions")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<List<UserSessionDto>>> GetUserSessions(Guid userId)
        {
            var query = new GetUserSessionsQuery { UserId = userId };
            var sessions = await Mediator.Send(query);
            return Ok(sessions);
        }

        [HttpPost("users/{userId}/sessions/terminate")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult> TerminateUserSessions(
            Guid userId,
            [FromBody] ManageUserSessionsRequest request)
        {
            var command = new ManageUserSessionsCommand
            {
                UserId = userId,
                SessionIds = request.SessionIds,
                TerminateAll = request.TerminateAll
            };

            await Mediator.Send(command);
            return Ok();
        }

        [HttpGet("organizations/{organizationId}/password-policy")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult<PasswordPolicyDto>> GetPasswordPolicy(Guid organizationId)
        {
            var query = new GetPasswordPolicyQuery { OrganizationId = organizationId };
            var policy = await Mediator.Send(query);
            return Ok(policy);
        }

        [HttpPut("organizations/{organizationId}/password-policy")]
        [Authorize(Roles = "Admin")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<ActionResult> UpdatePasswordPolicy(
            Guid organizationId,
            [FromBody] UpdatePasswordPolicyRequest request)
        {
            var command = new UpdatePasswordPolicyCommand
            {
                OrganizationId = organizationId,
                MinLength = request.MinLength,
                RequireUppercase = request.RequireUppercase,
                RequireLowercase = request.RequireLowercase,
                RequireDigits = request.RequireDigits,
                RequireSpecialCharacters = request.RequireSpecialCharacters,
                PreventPasswordReuse = request.PreventPasswordReuse,
                PasswordHistoryCount = request.PasswordHistoryCount,
                ExpiryDays = request.ExpiryDays
            };

            await Mediator.Send(command);
            return Ok();
        }
    }
}
