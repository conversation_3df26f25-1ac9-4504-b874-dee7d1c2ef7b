using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;

namespace PricingManagement.Infrastructure.Persistence.Configurations
{
    public class ContractConfiguration : IEntityTypeConfiguration<Contract>
    {
        public void Configure(EntityTypeBuilder<Contract> builder)
        {
            builder.ToTable("Contracts");

            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id)
                .ValueGeneratedNever();

            builder.Property(x => x.ContractNumber)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(x => x.Description)
                .IsRequired()
                .HasMaxLength(1000);

            builder.Property(x => x.CustomerId);

            builder.Property(x => x.ShipperId);

            builder.Property(x => x.CustomerName)
                .HasMaxLength(200);

            builder.Property(x => x.ShipperName)
                .HasMaxLength(200);

            builder.Property(x => x.Status)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(x => x.EffectiveDate)
                .IsRequired();

            builder.Property(x => x.ExpirationDate)
                .IsRequired();

            builder.Property(x => x.Currency)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(x => x.Terms)
                .HasMaxLength(2000);

            builder.Property(x => x.PaymentTerms)
                .HasMaxLength(500);

            builder.Property(x => x.MinimumCommitment)
                .HasPrecision(18, 2);

            builder.Property(x => x.MaximumCommitment)
                .HasPrecision(18, 2);

            builder.Property(x => x.CommitmentType)
                .HasMaxLength(50);

            builder.Property(x => x.CommitmentPeriod)
                .HasPrecision(18, 2);

            builder.Property(x => x.AutoRenewal)
                .IsRequired();

            builder.Property(x => x.AutoRenewalPeriod);

            builder.Property(x => x.NotificationPeriod)
                .HasMaxLength(100);

            builder.Property(x => x.ServiceTypes)
                .HasColumnType("jsonb");

            builder.Property(x => x.GeographicScope)
                .HasColumnType("jsonb");

            builder.Property(x => x.SpecialProvisions)
                .HasMaxLength(2000);

            builder.Property(x => x.ContractDocument)
                .HasMaxLength(500);

            builder.Property(x => x.LastReviewDate);

            builder.Property(x => x.NextReviewDate);

            builder.Property(x => x.ReviewNotes)
                .HasMaxLength(2000);

            builder.Property(x => x.Version)
                .IsRequired();

            builder.Property(x => x.ParentContractId);

            // Base entity properties
            builder.Property(x => x.CreatedAt)
                .IsRequired();

            builder.Property(x => x.UpdatedAt);

            builder.Property(x => x.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(x => x.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(x => x.DeletedAt);

            builder.Property(x => x.DeletedBy)
                .HasMaxLength(100);

            builder.Property(x => x.OrganizationId)
                .IsRequired();

            // Owned entities for rates
            builder.OwnsMany(x => x.Rates, rateBuilder =>
            {
                rateBuilder.ToTable("ContractRates");
                
                rateBuilder.WithOwner().HasForeignKey("ContractId");
                
                rateBuilder.Property<int>("Id")
                    .ValueGeneratedOnAdd();
                
                rateBuilder.HasKey("Id");

                rateBuilder.Property(r => r.ServiceType)
                    .IsRequired()
                    .HasMaxLength(100);

                rateBuilder.Property(r => r.OriginZone)
                    .HasMaxLength(50);

                rateBuilder.Property(r => r.DestinationZone)
                    .HasMaxLength(50);

                rateBuilder.Property(r => r.MinWeight)
                    .HasPrecision(18, 3);

                rateBuilder.Property(r => r.MaxWeight)
                    .HasPrecision(18, 3);

                rateBuilder.OwnsOne(r => r.Rate, moneyBuilder =>
                {
                    moneyBuilder.Property(m => m.Amount)
                        .HasColumnName("RateAmount")
                        .HasPrecision(18, 4);

                    moneyBuilder.Property(m => m.Currency)
                        .HasColumnName("RateCurrency")
                        .HasConversion<int>();
                });

                rateBuilder.Property(r => r.RateType)
                    .HasMaxLength(50);

                rateBuilder.Property(r => r.EffectiveDate);

                rateBuilder.Property(r => r.ExpirationDate);
            });

            // Owned entities for discounts
            builder.OwnsMany(x => x.Discounts, discountBuilder =>
            {
                discountBuilder.ToTable("ContractDiscounts");
                
                discountBuilder.WithOwner().HasForeignKey("ContractId");
                
                discountBuilder.Property<int>("Id")
                    .ValueGeneratedOnAdd();
                
                discountBuilder.HasKey("Id");

                discountBuilder.Property(d => d.DiscountType)
                    .IsRequired()
                    .HasConversion<int>();

                discountBuilder.Property(d => d.Description)
                    .IsRequired()
                    .HasMaxLength(500);

                discountBuilder.Property(d => d.Percentage)
                    .HasPrecision(5, 2);

                discountBuilder.OwnsOne(d => d.FixedAmount, moneyBuilder =>
                {
                    moneyBuilder.Property(m => m.Amount)
                        .HasColumnName("FixedAmount")
                        .HasPrecision(18, 4);

                    moneyBuilder.Property(m => m.Currency)
                        .HasColumnName("FixedAmountCurrency")
                        .HasConversion<int>();
                });

                discountBuilder.Property(d => d.MinVolume)
                    .HasPrecision(18, 3);

                discountBuilder.Property(d => d.MaxVolume)
                    .HasPrecision(18, 3);

                discountBuilder.Property(d => d.EffectiveDate);

                discountBuilder.Property(d => d.ExpirationDate);
            });

            // Owned entities for surcharges
            builder.OwnsMany(x => x.Surcharges, surchargeBuilder =>
            {
                surchargeBuilder.ToTable("ContractSurcharges");
                
                surchargeBuilder.WithOwner().HasForeignKey("ContractId");
                
                surchargeBuilder.Property<int>("Id")
                    .ValueGeneratedOnAdd();
                
                surchargeBuilder.HasKey("Id");

                surchargeBuilder.Property(s => s.SurchargeType)
                    .IsRequired()
                    .HasConversion<int>();

                surchargeBuilder.Property(s => s.Description)
                    .IsRequired()
                    .HasMaxLength(500);

                surchargeBuilder.OwnsOne(s => s.Amount, moneyBuilder =>
                {
                    moneyBuilder.Property(m => m.Amount)
                        .HasColumnName("SurchargeAmount")
                        .HasPrecision(18, 4);

                    moneyBuilder.Property(m => m.Currency)
                        .HasColumnName("SurchargeCurrency")
                        .HasConversion<int>();
                });

                surchargeBuilder.Property(s => s.CalculationBasis)
                    .HasMaxLength(100);

                surchargeBuilder.Property(s => s.EffectiveDate);

                surchargeBuilder.Property(s => s.ExpirationDate);
            });

            // Owned entities for commitments
            builder.OwnsMany(x => x.Commitments, commitmentBuilder =>
            {
                commitmentBuilder.ToTable("ContractCommitments");
                
                commitmentBuilder.WithOwner().HasForeignKey("ContractId");
                
                commitmentBuilder.Property<int>("Id")
                    .ValueGeneratedOnAdd();
                
                commitmentBuilder.HasKey("Id");

                commitmentBuilder.Property(c => c.CommitmentType)
                    .IsRequired()
                    .HasMaxLength(100);

                commitmentBuilder.Property(c => c.TargetValue)
                    .IsRequired()
                    .HasPrecision(18, 2);

                commitmentBuilder.Property(c => c.MinimumValue)
                    .HasPrecision(18, 2);

                commitmentBuilder.Property(c => c.MaximumValue)
                    .HasPrecision(18, 2);

                commitmentBuilder.Property(c => c.Period)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            // Indexes
            builder.HasIndex(x => x.OrganizationId);
            builder.HasIndex(x => new { x.OrganizationId, x.ContractNumber }).IsUnique();
            builder.HasIndex(x => new { x.OrganizationId, x.Status });
            builder.HasIndex(x => new { x.OrganizationId, x.CustomerId });
            builder.HasIndex(x => new { x.OrganizationId, x.ShipperId });
            builder.HasIndex(x => new { x.OrganizationId, x.EffectiveDate });
            builder.HasIndex(x => new { x.OrganizationId, x.ExpirationDate });
            builder.HasIndex(x => new { x.OrganizationId, x.NextReviewDate });
        }
    }
}
