# TriTrackz Carrier Integration Microservice

## Overview

The Carrier Integration Microservice is a comprehensive service within the TriTrackz logistics platform that provides unified carrier API integration, standardized request/response mapping, and advanced carrier management capabilities. This service supports multi-carrier operations, real-time tracking, rate shopping, booking management, and performance monitoring.

## Features

### Core Functionality

- **Multi-Carrier API Integration**: Unified abstraction layer for major carriers (UPS, FedEx, USPS, DHL, XPO, Old Dominion)
- **Carrier Management**: Complete CRUD operations for carriers, services, and accounts
- **Booking Management**: End-to-end booking lifecycle management
- **Rate Shopping**: Real-time rate comparison across multiple carriers
- **Tracking Integration**: Standardized tracking across all supported carriers
- **Label Generation**: Carrier-compliant label generation in multiple formats
- **Performance Monitoring**: Real-time carrier performance metrics and health checks

### Advanced Features

- **Carrier Selection Optimization**: Cost, transit time, and reliability-based selection
- **Failover Support**: Automatic carrier failover for high availability
- **Compliance Management**: Carrier certification and compliance tracking
- **Event-Driven Architecture**: Real-time event publishing for system integration
- **Caching**: Redis-based caching for improved performance
- **Rate Limiting**: Configurable rate limiting per carrier

## Architecture

### Clean Architecture Implementation

```
CarrierIntegration/
├── CarrierIntegration.Domain/          # Domain entities, value objects, events
├── CarrierIntegration.Application/     # CQRS handlers, DTOs, behaviors
├── CarrierIntegration.Infrastructure/  # Data access, external services
├── CarrierIntegration.API/            # REST API controllers
└── CarrierIntegration.Tests/          # Unit and integration tests
```

### Technology Stack

- **.NET 8** with Clean Architecture
- **Entity Framework Core** with PostgreSQL
- **MediatR** for CQRS pattern
- **JWT Authentication** integration with Identity service
- **RabbitMQ** for event-driven messaging
- **Redis** for caching
- **Swagger** for API documentation
- **Application Insights** for monitoring
- **Testcontainers** for integration testing

## Getting Started

### Prerequisites

- .NET 8 SDK
- PostgreSQL (localhost with timescale/timescale credentials)
- RabbitMQ (localhost)
- Redis (localhost:6379)
- Visual Studio 2022 or VS Code

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd TriTrackzMicroservices/CarrierIntegration
   ```

2. **Restore packages**
   ```bash
   dotnet restore
   ```

3. **Update database connection string** in `appsettings.json`
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Host=localhost;Database=TriTrackzCarrierIntegration;Username=timescale;Password=timescale"
     }
   }
   ```

4. **Run database migrations**
   ```bash
   dotnet ef database update --project CarrierIntegration.Infrastructure --startup-project CarrierIntegration.API
   ```

5. **Run the application**
   ```bash
   dotnet run --project CarrierIntegration.API
   ```

The API will be available at:
- HTTP: `http://localhost:5010`
- HTTPS: `https://localhost:7010`
- Swagger UI: `https://localhost:7010/swagger`

## API Documentation

### Core Endpoints

#### Carriers Management
- `GET /api/v1/carriers` - Get paginated carriers with filtering
- `GET /api/v1/carriers/{id}` - Get carrier by ID
- `POST /api/v1/carriers` - Create new carrier
- `PUT /api/v1/carriers/{id}` - Update carrier
- `DELETE /api/v1/carriers/{id}` - Soft delete carrier
- `POST /api/v1/carriers/{id}/activate` - Activate carrier
- `POST /api/v1/carriers/{id}/deactivate` - Deactivate carrier
- `POST /api/v1/carriers/{id}/health-check` - Perform health check

#### Carrier Bookings
- `GET /api/v1/carrierbookings` - Get paginated bookings with filtering
- `GET /api/v1/carrierbookings/{id}` - Get booking by ID
- `POST /api/v1/carrierbookings` - Create new booking
- `PUT /api/v1/carrierbookings/{id}` - Update booking
- `POST /api/v1/carrierbookings/{id}/confirm` - Confirm booking
- `POST /api/v1/carrierbookings/{id}/cancel` - Cancel booking
- `POST /api/v1/carrierbookings/{id}/complete` - Complete booking

#### Carrier Services
- `GET /api/v1/carriers/{carrierId}/services` - Get carrier services
- `POST /api/v1/carriers/{carrierId}/services` - Add service to carrier
- `PUT /api/v1/carriers/{carrierId}/services/{serviceId}` - Update service
- `DELETE /api/v1/carriers/{carrierId}/services/{serviceId}` - Remove service

### Authentication

All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Configuration

### Application Settings

Key configuration sections in `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Host=localhost;Database=TriTrackzCarrierIntegration;Username=timescale;Password=timescale",
    "Redis": "localhost:6379"
  },
  "JwtSettings": {
    "Secret": "your-secret-key",
    "Issuer": "TriTrackzIdentity",
    "Audience": "TriTrackzServices"
  },
  "CarrierApiSettings": {
    "UPS": {
      "BaseUrl": "https://onlinetools.ups.com/api",
      "TestBaseUrl": "https://wwwcie.ups.com/api",
      "TimeoutSeconds": 30,
      "RetryAttempts": 3
    }
  },
  "FeatureFlags": {
    "EnableCaching": true,
    "EnableRealTimeTracking": true,
    "EnableCarrierHealthChecks": true
  }
}
```

### Environment Variables

- `ASPNETCORE_ENVIRONMENT` - Environment (Development, Staging, Production)
- `ConnectionStrings__DefaultConnection` - Database connection string
- `ConnectionStrings__Redis` - Redis connection string
- `JwtSettings__Secret` - JWT signing secret

## Data Seeding

The service includes comprehensive data seeding for development and testing:

### Default Carriers
- UPS (United Parcel Service)
- FedEx (Federal Express Corporation)
- USPS (United States Postal Service)
- DHL Express
- XPO Logistics
- Old Dominion Freight Line

### Carrier Services
Each carrier includes standard service offerings:
- Ground services
- Express services
- Overnight services
- International services (where applicable)

### Demo Accounts
Demo accounts are created for each carrier with sandbox configurations.

## Testing

### Running Tests

```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test category
dotnet test --filter Category=Integration
```

### Test Categories

- **Unit Tests**: Domain logic and business rules
- **Integration Tests**: API endpoints with test database
- **Performance Tests**: Load testing for carrier APIs
- **Contract Tests**: Carrier API contract validation

### Test Database

Integration tests use Testcontainers with PostgreSQL for isolated testing environments.

## Performance Requirements

### Response Time Targets
- Carrier rate requests: < 200ms (95th percentile)
- Booking operations: < 500ms (95th percentile)
- Tracking updates: < 100ms (95th percentile)
- Health checks: < 5 seconds

### Throughput Targets
- 1,000+ requests/second sustained
- 10,000+ concurrent connections
- 99.9% uptime SLA

### Scalability
- Horizontal scaling support
- Database connection pooling
- Redis caching for frequently accessed data
- Async processing for non-critical operations

## Security

### Authentication & Authorization
- JWT token validation
- Role-based access control
- Organization-level data isolation
- API key management for carrier accounts

### Data Protection
- Sensitive data encryption at rest
- TLS 1.3 for data in transit
- Audit logging for all operations
- PII data handling compliance

## Monitoring & Observability

### Application Insights
- Request/response tracking
- Performance metrics
- Error tracking and alerting
- Custom telemetry

### Health Checks
- Database connectivity
- Redis connectivity
- Carrier API availability
- Service dependencies

### Logging
- Structured logging with Serilog
- Request/response correlation
- Performance tracking
- Error details and stack traces

## Integration Points

### Internal Services
- **Identity Service**: Authentication and authorization
- **UserManagement Service**: Organization and user context
- **MasterManagement Service**: Geographic and reference data
- **OrderManagement Service**: Order processing integration
- **ShipmentManagement Service**: Shipment lifecycle management
- **PricingManagement Service**: Rate comparison and optimization

### External Carriers
- UPS API integration
- FedEx API integration
- USPS API integration
- DHL API integration
- Regional and LTL carrier APIs

### Message Broker
- RabbitMQ for event publishing
- Event-driven architecture
- Async processing support

## Deployment

### Docker Support
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["CarrierIntegration.API/CarrierIntegration.API.csproj", "CarrierIntegration.API/"]
RUN dotnet restore "CarrierIntegration.API/CarrierIntegration.API.csproj"
COPY . .
WORKDIR "/src/CarrierIntegration.API"
RUN dotnet build "CarrierIntegration.API.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "CarrierIntegration.API.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CarrierIntegration.API.dll"]
```

### Kubernetes Deployment
- Horizontal Pod Autoscaler (HPA)
- Resource limits and requests
- Health check probes
- ConfigMaps and Secrets

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Internal Wiki]
- Issue Tracker: [GitHub Issues]
