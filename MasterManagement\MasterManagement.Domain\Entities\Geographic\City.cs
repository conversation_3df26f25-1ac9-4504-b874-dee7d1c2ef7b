using System;
using System.Collections.Generic;
using System.Linq;
using MasterManagement.Domain.Common;
using MasterManagement.Domain.Exceptions;
using MasterManagement.Domain.Events.Geographic;
using MasterManagement.Domain.ValueObjects;

namespace MasterManagement.Domain.Entities.Geographic
{
    public class City : AggregateRoot
    {
        public string Name { get; private set; }
        public string Type { get; private set; } // City, Town, Village, etc.
        public Guid StateId { get; private set; }
        public Guid CountryId { get; private set; }
        public bool IsActive { get; private set; }
        public Coordinates? Coordinates { get; private set; }
        public int? Population { get; private set; }
        public string TimeZone { get; private set; }
        
        // Navigation properties
        public State State { get; private set; }
        public Country Country { get; private set; }
        
        private readonly List<Zone> _zones = new();
        public IReadOnlyCollection<Zone> Zones => _zones.AsReadOnly();

        private readonly List<PostalCode> _postalCodes = new();
        public IReadOnlyCollection<PostalCode> PostalCodes => _postalCodes.AsReadOnly();

        private City() { }

        public City(
            string name, 
            string type, 
            Guid stateId, 
            Guid countryId,
            Coordinates? coordinates = null,
            int? population = null,
            string timeZone = "")
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("City name cannot be empty");

            if (stateId == Guid.Empty)
                throw new DomainException("State ID cannot be empty");

            if (countryId == Guid.Empty)
                throw new DomainException("Country ID cannot be empty");

            Name = name;
            Type = type ?? "City";
            StateId = stateId;
            CountryId = countryId;
            IsActive = true;
            Coordinates = coordinates;
            Population = population;
            TimeZone = timeZone ?? string.Empty;

            AddDomainEvent(new CityCreatedEvent(Id, Name, StateId, CountryId));
        }

        public void UpdateDetails(
            string name, 
            string type, 
            Coordinates? coordinates, 
            int? population, 
            string timeZone)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            Type = type ?? Type;
            Coordinates = coordinates ?? Coordinates;
            Population = population ?? Population;
            TimeZone = timeZone ?? TimeZone;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new CityUpdatedEvent(Id, Name));
        }

        public void AddZone(Zone zone)
        {
            if (zone == null)
                throw new DomainException("Zone cannot be null");

            if (_zones.Any(z => z.Name.Equals(zone.Name, StringComparison.OrdinalIgnoreCase)))
                throw new DomainException($"Zone with name {zone.Name} already exists");

            _zones.Add(zone);
        }

        public void AddPostalCode(PostalCode postalCode)
        {
            if (postalCode == null)
                throw new DomainException("Postal code cannot be null");

            if (_postalCodes.Any(p => p.Code == postalCode.Code))
                throw new DomainException($"Postal code {postalCode.Code} already exists");

            _postalCodes.Add(postalCode);
        }

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
