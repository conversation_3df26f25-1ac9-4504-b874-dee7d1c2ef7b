using AutoMapper;
using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Domain.ValueObjects;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Features.CarrierBookings.Commands
{
    public class UpdateCarrierBookingCommand : IRequest<ApiResponseDto<CarrierBookingDto>>
    {
        public Guid Id { get; set; }
        public Guid? CarrierAccountId { get; set; }
        public Guid? CarrierServiceId { get; set; }
        public DateTime RequestedPickupDate { get; set; }
        public DateTime? RequestedDeliveryDate { get; set; }
        public TimeSpan? PickupTimeWindow { get; set; }
        public TimeSpan? DeliveryTimeWindow { get; set; }
        public AddressDto PickupAddress { get; set; } = null!;
        public AddressDto DeliveryAddress { get; set; } = null!;
        public ContactDto PickupContact { get; set; } = null!;
        public ContactDto DeliveryContact { get; set; } = null!;
        public string? PickupInstructions { get; set; }
        public string? DeliveryInstructions { get; set; }
        public WeightDto TotalWeight { get; set; } = null!;
        public DimensionsDto? TotalDimensions { get; set; }
        public int PackageCount { get; set; }
        public string? PackageDescription { get; set; }
        public MoneyDto? DeclaredValue { get; set; }
        public string? CommodityCode { get; set; }
        public string? HazmatClass { get; set; }
        public bool RequiresSignature { get; set; }
        public bool RequiresInsurance { get; set; }
        public bool IsCOD { get; set; }
        public MoneyDto? CODAmount { get; set; }
        public string? CODPaymentMethod { get; set; }
        public bool IsResidentialPickup { get; set; }
        public bool IsResidentialDelivery { get; set; }
        public bool RequiresAppointment { get; set; }
        public bool RequiresLiftgate { get; set; }
        public bool RequiresInsideDelivery { get; set; }
        public bool RequiresWhiteGlove { get; set; }
        public string? SpecialServices { get; set; }
        public string? AccessorialServices { get; set; }
        public string? CustomerReference { get; set; }
        public string? ShipperReference { get; set; }
        public string? PoNumber { get; set; }
        public string? InternalNotes { get; set; }
        public string UpdatedBy { get; set; } = "System";
    }

    public class UpdateCarrierBookingCommandValidator : AbstractValidator<UpdateCarrierBookingCommand>
    {
        private readonly IUnitOfWork _unitOfWork;

        public UpdateCarrierBookingCommandValidator(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

            RuleFor(x => x.Id)
                .NotEmpty()
                .WithMessage("Booking ID is required")
                .MustAsync(BeValidBooking)
                .WithMessage("Booking does not exist or cannot be modified");

            RuleFor(x => x.RequestedPickupDate)
                .GreaterThanOrEqualTo(DateTime.Today)
                .WithMessage("Requested pickup date cannot be in the past");

            RuleFor(x => x.RequestedDeliveryDate)
                .GreaterThan(x => x.RequestedPickupDate)
                .When(x => x.RequestedDeliveryDate.HasValue)
                .WithMessage("Requested delivery date must be after pickup date");

            RuleFor(x => x.PickupAddress)
                .NotNull()
                .WithMessage("Pickup address is required");

            RuleFor(x => x.DeliveryAddress)
                .NotNull()
                .WithMessage("Delivery address is required");

            RuleFor(x => x.PickupContact)
                .NotNull()
                .WithMessage("Pickup contact is required");

            RuleFor(x => x.DeliveryContact)
                .NotNull()
                .WithMessage("Delivery contact is required");

            RuleFor(x => x.TotalWeight)
                .NotNull()
                .WithMessage("Total weight is required");

            RuleFor(x => x.TotalWeight.Value)
                .GreaterThan(0)
                .When(x => x.TotalWeight != null)
                .WithMessage("Total weight must be greater than zero");

            RuleFor(x => x.PackageCount)
                .GreaterThan(0)
                .WithMessage("Package count must be greater than zero");

            RuleFor(x => x.CODAmount)
                .NotNull()
                .When(x => x.IsCOD)
                .WithMessage("COD amount is required when COD is enabled");

            RuleFor(x => x.CODAmount!.Amount)
                .GreaterThan(0)
                .When(x => x.IsCOD && x.CODAmount != null)
                .WithMessage("COD amount must be greater than zero");
        }

        private async Task<bool> BeValidBooking(Guid bookingId, CancellationToken cancellationToken)
        {
            var booking = await _unitOfWork.CarrierBookings.GetByIdAsync(bookingId);
            return booking != null && booking.CanBeModified();
        }
    }

    public class UpdateCarrierBookingCommandHandler : IRequestHandler<UpdateCarrierBookingCommand, ApiResponseDto<CarrierBookingDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<UpdateCarrierBookingCommandHandler> _logger;

        public UpdateCarrierBookingCommandHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<UpdateCarrierBookingCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ApiResponseDto<CarrierBookingDto>> Handle(UpdateCarrierBookingCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Updating carrier booking {BookingId}", request.Id);

                var booking = await _unitOfWork.CarrierBookings.GetByIdAsync(request.Id);
                if (booking == null)
                {
                    return ApiResponseDto<CarrierBookingDto>.ErrorResult("Booking not found");
                }

                if (!booking.CanBeModified())
                {
                    return ApiResponseDto<CarrierBookingDto>.ErrorResult("Booking cannot be modified in its current status");
                }

                // Create value objects
                var pickupAddress = _mapper.Map<Address>(request.PickupAddress);
                var deliveryAddress = _mapper.Map<Address>(request.DeliveryAddress);
                var pickupContact = _mapper.Map<ContactInfo>(request.PickupContact);
                var deliveryContact = _mapper.Map<ContactInfo>(request.DeliveryContact);
                var totalWeight = _mapper.Map<Weight>(request.TotalWeight);

                // Update booking properties
                booking.UpdateCarrierInfo(
                    request.CarrierAccountId,
                    request.CarrierServiceId,
                    null, // CarrierServiceCode will be set later
                    null, // CarrierServiceName will be set later
                    request.UpdatedBy);

                booking.UpdateSchedule(
                    request.RequestedPickupDate,
                    request.RequestedDeliveryDate,
                    request.PickupTimeWindow,
                    request.DeliveryTimeWindow,
                    request.UpdatedBy);

                booking.UpdateAddresses(
                    pickupAddress,
                    deliveryAddress,
                    pickupContact,
                    deliveryContact,
                    request.PickupInstructions,
                    request.DeliveryInstructions,
                    request.UpdatedBy);

                var totalDimensions = request.TotalDimensions != null ? _mapper.Map<Dimensions>(request.TotalDimensions) : null;
                var declaredValue = request.DeclaredValue != null ? _mapper.Map<Money>(request.DeclaredValue) : null;

                booking.UpdatePackageInfo(
                    totalWeight,
                    totalDimensions,
                    request.PackageCount,
                    request.PackageDescription,
                    declaredValue,
                    request.CommodityCode,
                    request.HazmatClass,
                    request.UpdatedBy);

                var codAmount = request.CODAmount != null ? _mapper.Map<Money>(request.CODAmount) : null;

                booking.UpdateServiceRequirements(
                    request.RequiresSignature,
                    request.RequiresInsurance,
                    request.IsCOD,
                    codAmount,
                    request.CODPaymentMethod,
                    request.IsResidentialPickup,
                    request.IsResidentialDelivery,
                    request.RequiresAppointment,
                    request.RequiresLiftgate,
                    request.RequiresInsideDelivery,
                    request.RequiresWhiteGlove,
                    request.SpecialServices,
                    request.AccessorialServices,
                    request.UpdatedBy);

                booking.UpdateOrderInfo(
                    booking.OrderId, // Keep existing OrderId
                    booking.ShipmentId, // Keep existing ShipmentId
                    request.CustomerReference,
                    request.ShipperReference,
                    request.PoNumber,
                    request.UpdatedBy);

                booking.UpdateNotes(
                    booking.CarrierNotes, // Keep existing carrier notes
                    request.InternalNotes,
                    request.UpdatedBy);

                // Mark as modified
                booking.Modify("Booking updated via API", request.UpdatedBy);

                // Save to database
                await _unitOfWork.CarrierBookings.UpdateAsync(booking);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully updated carrier booking {BookingId}", booking.Id);

                // Map to DTO and return
                var bookingDto = _mapper.Map<CarrierBookingDto>(booking);
                return ApiResponseDto<CarrierBookingDto>.SuccessResult(bookingDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating carrier booking {BookingId}", request.Id);
                return ApiResponseDto<CarrierBookingDto>.ErrorResult($"Failed to update carrier booking: {ex.Message}");
            }
        }
    }
}
