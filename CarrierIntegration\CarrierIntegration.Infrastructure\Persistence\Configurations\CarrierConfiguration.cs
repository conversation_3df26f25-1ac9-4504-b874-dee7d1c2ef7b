using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.Enums;
using CarrierIntegration.Domain.ValueObjects;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CarrierIntegration.Infrastructure.Persistence.Configurations
{
    public class CarrierConfiguration : IEntityTypeConfiguration<Carrier>
    {
        public void Configure(EntityTypeBuilder<Carrier> builder)
        {
            builder.ToTable("Carriers");

            builder.HasKey(c => c.Id);

            builder.Property(c => c.Id)
                .ValueGeneratedNever();

            builder.Property(c => c.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(c => c.Code)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(c => c.Type)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(c => c.Status)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(c => c.Description)
                .HasMaxLength(1000);

            builder.Property(c => c.Website)
                .HasMaxLength(500);

            builder.Property(c => c.ApiEndpoint)
                .HasMaxLength(500);

            builder.Property(c => c.ApiVersion)
                .HasMaxLength(50);

            builder.Property(c => c.TimeZone)
                .HasMaxLength(100);

            builder.Property(c => c.OperatingHours)
                .HasMaxLength(500);

            builder.Property(c => c.MaxWeightUnit)
                .HasMaxLength(10);

            builder.Property(c => c.MaxDimensionsUnit)
                .HasMaxLength(10);

            builder.Property(c => c.SupportContact)
                .HasMaxLength(500);

            builder.Property(c => c.BillingContact)
                .HasMaxLength(500);

            builder.Property(c => c.ContractTerms)
                .HasMaxLength(2000);

            builder.Property(c => c.BaseCurrency)
                .HasConversion<int?>();

            builder.Property(c => c.InsuranceProvider)
                .HasMaxLength(200);

            builder.Property(c => c.InsurancePolicyNumber)
                .HasMaxLength(100);

            builder.Property(c => c.LicenseNumber)
                .HasMaxLength(100);

            builder.Property(c => c.DotNumber)
                .HasMaxLength(50);

            builder.Property(c => c.McNumber)
                .HasMaxLength(50);

            builder.Property(c => c.ScacCode)
                .HasMaxLength(10);

            builder.Property(c => c.IataCode)
                .HasMaxLength(10);

            builder.Property(c => c.CustomsCode)
                .HasMaxLength(50);

            builder.Property(c => c.HealthCheckMessage)
                .HasMaxLength(1000);

            builder.Property(c => c.OrganizationId)
                .IsRequired();

            builder.Property(c => c.CreatedAt)
                .IsRequired();

            builder.Property(c => c.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(c => c.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(c => c.DeletedBy)
                .HasMaxLength(100);

            // Configure value objects
            builder.OwnsOne(c => c.PrimaryContact, contact =>
            {
                contact.Property(x => x.Name)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("PrimaryContactName");

                contact.Property(x => x.Email)
                    .HasMaxLength(200)
                    .HasColumnName("PrimaryContactEmail");

                contact.Property(x => x.Phone)
                    .HasMaxLength(50)
                    .HasColumnName("PrimaryContactPhone");

                contact.Property(x => x.Title)
                    .HasMaxLength(100)
                    .HasColumnName("PrimaryContactTitle");

                contact.Property(x => x.Department)
                    .HasMaxLength(100)
                    .HasColumnName("PrimaryContactDepartment");
            });

            builder.OwnsOne(c => c.HeadquartersAddress, address =>
            {
                address.Property(x => x.Street1)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("HeadquartersStreet1");

                address.Property(x => x.Street2)
                    .HasMaxLength(200)
                    .HasColumnName("HeadquartersStreet2");

                address.Property(x => x.City)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("HeadquartersCity");

                address.Property(x => x.State)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("HeadquartersState");

                address.Property(x => x.PostalCode)
                    .IsRequired()
                    .HasMaxLength(20)
                    .HasColumnName("HeadquartersPostalCode");

                address.Property(x => x.Country)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("HeadquartersCountry");

                address.Property(x => x.Latitude)
                    .HasPrecision(10, 7)
                    .HasColumnName("HeadquartersLatitude");

                address.Property(x => x.Longitude)
                    .HasPrecision(10, 7)
                    .HasColumnName("HeadquartersLongitude");
            });

            // Configure relationships
            builder.HasMany(c => c.Services)
                .WithOne()
                .HasForeignKey(s => s.CarrierId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(c => c.Accounts)
                .WithOne()
                .HasForeignKey(a => a.CarrierId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure indexes
            builder.HasIndex(c => new { c.OrganizationId, c.Code })
                .IsUnique()
                .HasDatabaseName("IX_Carriers_OrganizationId_Code");

            builder.HasIndex(c => c.OrganizationId)
                .HasDatabaseName("IX_Carriers_OrganizationId");

            builder.HasIndex(c => c.Type)
                .HasDatabaseName("IX_Carriers_Type");

            builder.HasIndex(c => c.Status)
                .HasDatabaseName("IX_Carriers_Status");

            builder.HasIndex(c => c.IsActive)
                .HasDatabaseName("IX_Carriers_IsActive");

            builder.HasIndex(c => c.CreatedAt)
                .HasDatabaseName("IX_Carriers_CreatedAt");

            // Ignore domain events for EF Core
            builder.Ignore(c => c.DomainEvents);
        }
    }
}
