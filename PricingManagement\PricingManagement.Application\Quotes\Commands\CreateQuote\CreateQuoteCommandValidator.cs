using FluentValidation;

namespace PricingManagement.Application.Quotes.Commands.CreateQuote
{
    public class CreateQuoteCommandValidator : AbstractValidator<CreateQuoteCommand>
    {
        public CreateQuoteCommandValidator()
        {
            RuleFor(x => x.OriginAddress)
                .NotEmpty().WithMessage("Origin address is required")
                .MaximumLength(500).WithMessage("Origin address must not exceed 500 characters");

            RuleFor(x => x.DestinationAddress)
                .NotEmpty().WithMessage("Destination address is required")
                .MaximumLength(500).WithMessage("Destination address must not exceed 500 characters");

            RuleFor(x => x.ServiceType)
                .NotEmpty().WithMessage("Service type is required")
                .MaximumLength(100).WithMessage("Service type must not exceed 100 characters");

            RuleFor(x => x.ExpirationDate)
                .GreaterThan(DateTime.UtcNow)
                .WithMessage("Expiration date must be in the future");

            RuleFor(x => x.PackageWeight)
                .NotNull().WithMessage("Package weight is required");

            RuleFor(x => x.PackageWeight.Value)
                .GreaterThan(0).WithMessage("Package weight must be greater than 0")
                .When(x => x.PackageWeight != null);

            RuleFor(x => x.DeclaredValue)
                .GreaterThanOrEqualTo(0)
                .When(x => x.DeclaredValue.HasValue)
                .WithMessage("Declared value cannot be negative");

            RuleFor(x => x.Distance)
                .GreaterThanOrEqualTo(0)
                .When(x => x.Distance.HasValue)
                .WithMessage("Distance cannot be negative");

            RuleFor(x => x.CustomerName)
                .NotEmpty()
                .When(x => x.CustomerId.HasValue)
                .WithMessage("Customer name is required when customer ID is provided");

            RuleFor(x => x.ShipperName)
                .NotEmpty()
                .When(x => x.ShipperId.HasValue)
                .WithMessage("Shipper name is required when shipper ID is provided");

            RuleFor(x => x.Notes)
                .MaximumLength(2000)
                .When(x => !string.IsNullOrEmpty(x.Notes))
                .WithMessage("Notes must not exceed 2000 characters");

            RuleFor(x => x.Terms)
                .MaximumLength(2000)
                .When(x => !string.IsNullOrEmpty(x.Terms))
                .WithMessage("Terms must not exceed 2000 characters");

            RuleForEach(x => x.LineItems)
                .SetValidator(new CreateQuoteLineItemValidator());

            When(x => x.PackageDimensions != null, () =>
            {
                RuleFor(x => x.PackageDimensions!.Length)
                    .GreaterThan(0).WithMessage("Length must be greater than 0");

                RuleFor(x => x.PackageDimensions!.Width)
                    .GreaterThan(0).WithMessage("Width must be greater than 0");

                RuleFor(x => x.PackageDimensions!.Height)
                    .GreaterThan(0).WithMessage("Height must be greater than 0");
            });
        }
    }

    public class CreateQuoteLineItemValidator : AbstractValidator<CreateQuoteLineItemDto>
    {
        public CreateQuoteLineItemValidator()
        {
            RuleFor(x => x.Description)
                .NotEmpty().WithMessage("Line item description is required")
                .MaximumLength(500).WithMessage("Description must not exceed 500 characters");

            RuleFor(x => x.Quantity)
                .GreaterThan(0).WithMessage("Quantity must be greater than 0");

            RuleFor(x => x.UnitPrice)
                .NotNull().WithMessage("Unit price is required");

            RuleFor(x => x.UnitPrice.Amount)
                .GreaterThanOrEqualTo(0).WithMessage("Unit price cannot be negative")
                .When(x => x.UnitPrice != null);
        }
    }
}
