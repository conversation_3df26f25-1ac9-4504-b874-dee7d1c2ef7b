using MediatR;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Enums;
using System;
using System.Collections.Generic;

namespace PricingManagement.Application.Contracts.Commands.CreateContract
{
    public class CreateContractCommand : IRequest<OperationResultDto<ContractDto>>
    {
        public string ContractNumber { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Guid? CustomerId { get; set; }
        public Guid? ShipperId { get; set; }
        public string? CustomerName { get; set; }
        public string? ShipperName { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime ExpirationDate { get; set; }
        public CurrencyCode Currency { get; set; } = CurrencyCode.USD;
        public string? Terms { get; set; }
        public string? PaymentTerms { get; set; }
        public decimal? MinimumCommitment { get; set; }
        public decimal? MaximumCommitment { get; set; }
        public string? CommitmentType { get; set; }
        public decimal? CommitmentPeriod { get; set; }
        public bool AutoRenewal { get; set; }
        public int? AutoRenewalPeriod { get; set; }
        public string? NotificationPeriod { get; set; }
        public List<string> ServiceTypes { get; set; } = new();
        public Dictionary<string, object> GeographicScope { get; set; } = new();
        public string? SpecialProvisions { get; set; }
        public List<ContractRateDto> Rates { get; set; } = new();
        public List<ContractDiscountDto> Discounts { get; set; } = new();
        public List<ContractSurchargeDto> Surcharges { get; set; } = new();
        public List<ContractCommitmentDto> Commitments { get; set; } = new();
    }
}
