using PricingManagement.Domain.Common;
using PricingManagement.Domain.ValueObjects;
using System;

namespace PricingManagement.Domain.Events
{
    public class QuoteCreatedEvent : DomainEvent
    {
        public Guid QuoteId { get; }
        public string QuoteNumber { get; }

        public QuoteCreatedEvent(Guid quoteId, string quoteNumber, Guid? organizationId)
            : base(organizationId)
        {
            QuoteId = quoteId;
            QuoteNumber = quoteNumber;
        }
    }

    public class QuoteGeneratedEvent : DomainEvent
    {
        public Guid QuoteId { get; }
        public string QuoteNumber { get; }
        public Money TotalAmount { get; }

        public QuoteGeneratedEvent(Guid quoteId, string quoteNumber, Money totalAmount, Guid? organizationId)
            : base(organizationId)
        {
            QuoteId = quoteId;
            QuoteNumber = quoteNumber;
            TotalAmount = totalAmount;
        }
    }

    public class QuoteSentEvent : DomainEvent
    {
        public Guid QuoteId { get; }
        public string QuoteNumber { get; }

        public QuoteSentEvent(Guid quoteId, string quoteNumber, Guid? organizationId)
            : base(organizationId)
        {
            QuoteId = quoteId;
            QuoteNumber = quoteNumber;
        }
    }

    public class QuoteAcceptedEvent : DomainEvent
    {
        public Guid QuoteId { get; }
        public string QuoteNumber { get; }
        public Money TotalAmount { get; }

        public QuoteAcceptedEvent(Guid quoteId, string quoteNumber, Money totalAmount, Guid? organizationId)
            : base(organizationId)
        {
            QuoteId = quoteId;
            QuoteNumber = quoteNumber;
            TotalAmount = totalAmount;
        }
    }

    public class QuoteRejectedEvent : DomainEvent
    {
        public Guid QuoteId { get; }
        public string QuoteNumber { get; }
        public string Reason { get; }

        public QuoteRejectedEvent(Guid quoteId, string quoteNumber, string reason, Guid? organizationId)
            : base(organizationId)
        {
            QuoteId = quoteId;
            QuoteNumber = quoteNumber;
            Reason = reason;
        }
    }

    public class QuoteExpiredEvent : DomainEvent
    {
        public Guid QuoteId { get; }
        public string QuoteNumber { get; }

        public QuoteExpiredEvent(Guid quoteId, string quoteNumber, Guid? organizationId)
            : base(organizationId)
        {
            QuoteId = quoteId;
            QuoteNumber = quoteNumber;
        }
    }

    public class QuoteConvertedEvent : DomainEvent
    {
        public Guid QuoteId { get; }
        public string QuoteNumber { get; }
        public Guid OrderId { get; }

        public QuoteConvertedEvent(Guid quoteId, string quoteNumber, Guid orderId, Guid? organizationId)
            : base(organizationId)
        {
            QuoteId = quoteId;
            QuoteNumber = quoteNumber;
            OrderId = orderId;
        }
    }
}
