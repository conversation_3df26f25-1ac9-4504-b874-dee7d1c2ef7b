using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Persistence.Repositories
{
    public class CarrierComplianceRepository : BaseRepository<CarrierCompliance>, ICarrierComplianceRepository
    {
        public CarrierComplianceRepository(CarrierIntegrationDbContext context) : base(context)
        {
        }

        public async Task<List<CarrierCompliance>> GetByCarrierIdAsync(Guid carrierId)
        {
            return await _context.CarrierCompliances
                .Where(cc => cc.CarrierId == carrierId)
                .OrderByDescending(cc => cc.ExpirationDate)
                .ToListAsync();
        }

        public async Task<List<CarrierCompliance>> GetByOrganizationIdAsync(Guid organizationId)
        {
            return await _context.CarrierCompliances
                .Include(cc => cc.Carrier)
                .Where(cc => cc.OrganizationId == organizationId)
                .OrderByDescending(cc => cc.ExpirationDate)
                .ToListAsync();
        }

        public async Task<List<CarrierCompliance>> GetByComplianceTypeAsync(Guid organizationId, string complianceType)
        {
            return await _context.CarrierCompliances
                .Include(cc => cc.Carrier)
                .Where(cc => cc.OrganizationId == organizationId && cc.ComplianceType == complianceType)
                .OrderByDescending(cc => cc.ExpirationDate)
                .ToListAsync();
        }

        public async Task<List<CarrierCompliance>> GetExpiringComplianceAsync(Guid organizationId, int daysAhead)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(daysAhead);
            
            return await _context.CarrierCompliances
                .Include(cc => cc.Carrier)
                .Where(cc => cc.OrganizationId == organizationId && 
                            cc.ExpirationDate.HasValue && 
                            cc.ExpirationDate.Value <= cutoffDate &&
                            cc.ExpirationDate.Value >= DateTime.UtcNow)
                .OrderBy(cc => cc.ExpirationDate)
                .ToListAsync();
        }

        public async Task<List<CarrierCompliance>> GetByStatusAsync(Guid organizationId, string status)
        {
            return await _context.CarrierCompliances
                .Include(cc => cc.Carrier)
                .Where(cc => cc.OrganizationId == organizationId && cc.Status == status)
                .OrderByDescending(cc => cc.ExpirationDate)
                .ToListAsync();
        }

        public async Task<bool> ComplianceExistsAsync(Guid carrierId, string complianceType, string complianceName, Guid? excludeId = null)
        {
            var query = _context.CarrierCompliances
                .Where(cc => cc.CarrierId == carrierId && 
                            cc.ComplianceType == complianceType && 
                            cc.ComplianceName == complianceName);

            if (excludeId.HasValue)
                query = query.Where(cc => cc.Id != excludeId.Value);

            return await query.AnyAsync();
        }
    }
}
