namespace PricingManagement.Domain.Enums
{
    public enum PricingRuleType
    {
        BaseRate = 1,
        WeightBased = 2,
        DistanceBased = 3,
        ZoneBased = 4,
        DimensionalWeight = 5,
        TimeOfDay = 6,
        Seasonal = 7,
        DemandBased = 8,
        CompetitorBased = 9,
        ServiceLevel = 10,
        VolumeDiscount = 11,
        LaneSpecific = 12
    }

    public enum CalculationMethod
    {
        Fixed = 1,
        Percentage = 2,
        PerUnit = 3,
        Tiered = 4,
        Progressive = 5,
        Matrix = 6
    }

    public enum PricingStatus
    {
        Draft = 1,
        Active = 2,
        Inactive = 3,
        Expired = 4,
        Suspended = 5
    }

    public enum QuoteStatus
    {
        Draft = 1,
        Generated = 2,
        Sent = 3,
        Accepted = 4,
        Rejected = 5,
        Expired = 6,
        Converted = 7
    }

    public enum ContractStatus
    {
        Draft = 1,
        Active = 2,
        Expired = 3,
        Terminated = 4,
        Suspended = 5,
        UnderReview = 6
    }

    public enum DiscountType
    {
        Percentage = 1,
        FixedAmount = 2,
        VolumeDiscount = 3,
        EarlyPayment = 4,
        Loyalty = 5,
        Promotional = 6,
        Seasonal = 7,
        NewCustomer = 8
    }

    public enum SurchargeType
    {
        Fuel = 1,
        RemoteArea = 2,
        Residential = 3,
        Weekend = 4,
        Holiday = 5,
        PeakSeason = 6,
        AddressCorrection = 7,
        Oversize = 8,
        Overweight = 9,
        HazardousMaterials = 10,
        SignatureRequired = 11,
        Insurance = 12
    }

    public enum TaxType
    {
        SalesTax = 1,
        VAT = 2,
        GST = 3,
        HST = 4,
        ServiceTax = 5,
        CustomsDuty = 6
    }

    public enum CurrencyCode
    {
        USD = 1,
        EUR = 2,
        GBP = 3,
        CAD = 4,
        AUD = 5,
        JPY = 6,
        CHF = 7,
        CNY = 8,
        INR = 9,
        MXN = 10
    }

    public enum ApprovalStatus
    {
        Pending = 1,
        Approved = 2,
        Rejected = 3,
        Expired = 4,
        Cancelled = 5
    }

    public enum PricingEventType
    {
        RateCalculated = 1,
        QuoteGenerated = 2,
        ContractCreated = 3,
        DiscountApplied = 4,
        SurchargeApplied = 5,
        TaxCalculated = 6,
        CurrencyConverted = 7,
        ApprovalRequested = 8,
        ApprovalGranted = 9,
        ApprovalDenied = 10
    }
}
