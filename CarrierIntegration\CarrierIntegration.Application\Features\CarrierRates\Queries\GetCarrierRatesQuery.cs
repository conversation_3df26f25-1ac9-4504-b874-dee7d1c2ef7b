using AutoMapper;
using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Features.CarrierRates.Queries
{
    public class GetCarrierRatesQuery : IRequest<ApiResponseDto<List<CarrierRateDto>>>
    {
        public Guid OrganizationId { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public List<PackageDto> Packages { get; set; } = new();
        public DateTime ShipDate { get; set; }
        public List<Guid>? PreferredCarrierIds { get; set; }
        public List<string>? ServiceCodes { get; set; }
        public bool IncludeAccessorials { get; set; } = true;
        public decimal? DeclaredValue { get; set; }
        public string Currency { get; set; } = "USD";
        public CarrierSelectionCriteriaDto? SelectionCriteria { get; set; }
    }

    public class GetCarrierRatesQueryValidator : AbstractValidator<GetCarrierRatesQuery>
    {
        public GetCarrierRatesQueryValidator()
        {
            RuleFor(x => x.OrganizationId)
                .NotEmpty()
                .WithMessage("Organization ID is required");

            RuleFor(x => x.OriginAddress)
                .NotNull()
                .WithMessage("Origin address is required");

            RuleFor(x => x.DestinationAddress)
                .NotNull()
                .WithMessage("Destination address is required");

            RuleFor(x => x.Packages)
                .NotEmpty()
                .WithMessage("At least one package is required");

            RuleFor(x => x.ShipDate)
                .GreaterThanOrEqualTo(DateTime.Today)
                .WithMessage("Ship date cannot be in the past");

            RuleFor(x => x.DeclaredValue)
                .GreaterThan(0)
                .When(x => x.DeclaredValue.HasValue)
                .WithMessage("Declared value must be greater than zero");

            RuleFor(x => x.Currency)
                .NotEmpty()
                .WithMessage("Currency is required")
                .Length(3)
                .WithMessage("Currency must be a 3-letter code");

            RuleForEach(x => x.Packages)
                .SetValidator(new PackageDtoValidator());
        }
    }

    public class PackageDtoValidator : AbstractValidator<PackageDto>
    {
        public PackageDtoValidator()
        {
            RuleFor(x => x.Weight)
                .NotNull()
                .WithMessage("Package weight is required");

            RuleFor(x => x.Weight.Value)
                .GreaterThan(0)
                .When(x => x.Weight != null)
                .WithMessage("Package weight must be greater than zero");

            RuleFor(x => x.Dimensions)
                .NotNull()
                .WithMessage("Package dimensions are required");

            When(x => x.Dimensions != null, () =>
            {
                RuleFor(x => x.Dimensions!.Length)
                    .GreaterThan(0)
                    .WithMessage("Package length must be greater than zero");

                RuleFor(x => x.Dimensions!.Width)
                    .GreaterThan(0)
                    .WithMessage("Package width must be greater than zero");

                RuleFor(x => x.Dimensions!.Height)
                    .GreaterThan(0)
                    .WithMessage("Package height must be greater than zero");
            });
        }
    }

    public class GetCarrierRatesQueryHandler : IRequestHandler<GetCarrierRatesQuery, ApiResponseDto<List<CarrierRateDto>>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICarrierApiService _carrierApiService;
        private readonly IMapper _mapper;
        private readonly ILogger<GetCarrierRatesQueryHandler> _logger;

        public GetCarrierRatesQueryHandler(
            IUnitOfWork unitOfWork,
            ICarrierApiService carrierApiService,
            IMapper mapper,
            ILogger<GetCarrierRatesQueryHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _carrierApiService = carrierApiService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ApiResponseDto<List<CarrierRateDto>>> Handle(GetCarrierRatesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Getting carrier rates for organization {OrganizationId} from {Origin} to {Destination}",
                    request.OrganizationId, request.OriginAddress.City, request.DestinationAddress.City);

                // Get eligible carriers
                var carriers = await GetEligibleCarriers(request);

                if (!carriers.Any())
                {
                    _logger.LogWarning("No eligible carriers found for rate request");
                    return ApiResponseDto<List<CarrierRateDto>>.ErrorResult("No eligible carriers found for this route");
                }

                var allRates = new List<CarrierRateDto>();

                // Get rates from each carrier
                foreach (var carrier in carriers)
                {
                    try
                    {
                        var carrierRates = await GetRatesFromCarrier(carrier, request);
                        allRates.AddRange(carrierRates);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to get rates from carrier {CarrierName}", carrier.Name);
                        // Continue with other carriers
                    }
                }

                // Apply selection criteria and sorting
                var filteredRates = ApplySelectionCriteria(allRates, request.SelectionCriteria);
                var sortedRates = SortRatesByPreference(filteredRates, request.SelectionCriteria);

                _logger.LogInformation("Successfully retrieved {RateCount} rates from {CarrierCount} carriers",
                    sortedRates.Count, carriers.Count());

                return ApiResponseDto<List<CarrierRateDto>>.SuccessResult(sortedRates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting carrier rates for organization {OrganizationId}", request.OrganizationId);
                return ApiResponseDto<List<CarrierRateDto>>.ErrorResult($"Failed to get carrier rates: {ex.Message}");
            }
        }

        private async Task<IEnumerable<Domain.Entities.Carrier>> GetEligibleCarriers(GetCarrierRatesQuery request)
        {
            var carriers = await _unitOfWork.Carriers.GetActiveCarriersAsync(request.OrganizationId);

            // Filter by preferred carriers if specified
            if (request.PreferredCarrierIds?.Any() == true)
            {
                carriers = carriers.Where(c => request.PreferredCarrierIds.Contains(c.Id)).ToList();
            }

            // Filter by capabilities
            carriers = carriers.Where(c => 
                c.SupportsRating && 
                c.IsOperational() && 
                c.IsContractValid() && 
                c.IsComplianceValid()).ToList();

            // Filter by weight and dimension limitations
            var totalWeight = request.Packages.Sum(p => p.Weight.Value);
            carriers = carriers.Where(c => c.CanHandleWeight(new Domain.ValueObjects.Weight(totalWeight, request.Packages.First().Weight.Unit))).ToList();

            return carriers;
        }

        private async Task<List<CarrierRateDto>> GetRatesFromCarrier(Domain.Entities.Carrier carrier, GetCarrierRatesQuery request)
        {
            var rateRequest = new CarrierRateRequest
            {
                CarrierId = carrier.Id,
                OriginAddress = request.OriginAddress,
                DestinationAddress = request.DestinationAddress,
                Packages = request.Packages,
                ShipDate = request.ShipDate,
                ServiceCodes = request.ServiceCodes,
                IncludeAccessorials = request.IncludeAccessorials,
                DeclaredValue = request.DeclaredValue,
                Currency = request.Currency
            };

            var response = await _carrierApiService.GetRatesAsync(rateRequest);

            if (!response.Success)
            {
                _logger.LogWarning("Failed to get rates from carrier {CarrierName}: {Error}", 
                    carrier.Name, response.ErrorMessage);
                return new List<CarrierRateDto>();
            }

            return response.Rates;
        }

        private static List<CarrierRateDto> ApplySelectionCriteria(List<CarrierRateDto> rates, CarrierSelectionCriteriaDto? criteria)
        {
            if (criteria == null)
                return rates;

            var filteredRates = rates.AsEnumerable();

            // Filter by max cost
            if (criteria.MaxCost != null)
            {
                filteredRates = filteredRates.Where(r => r.TotalCost.Amount <= criteria.MaxCost.Amount);
            }

            // Filter by max transit days
            if (criteria.MaxTransitDays.HasValue)
            {
                filteredRates = filteredRates.Where(r => r.EstimatedTransitDays <= criteria.MaxTransitDays.Value);
            }

            // Filter by service level
            if (!string.IsNullOrWhiteSpace(criteria.ServiceLevel))
            {
                filteredRates = filteredRates.Where(r => r.ServiceLevel.ToString().Contains(criteria.ServiceLevel, StringComparison.OrdinalIgnoreCase));
            }

            return filteredRates.ToList();
        }

        private static List<CarrierRateDto> SortRatesByPreference(List<CarrierRateDto> rates, CarrierSelectionCriteriaDto? criteria)
        {
            if (criteria == null)
                return rates.OrderBy(r => r.TotalCost.Amount).ToList();

            // Default sorting by cost, then by transit time
            return rates
                .OrderBy(r => r.TotalCost.Amount)
                .ThenBy(r => r.EstimatedTransitDays)
                .ThenByDescending(r => r.IsGuaranteed)
                .ToList();
        }
    }
}
