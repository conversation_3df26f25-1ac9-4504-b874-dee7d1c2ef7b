using FluentValidation;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using PricingManagement.Application.Behaviors;
using PricingManagement.Application.Services;
using System.Reflection;

namespace PricingManagement.Application
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddApplication(this IServiceCollection services)
        {
            // MediatR
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

            // Behaviors
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(LoggingBehavior<,>));
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(PerformanceBehavior<,>));

            // Validators
            services.AddValidatorsFromAssembly(Assembly.GetExecutingAssembly());

            // AutoMapper
            services.AddAutoMapper(Assembly.GetExecutingAssembly());

            // Application Services
            services.AddScoped<IPricingEngine, PricingEngine>();
            services.AddScoped<IRateCalculationService, RateCalculationService>();
            services.AddScoped<IQuoteService, QuoteService>();
            services.AddScoped<IContractService, ContractService>();
            services.AddScoped<IDiscountService, DiscountService>();
            services.AddScoped<ISurchargeService, SurchargeService>();
            services.AddScoped<ITaxCalculationService, TaxCalculationService>();
            services.AddScoped<ICurrencyConversionService, CurrencyConversionService>();
            services.AddScoped<IFuelSurchargeService, FuelSurchargeService>();
            services.AddScoped<ICarrierRateService, CarrierRateService>();
            services.AddScoped<IPricingApprovalService, PricingApprovalService>();
            services.AddScoped<IPricingSimulationService, PricingSimulationService>();
            services.AddScoped<ICurrentUserService, CurrentUserService>();

            // External Service Interfaces
            services.AddScoped<IIdentityService, IdentityService>();
            services.AddScoped<IUserManagementService, UserManagementService>();
            services.AddScoped<IMasterManagementService, MasterManagementService>();
            services.AddScoped<ICustomerManagementService, CustomerManagementService>();
            services.AddScoped<IShipperManagementService, ShipperManagementService>();
            services.AddScoped<IOrderManagementService, OrderManagementService>();
            services.AddScoped<IShipmentManagementService, ShipmentManagementService>();

            return services;
        }
    }
}
