using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Enums;
using UserManagement.Domain.Repositories;
using UserManagement.Application.Common.Interfaces;

namespace UserManagement.Infrastructure.Persistence.Repositories
{
    public class OrganizationRepository : Repository<Organization>, IOrganizationRepository
    {
        private readonly ICacheService _cacheService;

        public OrganizationRepository(UserManagementDbContext dbContext, ICacheService cacheService) 
            : base(dbContext)
        {
            _cacheService = cacheService;
        }

        public async Task<Organization?> GetByBusinessRegistrationNumberAsync(string number)
        {
            if (string.IsNullOrWhiteSpace(number))
                return null;

            // Try to get from cache first
            var cacheKey = $"organization:brn:{number}";
            var cachedOrg = await _cacheService.GetAsync<Organization>(cacheKey);
            if (cachedOrg != null)
            {
                return cachedOrg;
            }

            // If not in cache, get from database
            var organization = await _dbContext.Organizations
                .Where(o => o.BusinessRegistrationNumber == number)
                .Include(o => o.Branches)
                .Include(o => o.Teams)
                .Include(o => o.CustomRoles)
                .FirstOrDefaultAsync();

            // Cache the result if found
            if (organization != null)
            {
                await _cacheService.SetAsync(cacheKey, organization, TimeSpan.FromMinutes(10));
            }

            return organization;
        }

        public async Task<List<Organization>> GetByTypeAsync(OrganizationType type)
        {
            // Try to get from cache first
            var cacheKey = $"organizations:type:{type}";
            var cachedOrgs = await _cacheService.GetAsync<List<Organization>>(cacheKey);
            if (cachedOrgs != null)
            {
                return cachedOrgs;
            }

            // If not in cache, get from database
            var organizations = await _dbContext.Organizations
                .Where(o => o.Type == type)
                .ToListAsync();

            // Cache the result
            await _cacheService.SetAsync(cacheKey, organizations, TimeSpan.FromMinutes(10));

            return organizations;
        }

        public override async Task<Organization?> GetByIdAsync(Guid id)
        {
            if (id == Guid.Empty)
                return null;

            // Try to get from cache first
            var cacheKey = $"organization:{id}";
            var cachedOrg = await _cacheService.GetAsync<Organization>(cacheKey);
            if (cachedOrg != null)
            {
                return cachedOrg;
            }

            // If not in cache, get from database with related entities
            var organization = await _dbContext.Organizations
                .Where(o => o.Id == id)
                .Include(o => o.Branches)
                .Include(o => o.Teams)
                .Include(o => o.CustomRoles)
                .FirstOrDefaultAsync();

            // Cache the result if found
            if (organization != null)
            {
                await _cacheService.SetAsync(cacheKey, organization, TimeSpan.FromMinutes(10));
            }

            return organization;
        }

        public override async Task<Organization> AddAsync(Organization entity)
        {
            var result = await base.AddAsync(entity);
            
            // Invalidate cache
            await _cacheService.RemoveAsync($"organization:{entity.Id}");
            await _cacheService.RemoveAsync($"organization:brn:{entity.BusinessRegistrationNumber}");
            await _cacheService.RemoveAsync($"organizations:type:{entity.Type}");
            
            return result;
        }

        public override async Task UpdateAsync(Organization entity)
        {
            await base.UpdateAsync(entity);
            
            // Invalidate cache
            await _cacheService.RemoveAsync($"organization:{entity.Id}");
            await _cacheService.RemoveAsync($"organization:brn:{entity.BusinessRegistrationNumber}");
            await _cacheService.RemoveAsync($"organizations:type:{entity.Type}");
        }

        public override async Task DeleteAsync(Organization entity)
        {
            await base.DeleteAsync(entity);
            
            // Invalidate cache
            await _cacheService.RemoveAsync($"organization:{entity.Id}");
            await _cacheService.RemoveAsync($"organization:brn:{entity.BusinessRegistrationNumber}");
            await _cacheService.RemoveAsync($"organizations:type:{entity.Type}");
        }
    }
}
