{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\UserManagement\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{5BF3698B-A52C-472F-B7D2-DCD7A1B78150}|UserManagement.API\\UserManagement.API.csproj|d:\\projects\\tritrackz\\tritrackzmicroservices\\usermanagement\\usermanagement.api\\controllers\\organizationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5BF3698B-A52C-472F-B7D2-DCD7A1B78150}|UserManagement.API\\UserManagement.API.csproj|solutionrelative:usermanagement.api\\controllers\\organizationscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "OrganizationsController.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\UserManagement\\UserManagement.API\\Controllers\\OrganizationsController.cs", "RelativeDocumentMoniker": "UserManagement.API\\Controllers\\OrganizationsController.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\UserManagement\\UserManagement.API\\Controllers\\OrganizationsController.cs", "RelativeToolTip": "UserManagement.API\\Controllers\\OrganizationsController.cs", "ViewState": "AQIAABAAAAAAAAAAAAAuwB8AAAAMAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-02T11:04:48.474Z", "EditorCaption": ""}]}]}]}