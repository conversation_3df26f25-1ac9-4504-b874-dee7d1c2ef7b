using System;
using System.Collections.Generic;
using System.Linq;
using MasterManagement.Domain.Common;
using MasterManagement.Domain.Exceptions;
using MasterManagement.Domain.Events.Geographic;
using MasterManagement.Domain.ValueObjects;

namespace MasterManagement.Domain.Entities.Geographic
{
    public class State : AggregateRoot
    {
        public string Name { get; private set; }
        public string Code { get; private set; }
        public string Type { get; private set; } // State, Province, Territory, etc.
        public Guid CountryId { get; private set; }
        public bool IsActive { get; private set; }
        public Coordinates? Coordinates { get; private set; }
        
        // Navigation properties
        public Country Country { get; private set; }
        
        private readonly List<City> _cities = new();
        public IReadOnlyCollection<City> Cities => _cities.AsReadOnly();

        private State() { }

        public State(
            string name, 
            string code, 
            string type, 
            Guid countryId, 
            Coordinates? coordinates = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("State name cannot be empty");

            if (string.IsNullOrWhiteSpace(code))
                throw new DomainException("State code cannot be empty");

            if (countryId == Guid.Empty)
                throw new DomainException("Country ID cannot be empty");

            Name = name;
            Code = code.ToUpperInvariant();
            Type = type ?? "State";
            CountryId = countryId;
            IsActive = true;
            Coordinates = coordinates;

            AddDomainEvent(new StateCreatedEvent(Id, Name, Code, CountryId));
        }

        public void UpdateDetails(string name, string type, Coordinates? coordinates)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            Type = type ?? Type;
            Coordinates = coordinates ?? Coordinates;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new StateUpdatedEvent(Id, Name, Code));
        }

        public void AddCity(City city)
        {
            if (city == null)
                throw new DomainException("City cannot be null");

            if (_cities.Any(c => c.Name.Equals(city.Name, StringComparison.OrdinalIgnoreCase)))
                throw new DomainException($"City with name {city.Name} already exists");

            _cities.Add(city);
        }

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
