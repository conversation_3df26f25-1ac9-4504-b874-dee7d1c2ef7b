using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Services;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using System.Text.Json;

namespace PricingManagement.Infrastructure.Services
{
    public class TaxCalculationService : ITaxCalculationService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TaxCalculationService> _logger;

        // Mock tax rates by jurisdiction
        private readonly Dictionary<string, Dictionary<string, decimal>> _taxRates = new()
        {
            ["US-CA"] = new() { ["SalesTax"] = 7.25m, ["LocalTax"] = 2.5m },
            ["US-NY"] = new() { ["SalesTax"] = 8.0m, ["LocalTax"] = 3.0m },
            ["US-TX"] = new() { ["SalesTax"] = 6.25m, ["LocalTax"] = 2.0m },
            ["US-FL"] = new() { ["SalesTax"] = 6.0m, ["LocalTax"] = 1.5m },
            ["CA"] = new() { ["GST"] = 5.0m, ["PST"] = 7.0m },
            ["GB"] = new() { ["VAT"] = 20.0m },
            ["DE"] = new() { ["VAT"] = 19.0m },
            ["FR"] = new() { ["VAT"] = 20.0m }
        };

        public TaxCalculationService(
            HttpClient httpClient,
            IConfiguration configuration,
            ILogger<TaxCalculationService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<IEnumerable<TaxDto>> CalculateTaxesAsync(RateCalculationRequestDto request, Money taxableAmount)
        {
            var taxes = new List<TaxDto>();

            try
            {
                // Determine jurisdiction from destination address
                var jurisdiction = await DetermineJurisdictionAsync(request.DestinationAddress);

                // Check if customer is tax exempt
                if (request.CustomerId.HasValue)
                {
                    var isExempt = await IsTaxExemptAsync(request.CustomerId.Value, jurisdiction);
                    if (isExempt)
                    {
                        _logger.LogInformation("Customer {CustomerId} is tax exempt in jurisdiction {Jurisdiction}", 
                            request.CustomerId, jurisdiction);
                        return taxes;
                    }
                }

                // Calculate applicable taxes based on jurisdiction
                if (jurisdiction.StartsWith("US-"))
                {
                    // US Sales Tax
                    var salesTax = await CalculateSalesTaxAsync(request, taxableAmount);
                    if (salesTax.Amount.Amount > 0)
                        taxes.Add(salesTax);
                }
                else if (jurisdiction == "CA")
                {
                    // Canadian GST/PST
                    var gst = await CalculateGSTAsync(request, taxableAmount);
                    if (gst.Amount.Amount > 0)
                        taxes.Add(gst);
                }
                else if (jurisdiction == "GB" || jurisdiction == "DE" || jurisdiction == "FR")
                {
                    // European VAT
                    var vat = await CalculateVATAsync(request, taxableAmount);
                    if (vat.Amount.Amount > 0)
                        taxes.Add(vat);
                }

                // Calculate excise tax if applicable
                var exciseTax = await CalculateExciseTaxAsync(request, taxableAmount);
                if (exciseTax.Amount.Amount > 0)
                    taxes.Add(exciseTax);

                // Calculate customs duty for international shipments
                if (IsInternationalShipment(request))
                {
                    var customsDuty = await CalculateCustomsDutyAsync(request, taxableAmount);
                    if (customsDuty.Amount.Amount > 0)
                        taxes.Add(customsDuty);
                }

                return taxes;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating taxes for organization {OrganizationId}", request.OrganizationId);
                return new List<TaxDto>();
            }
        }

        public async Task<TaxDto> CalculateSalesTaxAsync(RateCalculationRequestDto request, Money taxableAmount)
        {
            var tax = new TaxDto
            {
                TaxType = TaxType.SalesTax,
                Description = "Sales Tax",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                var jurisdiction = await DetermineJurisdictionAsync(request.DestinationAddress);
                var taxRate = await GetTaxRateAsync(jurisdiction, "SalesTax");

                if (taxRate > 0)
                {
                    tax.Amount = taxableAmount.ApplyPercentage(taxRate);
                    tax.Rate = taxRate;
                    tax.Jurisdiction = jurisdiction;
                }

                return tax;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating sales tax");
                return tax;
            }
        }

        public async Task<TaxDto> CalculateVATAsync(RateCalculationRequestDto request, Money taxableAmount)
        {
            var tax = new TaxDto
            {
                TaxType = TaxType.VAT,
                Description = "Value Added Tax",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                var jurisdiction = await DetermineJurisdictionAsync(request.DestinationAddress);
                var taxRate = await GetTaxRateAsync(jurisdiction, "VAT");

                if (taxRate > 0)
                {
                    tax.Amount = taxableAmount.ApplyPercentage(taxRate);
                    tax.Rate = taxRate;
                    tax.Jurisdiction = jurisdiction;
                }

                return tax;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating VAT");
                return tax;
            }
        }

        public async Task<TaxDto> CalculateGSTAsync(RateCalculationRequestDto request, Money taxableAmount)
        {
            var tax = new TaxDto
            {
                TaxType = TaxType.GST,
                Description = "Goods and Services Tax",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                var jurisdiction = await DetermineJurisdictionAsync(request.DestinationAddress);
                var taxRate = await GetTaxRateAsync(jurisdiction, "GST");

                if (taxRate > 0)
                {
                    tax.Amount = taxableAmount.ApplyPercentage(taxRate);
                    tax.Rate = taxRate;
                    tax.Jurisdiction = jurisdiction;
                }

                return tax;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating GST");
                return tax;
            }
        }

        public async Task<TaxDto> CalculateExciseTaxAsync(RateCalculationRequestDto request, Money taxableAmount)
        {
            var tax = new TaxDto
            {
                TaxType = TaxType.ExciseTax,
                Description = "Excise Tax",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                // Apply excise tax for specific service types or special services
                if (request.SpecialServices.Contains("HAZMAT", StringComparer.OrdinalIgnoreCase))
                {
                    var exciseRate = _configuration.GetValue<decimal>("TaxSettings:HazmatExciseRate", 2.0m);
                    tax.Amount = taxableAmount.ApplyPercentage(exciseRate);
                    tax.Rate = exciseRate;
                    tax.Jurisdiction = "Federal";
                    tax.Description = "Hazmat Excise Tax";
                }

                return tax;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating excise tax");
                return tax;
            }
        }

        public async Task<TaxDto> CalculateCustomsDutyAsync(RateCalculationRequestDto request, Money taxableAmount)
        {
            var tax = new TaxDto
            {
                TaxType = TaxType.CustomsDuty,
                Description = "Customs Duty",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                if (IsInternationalShipment(request) && request.DeclaredValue.HasValue)
                {
                    var dutyRate = _configuration.GetValue<decimal>("TaxSettings:CustomsDutyRate", 5.0m);
                    var dutyAmount = new Money(request.DeclaredValue.Value, request.Currency);
                    tax.Amount = dutyAmount.ApplyPercentage(dutyRate);
                    tax.Rate = dutyRate;
                    tax.Jurisdiction = "Customs";
                }

                return tax;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating customs duty");
                return tax;
            }
        }

        public async Task<decimal> GetTaxRateAsync(string jurisdiction, string taxType)
        {
            try
            {
                if (_taxRates.TryGetValue(jurisdiction, out var jurisdictionRates))
                {
                    if (jurisdictionRates.TryGetValue(taxType, out var rate))
                    {
                        return rate;
                    }
                }

                // TODO: Integrate with external tax API for real-time rates
                return 0m;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting tax rate for {Jurisdiction} {TaxType}", jurisdiction, taxType);
                return 0m;
            }
        }

        public async Task<bool> IsTaxExemptAsync(Guid? customerId, string jurisdiction)
        {
            try
            {
                if (!customerId.HasValue)
                    return false;

                // TODO: Integrate with customer management to check tax exemption status
                // For now, return false (no exemptions)
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking tax exemption for customer {CustomerId}", customerId);
                return false;
            }
        }

        public async Task<string> DetermineJurisdictionAsync(string address)
        {
            try
            {
                // Simple jurisdiction determination based on address
                // TODO: Integrate with address validation service for accurate jurisdiction mapping
                
                if (address.Contains("California", StringComparison.OrdinalIgnoreCase) || address.Contains("CA", StringComparison.OrdinalIgnoreCase))
                    return "US-CA";
                if (address.Contains("New York", StringComparison.OrdinalIgnoreCase) || address.Contains("NY", StringComparison.OrdinalIgnoreCase))
                    return "US-NY";
                if (address.Contains("Texas", StringComparison.OrdinalIgnoreCase) || address.Contains("TX", StringComparison.OrdinalIgnoreCase))
                    return "US-TX";
                if (address.Contains("Florida", StringComparison.OrdinalIgnoreCase) || address.Contains("FL", StringComparison.OrdinalIgnoreCase))
                    return "US-FL";
                if (address.Contains("Canada", StringComparison.OrdinalIgnoreCase))
                    return "CA";
                if (address.Contains("United Kingdom", StringComparison.OrdinalIgnoreCase) || address.Contains("UK", StringComparison.OrdinalIgnoreCase))
                    return "GB";
                if (address.Contains("Germany", StringComparison.OrdinalIgnoreCase))
                    return "DE";
                if (address.Contains("France", StringComparison.OrdinalIgnoreCase))
                    return "FR";

                // Default to US if no specific jurisdiction found
                return "US";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error determining jurisdiction for address {Address}", address);
                return "US";
            }
        }

        public async Task<bool> ValidateTaxExemptionCertificateAsync(Guid customerId, string certificateNumber)
        {
            try
            {
                // TODO: Integrate with tax authority APIs to validate exemption certificates
                return !string.IsNullOrWhiteSpace(certificateNumber);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating tax exemption certificate {CertificateNumber} for customer {CustomerId}", 
                    certificateNumber, customerId);
                return false;
            }
        }

        private bool IsInternationalShipment(RateCalculationRequestDto request)
        {
            // Simple check for international shipment
            // TODO: Implement proper country detection from addresses
            var originCountry = ExtractCountryFromAddress(request.OriginAddress);
            var destinationCountry = ExtractCountryFromAddress(request.DestinationAddress);
            
            return !string.Equals(originCountry, destinationCountry, StringComparison.OrdinalIgnoreCase);
        }

        private string ExtractCountryFromAddress(string address)
        {
            // Simple country extraction
            if (address.Contains("Canada", StringComparison.OrdinalIgnoreCase))
                return "CA";
            if (address.Contains("United Kingdom", StringComparison.OrdinalIgnoreCase) || address.Contains("UK", StringComparison.OrdinalIgnoreCase))
                return "GB";
            if (address.Contains("Germany", StringComparison.OrdinalIgnoreCase))
                return "DE";
            if (address.Contains("France", StringComparison.OrdinalIgnoreCase))
                return "FR";
            
            return "US"; // Default
        }
    }
}
