using System;
using System.Collections.Generic;
using System.Linq;
using MasterManagement.Domain.Common;
using MasterManagement.Domain.Exceptions;
using MasterManagement.Domain.Events.Geographic;
using MasterManagement.Domain.ValueObjects;

namespace MasterManagement.Domain.Entities.Geographic
{
    public class Zone : AggregateRoot
    {
        public string Name { get; private set; }
        public string Code { get; private set; }
        public string Description { get; private set; }
        public Guid CityId { get; private set; }
        public Guid StateId { get; private set; }
        public Guid CountryId { get; private set; }
        public Guid OrganizationId { get; private set; } // Multi-tenant
        public bool IsActive { get; private set; }
        public Coordinates? Coordinates { get; private set; }
        public string ZoneType { get; private set; } // Commercial, Residential, Industrial, etc.
        
        // Navigation properties
        public City City { get; private set; }
        public State State { get; private set; }
        public Country Country { get; private set; }
        
        private readonly List<PostalCode> _postalCodes = new();
        public IReadOnlyCollection<PostalCode> PostalCodes => _postalCodes.AsReadOnly();

        private Zone() { }

        public Zone(
            string name, 
            string code, 
            string description,
            Guid cityId, 
            Guid stateId, 
            Guid countryId,
            Guid organizationId,
            string zoneType = "General",
            Coordinates? coordinates = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Zone name cannot be empty");

            if (string.IsNullOrWhiteSpace(code))
                throw new DomainException("Zone code cannot be empty");

            if (cityId == Guid.Empty)
                throw new DomainException("City ID cannot be empty");

            if (stateId == Guid.Empty)
                throw new DomainException("State ID cannot be empty");

            if (countryId == Guid.Empty)
                throw new DomainException("Country ID cannot be empty");

            if (organizationId == Guid.Empty)
                throw new DomainException("Organization ID cannot be empty");

            Name = name;
            Code = code.ToUpperInvariant();
            Description = description ?? string.Empty;
            CityId = cityId;
            StateId = stateId;
            CountryId = countryId;
            OrganizationId = organizationId;
            IsActive = true;
            ZoneType = zoneType;
            Coordinates = coordinates;

            AddDomainEvent(new ZoneCreatedEvent(Id, Name, Code, OrganizationId, CityId));
        }

        public void UpdateDetails(
            string name, 
            string description, 
            string zoneType, 
            Coordinates? coordinates)
        {
            if (!string.IsNullOrWhiteSpace(name))
                Name = name;

            Description = description ?? Description;
            ZoneType = zoneType ?? ZoneType;
            Coordinates = coordinates ?? Coordinates;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new ZoneUpdatedEvent(Id, Name, Code, OrganizationId));
        }

        public void AddPostalCode(PostalCode postalCode)
        {
            if (postalCode == null)
                throw new DomainException("Postal code cannot be null");

            if (_postalCodes.Any(p => p.Code == postalCode.Code))
                throw new DomainException($"Postal code {postalCode.Code} already exists");

            _postalCodes.Add(postalCode);
        }

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
