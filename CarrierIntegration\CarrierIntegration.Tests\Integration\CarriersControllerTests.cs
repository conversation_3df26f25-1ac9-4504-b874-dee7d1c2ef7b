using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Domain.Enums;
using CarrierIntegration.Tests.Common;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using System;
using System.Net;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Xunit;

namespace CarrierIntegration.Tests.Integration
{
    public class CarriersControllerTests : IClassFixture<CarrierIntegrationWebApplicationFactory>
    {
        private readonly HttpClient _client;
        private readonly CarrierIntegrationWebApplicationFactory _factory;

        public CarriersControllerTests(CarrierIntegrationWebApplicationFactory factory)
        {
            _factory = factory;
            _client = factory.CreateClient();
        }

        [Fact]
        public async Task GetCarriers_WithValidOrganizationId_ReturnsCarriers()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            await _factory.SeedTestDataAsync(organizationId);

            // Act
            var response = await _client.GetAsync($"/api/v1/carriers?organizationId={organizationId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<PagedResultDto<CarrierDto>>>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Items.Should().NotBeEmpty();
        }

        [Fact]
        public async Task CreateCarrier_WithValidData_ReturnsCreatedCarrier()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var createCommand = new
            {
                OrganizationId = organizationId,
                Name = "Test Carrier",
                Code = "TEST",
                Type = CarrierType.NationalCarrier,
                Description = "Test carrier for integration testing",
                PrimaryContact = new
                {
                    Name = "Test Contact",
                    Email = "<EMAIL>",
                    Phone = "******-0123"
                },
                HeadquartersAddress = new
                {
                    Street1 = "123 Test Street",
                    City = "Test City",
                    State = "Test State",
                    PostalCode = "12345",
                    Country = "United States"
                },
                SupportsTracking = true,
                SupportsRating = true,
                SupportsLabeling = true,
                SupportsPickup = true,
                SupportsDeliveryConfirmation = true,
                SupportsInternational = false,
                RequiresAccount = true,
                Priority = 50
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/carriers", createCommand);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<CarrierDto>>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Name.Should().Be("Test Carrier");
            result.Data.Code.Should().Be("TEST");
            result.Data.Type.Should().Be(CarrierType.NationalCarrier);
        }

        [Fact]
        public async Task CreateCarrier_WithDuplicateCode_ReturnsBadRequest()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            await _factory.SeedTestDataAsync(organizationId);

            var createCommand = new
            {
                OrganizationId = organizationId,
                Name = "Duplicate Carrier",
                Code = "UPS", // This should already exist from seeding
                Type = CarrierType.NationalCarrier,
                PrimaryContact = new
                {
                    Name = "Test Contact",
                    Email = "<EMAIL>",
                    Phone = "******-0123"
                },
                HeadquartersAddress = new
                {
                    Street1 = "123 Test Street",
                    City = "Test City",
                    State = "Test State",
                    PostalCode = "12345",
                    Country = "United States"
                }
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/carriers", createCommand);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GetCarrier_WithValidId_ReturnsCarrier()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var carrierId = await _factory.SeedTestDataAsync(organizationId);

            // Act
            var response = await _client.GetAsync($"/api/v1/carriers/{carrierId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<CarrierDto>>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Id.Should().Be(carrierId);
        }

        [Fact]
        public async Task GetCarrier_WithInvalidId_ReturnsNotFound()
        {
            // Arrange
            var invalidId = Guid.NewGuid();

            // Act
            var response = await _client.GetAsync($"/api/v1/carriers/{invalidId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task GetActiveCarriers_WithValidOrganizationId_ReturnsOnlyActiveCarriers()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            await _factory.SeedTestDataAsync(organizationId);

            // Act
            var response = await _client.GetAsync($"/api/v1/carriers/active?organizationId={organizationId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<CarrierDto>>>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Should().OnlyContain(c => c.IsActive);
        }

        [Fact]
        public async Task GetCarriersByType_WithValidType_ReturnsCarriersOfType()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            await _factory.SeedTestDataAsync(organizationId);
            var carrierType = CarrierType.NationalCarrier;

            // Act
            var response = await _client.GetAsync($"/api/v1/carriers/by-type/{carrierType}?organizationId={organizationId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<List<CarrierDto>>>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Should().OnlyContain(c => c.Type == carrierType);
        }

        [Fact]
        public async Task ActivateCarrier_WithValidId_ReturnsSuccess()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var carrierId = await _factory.SeedTestDataAsync(organizationId);

            // First deactivate the carrier
            await _client.PostAsJsonAsync($"/api/v1/carriers/{carrierId}/deactivate", new { Reason = "Test deactivation" });

            // Act
            var response = await _client.PostAsync($"/api/v1/carriers/{carrierId}/activate", null);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<bool>>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Data.Should().BeTrue();
        }

        [Fact]
        public async Task DeactivateCarrier_WithValidId_ReturnsSuccess()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var carrierId = await _factory.SeedTestDataAsync(organizationId);

            var deactivateRequest = new { Reason = "Test deactivation" };

            // Act
            var response = await _client.PostAsJsonAsync($"/api/v1/carriers/{carrierId}/deactivate", deactivateRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<bool>>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Data.Should().BeTrue();
        }

        [Fact]
        public async Task PerformHealthCheck_WithValidId_ReturnsHealthCheckResult()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            var carrierId = await _factory.SeedTestDataAsync(organizationId);

            // Act
            var response = await _client.PostAsync($"/api/v1/carriers/{carrierId}/health-check", null);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<CarrierHealthCheckResult>>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.CheckedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        }

        [Fact]
        public async Task GetCarriers_WithSearchTerm_ReturnsFilteredResults()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            await _factory.SeedTestDataAsync(organizationId);
            var searchTerm = "UPS";

            // Act
            var response = await _client.GetAsync($"/api/v1/carriers?organizationId={organizationId}&searchTerm={searchTerm}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<PagedResultDto<CarrierDto>>>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Items.Should().OnlyContain(c => 
                c.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                c.Code.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                (c.Description != null && c.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)));
        }

        [Fact]
        public async Task GetCarriers_WithPagination_ReturnsCorrectPage()
        {
            // Arrange
            var organizationId = Guid.NewGuid();
            await _factory.SeedTestDataAsync(organizationId);
            var pageNumber = 1;
            var pageSize = 2;

            // Act
            var response = await _client.GetAsync($"/api/v1/carriers?organizationId={organizationId}&pageNumber={pageNumber}&pageSize={pageSize}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<PagedResultDto<CarrierDto>>>();
            result.Should().NotBeNull();
            result!.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.PageNumber.Should().Be(pageNumber);
            result.Data.PageSize.Should().Be(pageSize);
            result.Data.Items.Should().HaveCountLessOrEqualTo(pageSize);
        }
    }
}
