using PricingManagement.Domain.Common;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.Events;
using PricingManagement.Domain.Exceptions;
using PricingManagement.Domain.ValueObjects;
using System;
using System.Collections.Generic;

namespace PricingManagement.Domain.Entities
{
    public class Quote : BaseEntity
    {
        public string QuoteNumber { get; private set; }
        public Guid? CustomerId { get; private set; }
        public Guid? ShipperId { get; private set; }
        public string? CustomerName { get; private set; }
        public string? ShipperName { get; private set; }
        public QuoteStatus Status { get; private set; }
        public DateTime ExpirationDate { get; private set; }
        public string? OriginAddress { get; private set; }
        public string? DestinationAddress { get; private set; }
        public string? OriginZoneId { get; private set; }
        public string? DestinationZoneId { get; private set; }
        public decimal? Distance { get; private set; }
        public string? ServiceType { get; private set; }
        public Weight? PackageWeight { get; private set; }
        public Dimensions? PackageDimensions { get; private set; }
        public Weight? DimensionalWeight { get; private set; }
        public Weight? BillableWeight { get; private set; }
        public decimal? DeclaredValue { get; private set; }
        public string? SpecialServices { get; private set; } // JSON array
        public Money BaseRate { get; private set; }
        public Money TotalSurcharges { get; private set; }
        public Money TotalDiscounts { get; private set; }
        public Money TotalTaxes { get; private set; }
        public Money TotalAmount { get; private set; }
        public CurrencyCode Currency { get; private set; }
        public string? Notes { get; private set; }
        public string? Terms { get; private set; }
        public DateTime? ConvertedToOrderAt { get; private set; }
        public Guid? OrderId { get; private set; }
        public string? ExternalQuoteId { get; private set; }
        public string? CarrierQuoteData { get; private set; } // JSON for carrier-specific data

        private readonly List<QuoteLineItem> _lineItems = new();
        public IReadOnlyCollection<QuoteLineItem> LineItems => _lineItems.AsReadOnly();

        private readonly List<QuoteSurcharge> _surcharges = new();
        public IReadOnlyCollection<QuoteSurcharge> Surcharges => _surcharges.AsReadOnly();

        private readonly List<QuoteDiscount> _discounts = new();
        public IReadOnlyCollection<QuoteDiscount> Discounts => _discounts.AsReadOnly();

        private readonly List<QuoteTax> _taxes = new();
        public IReadOnlyCollection<QuoteTax> Taxes => _taxes.AsReadOnly();

        protected Quote() { }

        public Quote(
            string quoteNumber,
            DateTime expirationDate,
            CurrencyCode currency,
            Guid organizationId,
            string createdBy) : base(organizationId, createdBy)
        {
            if (string.IsNullOrWhiteSpace(quoteNumber))
                throw new DomainException("Quote number cannot be empty");

            if (expirationDate <= DateTime.UtcNow)
                throw new DomainException("Expiration date must be in the future");

            QuoteNumber = quoteNumber;
            ExpirationDate = expirationDate;
            Currency = currency;
            Status = QuoteStatus.Draft;
            BaseRate = Money.Zero(currency);
            TotalSurcharges = Money.Zero(currency);
            TotalDiscounts = Money.Zero(currency);
            TotalTaxes = Money.Zero(currency);
            TotalAmount = Money.Zero(currency);

            AddDomainEvent(new QuoteCreatedEvent(Id, QuoteNumber, OrganizationId));
        }

        public void SetCustomer(Guid customerId, string customerName, string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(customerName))
                throw new DomainException("Customer name cannot be empty");

            CustomerId = customerId;
            CustomerName = customerName;
            Update(updatedBy);
        }

        public void SetShipper(Guid shipperId, string shipperName, string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(shipperName))
                throw new DomainException("Shipper name cannot be empty");

            ShipperId = shipperId;
            ShipperName = shipperName;
            Update(updatedBy);
        }

        public void SetShipmentDetails(
            string originAddress,
            string destinationAddress,
            string? originZoneId,
            string? destinationZoneId,
            decimal? distance,
            string serviceType,
            Weight packageWeight,
            Dimensions? packageDimensions,
            decimal? declaredValue,
            string? specialServices,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(originAddress))
                throw new DomainException("Origin address cannot be empty");

            if (string.IsNullOrWhiteSpace(destinationAddress))
                throw new DomainException("Destination address cannot be empty");

            if (string.IsNullOrWhiteSpace(serviceType))
                throw new DomainException("Service type cannot be empty");

            if (distance.HasValue && distance < 0)
                throw new DomainException("Distance cannot be negative");

            if (declaredValue.HasValue && declaredValue < 0)
                throw new DomainException("Declared value cannot be negative");

            OriginAddress = originAddress;
            DestinationAddress = destinationAddress;
            OriginZoneId = originZoneId;
            DestinationZoneId = destinationZoneId;
            Distance = distance;
            ServiceType = serviceType;
            PackageWeight = packageWeight;
            PackageDimensions = packageDimensions;
            DeclaredValue = declaredValue;
            SpecialServices = specialServices;

            // Calculate dimensional weight if dimensions are provided
            if (packageDimensions != null)
            {
                DimensionalWeight = packageDimensions.CalculateDimensionalWeight(139, WeightUnit.Pounds);
                BillableWeight = packageWeight > DimensionalWeight ? packageWeight : DimensionalWeight;
            }
            else
            {
                BillableWeight = packageWeight;
            }

            Update(updatedBy);
        }

        public void SetBaseRate(Money baseRate, string updatedBy)
        {
            if (baseRate.Currency != Currency)
                throw new InvalidCurrencyException($"Base rate currency {baseRate.Currency} does not match quote currency {Currency}");

            BaseRate = baseRate;
            RecalculateTotal();
            Update(updatedBy);
        }

        public void AddLineItem(string description, decimal quantity, Money unitPrice, string? itemType, string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(description))
                throw new DomainException("Line item description cannot be empty");

            if (quantity <= 0)
                throw new DomainException("Quantity must be positive");

            if (unitPrice.Currency != Currency)
                throw new InvalidCurrencyException($"Unit price currency {unitPrice.Currency} does not match quote currency {Currency}");

            var lineItem = new QuoteLineItem(description, quantity, unitPrice, itemType);
            _lineItems.Add(lineItem);
            RecalculateTotal();
            Update(updatedBy);
        }

        public void AddSurcharge(SurchargeType surchargeType, string description, Money amount, string? calculationBasis, string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(description))
                throw new DomainException("Surcharge description cannot be empty");

            if (amount.Currency != Currency)
                throw new InvalidCurrencyException($"Surcharge currency {amount.Currency} does not match quote currency {Currency}");

            var surcharge = new QuoteSurcharge(surchargeType, description, amount, calculationBasis);
            _surcharges.Add(surcharge);
            RecalculateSurcharges();
            Update(updatedBy);
        }

        public void AddDiscount(DiscountType discountType, string description, Money amount, decimal? percentage, string? calculationBasis, string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(description))
                throw new DomainException("Discount description cannot be empty");

            if (amount.Currency != Currency)
                throw new InvalidCurrencyException($"Discount currency {amount.Currency} does not match quote currency {Currency}");

            if (percentage.HasValue && (percentage < 0 || percentage > 100))
                throw new InvalidDiscountException("Percentage must be between 0 and 100");

            var discount = new QuoteDiscount(discountType, description, amount, percentage, calculationBasis);
            _discounts.Add(discount);
            RecalculateDiscounts();
            Update(updatedBy);
        }

        public void AddTax(TaxType taxType, string description, Money amount, decimal rate, string? jurisdiction, string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(description))
                throw new DomainException("Tax description cannot be empty");

            if (amount.Currency != Currency)
                throw new InvalidCurrencyException($"Tax currency {amount.Currency} does not match quote currency {Currency}");

            if (rate < 0)
                throw new DomainException("Tax rate cannot be negative");

            var tax = new QuoteTax(taxType, description, amount, rate, jurisdiction);
            _taxes.Add(tax);
            RecalculateTaxes();
            Update(updatedBy);
        }

        public void ClearLineItems(string updatedBy)
        {
            _lineItems.Clear();
            RecalculateTotal();
            Update(updatedBy);
        }

        public void ClearSurcharges(string updatedBy)
        {
            _surcharges.Clear();
            RecalculateSurcharges();
            Update(updatedBy);
        }

        public void ClearDiscounts(string updatedBy)
        {
            _discounts.Clear();
            RecalculateDiscounts();
            Update(updatedBy);
        }

        public void ClearTaxes(string updatedBy)
        {
            _taxes.Clear();
            RecalculateTaxes();
            Update(updatedBy);
        }

        private void RecalculateSurcharges()
        {
            var total = Money.Zero(Currency);
            foreach (var surcharge in _surcharges)
            {
                total = total.Add(surcharge.Amount);
            }
            TotalSurcharges = total;
            RecalculateTotal();
        }

        private void RecalculateDiscounts()
        {
            var total = Money.Zero(Currency);
            foreach (var discount in _discounts)
            {
                total = total.Add(discount.Amount);
            }
            TotalDiscounts = total;
            RecalculateTotal();
        }

        private void RecalculateTaxes()
        {
            var total = Money.Zero(Currency);
            foreach (var tax in _taxes)
            {
                total = total.Add(tax.Amount);
            }
            TotalTaxes = total;
            RecalculateTotal();
        }

        private void RecalculateTotal()
        {
            var lineItemsTotal = Money.Zero(Currency);
            foreach (var lineItem in _lineItems)
            {
                lineItemsTotal = lineItemsTotal.Add(lineItem.TotalPrice);
            }

            TotalAmount = BaseRate
                .Add(lineItemsTotal)
                .Add(TotalSurcharges)
                .Subtract(TotalDiscounts)
                .Add(TotalTaxes);
        }

        public void Generate(string updatedBy)
        {
            if (Status != QuoteStatus.Draft)
                throw new DomainException("Only draft quotes can be generated");

            if (TotalAmount.Amount == 0)
                throw new DomainException("Quote must have a total amount greater than zero");

            Status = QuoteStatus.Generated;
            Update(updatedBy);

            AddDomainEvent(new QuoteGeneratedEvent(Id, QuoteNumber, TotalAmount, OrganizationId));
        }

        public void Send(string updatedBy)
        {
            if (Status != QuoteStatus.Generated)
                throw new DomainException("Only generated quotes can be sent");

            CheckExpiration();

            Status = QuoteStatus.Sent;
            Update(updatedBy);

            AddDomainEvent(new QuoteSentEvent(Id, QuoteNumber, OrganizationId));
        }

        public void Accept(string updatedBy)
        {
            if (Status != QuoteStatus.Sent)
                throw new DomainException("Only sent quotes can be accepted");

            CheckExpiration();

            Status = QuoteStatus.Accepted;
            Update(updatedBy);

            AddDomainEvent(new QuoteAcceptedEvent(Id, QuoteNumber, TotalAmount, OrganizationId));
        }

        public void Reject(string reason, string updatedBy)
        {
            if (Status != QuoteStatus.Sent)
                throw new DomainException("Only sent quotes can be rejected");

            Status = QuoteStatus.Rejected;
            Notes = string.IsNullOrWhiteSpace(Notes) ? reason : $"{Notes}\nRejection reason: {reason}";
            Update(updatedBy);

            AddDomainEvent(new QuoteRejectedEvent(Id, QuoteNumber, reason, OrganizationId));
        }

        public void ConvertToOrder(Guid orderId, string updatedBy)
        {
            if (Status != QuoteStatus.Accepted)
                throw new DomainException("Only accepted quotes can be converted to orders");

            CheckExpiration();

            Status = QuoteStatus.Converted;
            ConvertedToOrderAt = DateTime.UtcNow;
            OrderId = orderId;
            Update(updatedBy);

            AddDomainEvent(new QuoteConvertedEvent(Id, QuoteNumber, orderId, OrganizationId));
        }

        public void Expire(string updatedBy)
        {
            if (Status == QuoteStatus.Expired)
                return;

            if (Status == QuoteStatus.Converted || Status == QuoteStatus.Accepted)
                throw new DomainException("Converted or accepted quotes cannot be expired");

            Status = QuoteStatus.Expired;
            Update(updatedBy);

            AddDomainEvent(new QuoteExpiredEvent(Id, QuoteNumber, OrganizationId));
        }

        public void SetNotes(string? notes, string updatedBy)
        {
            Notes = notes;
            Update(updatedBy);
        }

        public void SetTerms(string? terms, string updatedBy)
        {
            Terms = terms;
            Update(updatedBy);
        }

        public void SetExternalQuoteId(string? externalQuoteId, string updatedBy)
        {
            ExternalQuoteId = externalQuoteId;
            Update(updatedBy);
        }

        public void SetCarrierQuoteData(string? carrierQuoteData, string updatedBy)
        {
            CarrierQuoteData = carrierQuoteData;
            Update(updatedBy);
        }

        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpirationDate;
        }

        public void CheckExpiration()
        {
            if (IsExpired())
                throw new ExpiredQuoteException(Id);
        }

        public void CheckAndUpdateExpiration()
        {
            if (IsExpired() && Status != QuoteStatus.Expired && Status != QuoteStatus.Converted)
            {
                Status = QuoteStatus.Expired;
                AddDomainEvent(new QuoteExpiredEvent(Id, QuoteNumber, OrganizationId));
            }
        }
    }

    public class QuoteLineItem
    {
        public string Description { get; private set; }
        public decimal Quantity { get; private set; }
        public Money UnitPrice { get; private set; }
        public Money TotalPrice { get; private set; }
        public string? ItemType { get; private set; }

        public QuoteLineItem(string description, decimal quantity, Money unitPrice, string? itemType)
        {
            Description = description;
            Quantity = quantity;
            UnitPrice = unitPrice;
            TotalPrice = unitPrice.Multiply(quantity);
            ItemType = itemType;
        }
    }

    public class QuoteSurcharge
    {
        public SurchargeType SurchargeType { get; private set; }
        public string Description { get; private set; }
        public Money Amount { get; private set; }
        public string? CalculationBasis { get; private set; }

        public QuoteSurcharge(SurchargeType surchargeType, string description, Money amount, string? calculationBasis)
        {
            SurchargeType = surchargeType;
            Description = description;
            Amount = amount;
            CalculationBasis = calculationBasis;
        }
    }

    public class QuoteDiscount
    {
        public DiscountType DiscountType { get; private set; }
        public string Description { get; private set; }
        public Money Amount { get; private set; }
        public decimal? Percentage { get; private set; }
        public string? CalculationBasis { get; private set; }

        public QuoteDiscount(DiscountType discountType, string description, Money amount, decimal? percentage, string? calculationBasis)
        {
            DiscountType = discountType;
            Description = description;
            Amount = amount;
            Percentage = percentage;
            CalculationBasis = calculationBasis;
        }
    }

    public class QuoteTax
    {
        public TaxType TaxType { get; private set; }
        public string Description { get; private set; }
        public Money Amount { get; private set; }
        public decimal Rate { get; private set; }
        public string? Jurisdiction { get; private set; }

        public QuoteTax(TaxType taxType, string description, Money amount, decimal rate, string? jurisdiction)
        {
            TaxType = taxType;
            Description = description;
            Amount = amount;
            Rate = rate;
            Jurisdiction = jurisdiction;
        }
    }
}
