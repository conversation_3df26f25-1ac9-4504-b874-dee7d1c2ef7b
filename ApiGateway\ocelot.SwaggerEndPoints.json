{
  "SwaggerEndPoints": [
    {
      "Key": "identity",
      "Config": [
        {
          "Name": "Identity API",
          "Version": "v1",
          "Url": "http://localhost:5005/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "usermanagement",
      "Config": [
        {
          "Name": "User Management API",
          "Version": "v1",
          "Url": "http://localhost:5002/swagger/v1/swagger.json"
        }
      ]
    },
    {
      "Key": "customermanagement",
      "Config": [
        {
          "Name": "Customer Management API",
          "Version": "v1",
          "Url": "http://localhost:5004/swagger/v1/swagger.json"
        }
      ]
    }
    // {
    //   "Key": "mastermanagement",
    //   "Config": [
    //     {
    //       "Name": "Master Management API",
    //       "Version": "v1",
    //       "Url": "http://localhost:5007/swagger/v1/swagger.json"
    //     }
    //   ]
    // }
  ]
}
