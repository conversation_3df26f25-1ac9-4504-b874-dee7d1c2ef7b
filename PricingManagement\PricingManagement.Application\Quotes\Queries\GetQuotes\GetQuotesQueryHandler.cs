using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Common;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Repositories;

namespace PricingManagement.Application.Quotes.Queries.GetQuotes
{
    public class GetQuotesQueryHandler : IRequestHandler<GetQuotesQuery, PagedResultDto<QuoteDto>>
    {
        private readonly IQuoteRepository _quoteRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILogger<GetQuotesQueryHandler> _logger;

        public GetQuotesQueryHandler(
            IQuoteRepository quoteRepository,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILogger<GetQuotesQueryHandler> logger)
        {
            _quoteRepository = quoteRepository;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<PagedResultDto<QuoteDto>> Handle(GetQuotesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_currentUserService.OrganizationId.HasValue)
                {
                    return new PagedResultDto<QuoteDto>
                    {
                        Items = new List<QuoteDto>(),
                        TotalCount = 0,
                        PageNumber = request.PageNumber,
                        PageSize = request.PageSize
                    };
                }

                var organizationId = _currentUserService.OrganizationId.Value;

                // Get quotes based on filters
                IEnumerable<Domain.Entities.Quote> quotes;

                if (request.ConvertedOnly)
                {
                    quotes = await _quoteRepository.GetConvertedQuotesAsync(organizationId, request.FromDate, request.ToDate);
                }
                else if (request.ExpiringBefore.HasValue)
                {
                    quotes = await _quoteRepository.GetExpiringQuotesAsync(organizationId, request.ExpiringBefore.Value);
                }
                else if (request.CustomerId.HasValue)
                {
                    quotes = await _quoteRepository.GetByCustomerAsync(request.CustomerId.Value, organizationId);
                }
                else if (request.ShipperId.HasValue)
                {
                    quotes = await _quoteRepository.GetByShipperAsync(request.ShipperId.Value, organizationId);
                }
                else if (request.Status.HasValue)
                {
                    quotes = await _quoteRepository.GetByStatusAsync(request.Status.Value, organizationId);
                }
                else if (request.FromDate.HasValue || request.ToDate.HasValue)
                {
                    var fromDate = request.FromDate ?? DateTime.MinValue;
                    var toDate = request.ToDate ?? DateTime.MaxValue;
                    quotes = await _quoteRepository.GetQuotesByDateRangeAsync(organizationId, fromDate, toDate);
                }
                else
                {
                    quotes = await _quoteRepository.GetByOrganizationAsync(organizationId);
                }

                // Apply additional filters
                var filteredQuotes = quotes.AsQueryable();

                if (!string.IsNullOrWhiteSpace(request.SearchTerm))
                {
                    filteredQuotes = filteredQuotes.Where(q =>
                        q.QuoteNumber.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                        (q.CustomerName != null && q.CustomerName.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)) ||
                        (q.ShipperName != null && q.ShipperName.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)) ||
                        (q.OriginAddress != null && q.OriginAddress.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)) ||
                        (q.DestinationAddress != null && q.DestinationAddress.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)));
                }

                if (request.MinAmount.HasValue)
                {
                    filteredQuotes = filteredQuotes.Where(q => q.TotalAmount.Amount >= request.MinAmount.Value);
                }

                if (request.MaxAmount.HasValue)
                {
                    filteredQuotes = filteredQuotes.Where(q => q.TotalAmount.Amount <= request.MaxAmount.Value);
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(request.SortBy))
                {
                    filteredQuotes = request.SortBy.ToLower() switch
                    {
                        "quotenumber" => request.SortDescending
                            ? filteredQuotes.OrderByDescending(q => q.QuoteNumber)
                            : filteredQuotes.OrderBy(q => q.QuoteNumber),
                        "customername" => request.SortDescending
                            ? filteredQuotes.OrderByDescending(q => q.CustomerName)
                            : filteredQuotes.OrderBy(q => q.CustomerName),
                        "totalamount" => request.SortDescending
                            ? filteredQuotes.OrderByDescending(q => q.TotalAmount.Amount)
                            : filteredQuotes.OrderBy(q => q.TotalAmount.Amount),
                        "expirationdate" => request.SortDescending
                            ? filteredQuotes.OrderByDescending(q => q.ExpirationDate)
                            : filteredQuotes.OrderBy(q => q.ExpirationDate),
                        "status" => request.SortDescending
                            ? filteredQuotes.OrderByDescending(q => q.Status)
                            : filteredQuotes.OrderBy(q => q.Status),
                        "createdat" => request.SortDescending
                            ? filteredQuotes.OrderByDescending(q => q.CreatedAt)
                            : filteredQuotes.OrderBy(q => q.CreatedAt),
                        _ => filteredQuotes.OrderByDescending(q => q.CreatedAt)
                    };
                }
                else
                {
                    filteredQuotes = filteredQuotes.OrderByDescending(q => q.CreatedAt);
                }

                var totalCount = filteredQuotes.Count();

                // Apply pagination
                var pagedQuotes = filteredQuotes
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                // Map to DTOs
                var quoteDtos = _mapper.Map<List<QuoteDto>>(pagedQuotes);

                _logger.LogInformation("Retrieved {Count} quotes for organization {OrganizationId}",
                    pagedQuotes.Count, organizationId);

                return new PagedResultDto<QuoteDto>
                {
                    Items = quoteDtos,
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving quotes");
                return new PagedResultDto<QuoteDto>
                {
                    Items = new List<QuoteDto>(),
                    TotalCount = 0,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }
        }
    }
}
