using Microsoft.EntityFrameworkCore;
using PricingManagement.Application.Common;
using PricingManagement.Domain.Common;
using PricingManagement.Domain.Entities;
using PricingManagement.Infrastructure.Persistence.Configurations;
using System.Linq.Expressions;
using System.Reflection;

namespace PricingManagement.Infrastructure.Persistence
{
    public class PricingManagementDbContext : DbContext
    {
        private readonly ICurrentUserService _currentUserService;

        public PricingManagementDbContext(
            DbContextOptions<PricingManagementDbContext> options,
            ICurrentUserService currentUserService) : base(options)
        {
            _currentUserService = currentUserService;
        }

        public DbSet<PricingRule> PricingRules => Set<PricingRule>();
        public DbSet<Quote> Quotes => Set<Quote>();
        public DbSet<Contract> Contracts => Set<Contract>();

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Apply all configurations from the current assembly
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // Apply global query filters for multi-tenancy
            ApplyGlobalFilters(modelBuilder);

            base.OnModelCreating(modelBuilder);
        }

        private void ApplyGlobalFilters(ModelBuilder modelBuilder)
        {
            // Apply organization filter to all entities that inherit from BaseEntity
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
                {
                    var parameter = Expression.Parameter(entityType.ClrType, "e");
                    var organizationProperty = Expression.Property(parameter, nameof(BaseEntity.OrganizationId));
                    var isDeletedProperty = Expression.Property(parameter, nameof(BaseEntity.IsDeleted));

                    // Filter by organization and exclude soft-deleted entities
                    var organizationFilter = Expression.Equal(
                        organizationProperty,
                        Expression.Constant(_currentUserService.OrganizationId ?? Guid.Empty));

                    var notDeletedFilter = Expression.Equal(
                        isDeletedProperty,
                        Expression.Constant(false));

                    var combinedFilter = Expression.AndAlso(organizationFilter, notDeletedFilter);
                    var lambda = Expression.Lambda(combinedFilter, parameter);

                    modelBuilder.Entity(entityType.ClrType).HasQueryFilter(lambda);
                }
            }
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // Update audit fields before saving
            foreach (var entry in ChangeTracker.Entries<BaseEntity>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        if (entry.Entity.OrganizationId == Guid.Empty && _currentUserService.OrganizationId.HasValue)
                        {
                            entry.Property(nameof(BaseEntity.OrganizationId)).CurrentValue = _currentUserService.OrganizationId.Value;
                        }
                        entry.Property(nameof(BaseEntity.CreatedAt)).CurrentValue = DateTime.UtcNow;
                        entry.Property(nameof(BaseEntity.CreatedBy)).CurrentValue = _currentUserService.Username ?? "System";
                        break;

                    case EntityState.Modified:
                        entry.Property(nameof(BaseEntity.UpdatedAt)).CurrentValue = DateTime.UtcNow;
                        entry.Property(nameof(BaseEntity.UpdatedBy)).CurrentValue = _currentUserService.Username ?? "System";
                        break;
                }
            }

            var result = await base.SaveChangesAsync(cancellationToken);

            // Publish domain events after successful save
            await PublishDomainEventsAsync();

            return result;
        }

        private async Task PublishDomainEventsAsync()
        {
            var domainEntities = ChangeTracker
                .Entries<BaseEntity>()
                .Where(x => x.Entity.DomainEvents.Any())
                .ToList();

            var domainEvents = domainEntities
                .SelectMany(x => x.Entity.DomainEvents)
                .ToList();

            domainEntities.ForEach(entity => entity.Entity.ClearDomainEvents());

            // TODO: Publish domain events through domain event publisher
            // This will be implemented when we add the domain event publisher service
        }
    }
}
