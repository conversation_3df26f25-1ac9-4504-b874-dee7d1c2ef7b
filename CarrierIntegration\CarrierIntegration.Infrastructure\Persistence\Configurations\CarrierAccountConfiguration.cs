using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CarrierIntegration.Infrastructure.Persistence.Configurations
{
    public class CarrierAccountConfiguration : IEntityTypeConfiguration<CarrierAccount>
    {
        public void Configure(EntityTypeBuilder<CarrierAccount> builder)
        {
            builder.ToTable("CarrierAccounts");

            builder.HasKey(ca => ca.Id);

            builder.Property(ca => ca.Id)
                .ValueGeneratedNever();

            builder.Property(ca => ca.CarrierId)
                .IsRequired();

            builder.Property(ca => ca.AccountNumber)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(ca => ca.AccountName)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(ca => ca.Description)
                .HasMaxLength(1000);

            builder.Property(ca => ca.Username)
                .HasMaxLength(100);

            builder.Property(ca => ca.PasswordHash)
                .HasMaxLength(500);

            builder.Property(ca => ca.ApiKey)
                .HasMaxLength(500);

            builder.Property(ca => ca.ApiSecret)
                .HasMaxLength(500);

            builder.Property(ca => ca.AccessToken)
                .HasMaxLength(1000);

            builder.Property(ca => ca.RefreshToken)
                .HasMaxLength(1000);

            builder.Property(ca => ca.AuthenticationMethod)
                .HasMaxLength(50);

            builder.Property(ca => ca.AuthenticationUrl)
                .HasMaxLength(500);

            builder.Property(ca => ca.CustomerNumber)
                .HasMaxLength(100);

            builder.Property(ca => ca.MeterNumber)
                .HasMaxLength(100);

            builder.Property(ca => ca.LicenseKey)
                .HasMaxLength(500);

            builder.Property(ca => ca.DeveloperKey)
                .HasMaxLength(500);

            builder.Property(ca => ca.Environment)
                .HasMaxLength(50);

            builder.Property(ca => ca.BaseUrl)
                .HasMaxLength(500);

            builder.Property(ca => ca.TestBaseUrl)
                .HasMaxLength(500);

            builder.Property(ca => ca.CreditCurrency)
                .HasConversion<int?>();

            builder.Property(ca => ca.PaymentTerms)
                .HasMaxLength(200);

            builder.Property(ca => ca.BillingCycle)
                .HasMaxLength(50);

            builder.Property(ca => ca.PaymentMethod)
                .HasMaxLength(100);

            builder.Property(ca => ca.ContractNumber)
                .HasMaxLength(100);

            builder.Property(ca => ca.ServiceLevel)
                .HasMaxLength(100);

            builder.Property(ca => ca.DiscountCode)
                .HasMaxLength(50);

            builder.Property(ca => ca.SpecialRates)
                .HasMaxLength(1000);

            builder.Property(ca => ca.RestrictedServices)
                .HasMaxLength(1000);

            builder.Property(ca => ca.AllowedServices)
                .HasMaxLength(1000);

            builder.Property(ca => ca.MaxShipmentValueCurrency)
                .HasConversion<int?>();

            builder.Property(ca => ca.TimeZone)
                .HasMaxLength(100);

            builder.Property(ca => ca.PreferredLanguage)
                .HasMaxLength(50);

            builder.Property(ca => ca.NotificationPreferences)
                .HasMaxLength(1000);

            builder.Property(ca => ca.ReportingPreferences)
                .HasMaxLength(1000);

            builder.Property(ca => ca.Notes)
                .HasMaxLength(2000);

            builder.Property(ca => ca.InternalReference)
                .HasMaxLength(100);

            builder.Property(ca => ca.ExternalReference)
                .HasMaxLength(100);

            builder.Property(ca => ca.Tags)
                .HasMaxLength(500);

            builder.Property(ca => ca.DeactivationReason)
                .HasMaxLength(500);

            builder.Property(ca => ca.OrganizationId)
                .IsRequired();

            builder.Property(ca => ca.CreatedAt)
                .IsRequired();

            builder.Property(ca => ca.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(ca => ca.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(ca => ca.DeletedBy)
                .HasMaxLength(100);

            // Configure value objects
            builder.OwnsOne(ca => ca.BillingContact, contact =>
            {
                contact.Property(x => x.Name)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("BillingContactName");

                contact.Property(x => x.Email)
                    .HasMaxLength(200)
                    .HasColumnName("BillingContactEmail");

                contact.Property(x => x.Phone)
                    .HasMaxLength(50)
                    .HasColumnName("BillingContactPhone");

                contact.Property(x => x.Title)
                    .HasMaxLength(100)
                    .HasColumnName("BillingContactTitle");

                contact.Property(x => x.Department)
                    .HasMaxLength(100)
                    .HasColumnName("BillingContactDepartment");
            });

            builder.OwnsOne(ca => ca.BillingAddress, address =>
            {
                address.Property(x => x.Street1)
                    .IsRequired()
                    .HasMaxLength(200)
                    .HasColumnName("BillingAddressStreet1");

                address.Property(x => x.Street2)
                    .HasMaxLength(200)
                    .HasColumnName("BillingAddressStreet2");

                address.Property(x => x.City)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("BillingAddressCity");

                address.Property(x => x.State)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("BillingAddressState");

                address.Property(x => x.PostalCode)
                    .IsRequired()
                    .HasMaxLength(20)
                    .HasColumnName("BillingAddressPostalCode");

                address.Property(x => x.Country)
                    .IsRequired()
                    .HasMaxLength(100)
                    .HasColumnName("BillingAddressCountry");

                address.Property(x => x.Latitude)
                    .HasPrecision(10, 7)
                    .HasColumnName("BillingAddressLatitude");

                address.Property(x => x.Longitude)
                    .HasPrecision(10, 7)
                    .HasColumnName("BillingAddressLongitude");
            });

            builder.OwnsOne(ca => ca.TechnicalContact, contact =>
            {
                contact.Property(x => x.Name)
                    .HasMaxLength(200)
                    .HasColumnName("TechnicalContactName");

                contact.Property(x => x.Email)
                    .HasMaxLength(200)
                    .HasColumnName("TechnicalContactEmail");

                contact.Property(x => x.Phone)
                    .HasMaxLength(50)
                    .HasColumnName("TechnicalContactPhone");

                contact.Property(x => x.Title)
                    .HasMaxLength(100)
                    .HasColumnName("TechnicalContactTitle");

                contact.Property(x => x.Department)
                    .HasMaxLength(100)
                    .HasColumnName("TechnicalContactDepartment");
            });

            // Configure indexes
            builder.HasIndex(ca => new { ca.CarrierId, ca.AccountNumber })
                .IsUnique()
                .HasDatabaseName("IX_CarrierAccounts_CarrierId_AccountNumber");

            builder.HasIndex(ca => ca.OrganizationId)
                .HasDatabaseName("IX_CarrierAccounts_OrganizationId");

            builder.HasIndex(ca => ca.IsActive)
                .HasDatabaseName("IX_CarrierAccounts_IsActive");

            builder.HasIndex(ca => ca.IsDefault)
                .HasDatabaseName("IX_CarrierAccounts_IsDefault");

            builder.HasIndex(ca => ca.CreatedAt)
                .HasDatabaseName("IX_CarrierAccounts_CreatedAt");

            // Ignore domain events for EF Core
            builder.Ignore(ca => ca.DomainEvents);
        }
    }
}
