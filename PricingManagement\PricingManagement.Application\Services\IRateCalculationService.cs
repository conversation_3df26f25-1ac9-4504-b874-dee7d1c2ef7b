using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.ValueObjects;

namespace PricingManagement.Application.Services
{
    public interface IRateCalculationService
    {
        Task<Money> CalculateBaseRateAsync(RateCalculationRequestDto request, IEnumerable<PricingRule> applicableRules);
        Task<Money> CalculateRateByRuleAsync(RateCalculationRequestDto request, PricingRule rule);
        Task<Money> CalculateWeightBasedRateAsync(RateCalculationRequestDto request, PricingRule rule);
        Task<Money> CalculateDistanceBasedRateAsync(RateCalculationRequestDto request, PricingRule rule);
        Task<Money> CalculateZoneBasedRateAsync(RateCalculationRequestDto request, PricingRule rule);
        Task<Money> CalculateDimensionalWeightRateAsync(RateCalculationRequestDto request, PricingRule rule);
        Task<Money> CalculateServiceLevelRateAsync(RateCalculationRequestDto request, PricingRule rule);
        Task<Money> ApplyTieredPricingAsync(decimal value, IEnumerable<PricingRuleTier> tiers, Money baseAmount);
    }
}
