using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Common.Interfaces;
using UserManagement.Application.EventHandlers;

namespace UserManagement.Infrastructure.Services
{
    public class EventSubscriptionService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IMessageBroker _messageBroker;
        private readonly ILogger<EventSubscriptionService> _logger;

        public EventSubscriptionService(
            IServiceProvider serviceProvider,
            IMessageBroker messageBroker,
            ILogger<EventSubscriptionService> logger)
        {
            _serviceProvider = serviceProvider;
            _messageBroker = messageBroker;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _logger.LogInformation("Starting event subscription service");

                // Subscribe to user registration events from Identity service
                await _messageBroker.SubscribeAsync<UserRegisteredEvent>("user.registered", async (eventData) =>
                {
                    using var scope = _serviceProvider.CreateScope();
                    var handler = scope.ServiceProvider.GetRequiredService<UserRegisteredEventHandler>();
                    await handler.Handle(eventData);
                });

                _logger.LogInformation("Event subscriptions established successfully");

                // Keep the service running
                while (!stoppingToken.IsCancellationRequested)
                {
                    await Task.Delay(1000, stoppingToken);
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in event subscription service");
                throw;
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping event subscription service");
            await base.StopAsync(cancellationToken);
        }
    }
}
