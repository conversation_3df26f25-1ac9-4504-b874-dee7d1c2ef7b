using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Features.Analytics.Queries
{
    public class GetCarrierDashboardQuery : IRequest<ApiResponseDto<CarrierDashboardDto>>
    {
        public Guid OrganizationId { get; set; }
        public string DashboardType { get; set; } = "Executive";
        public DateTime? AsOfDate { get; set; }
        public bool IncludeAlerts { get; set; } = true;
        public bool IncludeTrends { get; set; } = true;
        public bool IncludeForecasts { get; set; } = false;
        public string? TimeZone { get; set; }
    }

    public class GetCarrierDashboardQueryHandler : IRequestHandler<GetCarrierDashboardQuery, ApiResponseDto<CarrierDashboardDto>>
    {
        private readonly ICarrierDashboardService _dashboardService;
        private readonly ICachingService _cachingService;
        private readonly ILogger<GetCarrierDashboardQueryHandler> _logger;

        public GetCarrierDashboardQueryHandler(
            ICarrierDashboardService dashboardService,
            ICachingService cachingService,
            ILogger<GetCarrierDashboardQueryHandler> logger)
        {
            _dashboardService = dashboardService;
            _cachingService = cachingService;
            _logger = logger;
        }

        public async Task<ApiResponseDto<CarrierDashboardDto>> Handle(GetCarrierDashboardQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Generating {DashboardType} dashboard for organization {OrganizationId}",
                    request.DashboardType, request.OrganizationId);

                var dashboard = await _dashboardService.GenerateDashboardAsync(request);

                _logger.LogInformation("Successfully generated {DashboardType} dashboard with {WidgetCount} widgets",
                    request.DashboardType, dashboard.Widgets?.Count ?? 0);

                return ApiResponseDto<CarrierDashboardDto>.SuccessResult(dashboard);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating dashboard for organization {OrganizationId}", request.OrganizationId);
                return ApiResponseDto<CarrierDashboardDto>.ErrorResult($"Failed to generate dashboard: {ex.Message}");
            }
        }
    }
}
