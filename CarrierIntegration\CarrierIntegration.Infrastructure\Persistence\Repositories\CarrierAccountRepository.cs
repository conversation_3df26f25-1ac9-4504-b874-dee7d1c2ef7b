using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Persistence.Repositories
{
    public class CarrierAccountRepository : BaseRepository<CarrierAccount>, ICarrierAccountRepository
    {
        public CarrierAccountRepository(CarrierIntegrationDbContext context) : base(context)
        {
        }

        public async Task<List<CarrierAccount>> GetByCarrierIdAsync(Guid carrierId)
        {
            return await _context.CarrierAccounts
                .Where(ca => ca.CarrierId == carrierId)
                .OrderBy(ca => ca.AccountName)
                .ToListAsync();
        }

        public async Task<List<CarrierAccount>> GetByOrganizationIdAsync(Guid organizationId)
        {
            return await _context.CarrierAccounts
                .Include(ca => ca.Carrier)
                .Where(ca => ca.OrganizationId == organizationId)
                .OrderBy(ca => ca.Carrier.Name)
                .ThenBy(ca => ca.AccountName)
                .ToListAsync();
        }

        public async Task<List<CarrierAccount>> GetActiveAccountsAsync(Guid organizationId)
        {
            return await _context.CarrierAccounts
                .Include(ca => ca.Carrier)
                .Where(ca => ca.OrganizationId == organizationId && ca.IsActive)
                .OrderBy(ca => ca.Carrier.Name)
                .ThenBy(ca => ca.AccountName)
                .ToListAsync();
        }

        public async Task<CarrierAccount?> GetByAccountNumberAsync(string accountNumber, Guid carrierId)
        {
            return await _context.CarrierAccounts
                .Include(ca => ca.Carrier)
                .FirstOrDefaultAsync(ca => ca.AccountNumber == accountNumber && ca.CarrierId == carrierId);
        }

        public async Task<bool> AccountNumberExistsAsync(string accountNumber, Guid carrierId, Guid? excludeId = null)
        {
            var query = _context.CarrierAccounts
                .Where(ca => ca.AccountNumber == accountNumber && ca.CarrierId == carrierId);

            if (excludeId.HasValue)
                query = query.Where(ca => ca.Id != excludeId.Value);

            return await query.AnyAsync();
        }
    }
}
