using MediatR;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Enums;
using System;
using System.Collections.Generic;

namespace PricingManagement.Application.PricingRules.Commands.CreatePricingRule
{
    public class CreatePricingRuleCommand : IRequest<OperationResultDto<PricingRuleDto>>
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public PricingRuleType RuleType { get; set; }
        public CalculationMethod CalculationMethod { get; set; }
        public int Priority { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public List<string> ServiceTypes { get; set; } = new();
        public List<string> OriginZones { get; set; } = new();
        public List<string> DestinationZones { get; set; } = new();
        public List<string> CustomerSegments { get; set; } = new();
        public List<string> ShipperTypes { get; set; } = new();
        public decimal? MinWeight { get; set; }
        public decimal? MaxWeight { get; set; }
        public decimal? MinDistance { get; set; }
        public decimal? MaxDistance { get; set; }
        public decimal? MinValue { get; set; }
        public decimal? MaxValue { get; set; }
        public Dictionary<string, object> RuleConfiguration { get; set; } = new();
        public bool RequiresApproval { get; set; }
        public string? ApprovalWorkflow { get; set; }
        public List<string> Tags { get; set; } = new();
        public List<PricingRuleTierDto> Tiers { get; set; } = new();
    }
}
