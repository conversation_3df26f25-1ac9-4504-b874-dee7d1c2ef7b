using System;
using MasterManagement.Domain.Common;
using MasterManagement.Domain.Exceptions;
using MasterManagement.Domain.Events.Geographic;
using MasterManagement.Domain.ValueObjects;

namespace MasterManagement.Domain.Entities.Geographic
{
    public class PostalCode : AggregateRoot
    {
        public string Code { get; private set; }
        public string? SecondaryCode { get; private set; }
        public Guid CityId { get; private set; }
        public Guid StateId { get; private set; }
        public Guid CountryId { get; private set; }
        public Guid? ZoneId { get; private set; }
        public Guid? OrganizationId { get; private set; } // Null for shared postal codes
        public bool IsActive { get; private set; }
        public Coordinates? Coordinates { get; private set; }
        public string? AreaName { get; private set; }
        public string PostalCodeType { get; private set; } // Standard, PO Box, Military, etc.

        // Navigation properties
        public City City { get; private set; }
        public State State { get; private set; }
        public Country Country { get; private set; }
        public Zone Zone { get; private set; }

        private PostalCode() { }

        public PostalCode(
            string code,
            Guid cityId,
            Guid stateId,
            Guid countryId,
            Guid? zoneId = null,
            Guid? organizationId = null,
            string? secondaryCode = null,
            string? areaName = null,
            string postalCodeType = "Standard",
            Coordinates? coordinates = null)
        {
            if (string.IsNullOrWhiteSpace(code))
                throw new DomainException("Postal code cannot be empty");

            if (cityId == Guid.Empty)
                throw new DomainException("City ID cannot be empty");

            if (stateId == Guid.Empty)
                throw new DomainException("State ID cannot be empty");

            if (countryId == Guid.Empty)
                throw new DomainException("Country ID cannot be empty");

            Code = code.ToUpperInvariant();
            SecondaryCode = secondaryCode?.ToUpperInvariant();
            CityId = cityId;
            StateId = stateId;
            CountryId = countryId;
            ZoneId = zoneId;
            OrganizationId = organizationId;
            IsActive = true;
            AreaName = areaName;
            PostalCodeType = postalCodeType;
            Coordinates = coordinates;

            AddDomainEvent(new PostalCodeCreatedEvent(Id, Code, CityId, OrganizationId));
        }

        public void UpdateDetails(
            string? secondaryCode,
            string? areaName,
            string postalCodeType,
            Coordinates? coordinates)
        {
            SecondaryCode = secondaryCode?.ToUpperInvariant();
            AreaName = areaName ?? AreaName;
            PostalCodeType = postalCodeType ?? PostalCodeType;
            Coordinates = coordinates ?? Coordinates;
            UpdatedAt = DateTime.UtcNow;

            AddDomainEvent(new PostalCodeUpdatedEvent(Id, Code, OrganizationId));
        }

        public void AssignToZone(Guid zoneId)
        {
            ZoneId = zoneId;
            UpdatedAt = DateTime.UtcNow;
        }

        public void AssignToOrganization(Guid organizationId)
        {
            OrganizationId = organizationId;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Activate()
        {
            IsActive = true;
            UpdatedAt = DateTime.UtcNow;
        }

        public void Deactivate()
        {
            IsActive = false;
            UpdatedAt = DateTime.UtcNow;
        }
    }
}
