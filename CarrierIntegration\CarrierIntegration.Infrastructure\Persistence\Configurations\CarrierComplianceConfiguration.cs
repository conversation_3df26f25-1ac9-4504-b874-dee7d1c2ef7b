using CarrierIntegration.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CarrierIntegration.Infrastructure.Persistence.Configurations
{
    public class CarrierComplianceConfiguration : IEntityTypeConfiguration<CarrierCompliance>
    {
        public void Configure(EntityTypeBuilder<CarrierCompliance> builder)
        {
            builder.ToTable("CarrierCompliances");

            builder.<PERSON><PERSON>ey(cc => cc.Id);

            builder.Property(cc => cc.Id)
                .ValueGeneratedNever();

            builder.Property(cc => cc.CarrierId)
                .IsRequired();

            builder.Property(cc => cc.ComplianceType)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cc => cc.ComplianceName)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(cc => cc.ComplianceNumber)
                .HasMaxLength(100);

            builder.Property(cc => cc.IssuingAuthority)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(cc => cc.IssueDate)
                .IsRequired();

            builder.Property(cc => cc.ExpirationDate)
                .IsRequired();

            builder.Property(cc => cc.Status)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(cc => cc.DocumentUrl)
                .HasMaxLength(500);

            builder.Property(cc => cc.DocumentPath)
                .HasMaxLength(500);

            builder.Property(cc => cc.Notes)
                .HasMaxLength(2000);

            builder.Property(cc => cc.LastVerificationBy)
                .HasMaxLength(100);

            builder.Property(cc => cc.VerificationMethod)
                .HasMaxLength(200);

            builder.Property(cc => cc.RenewalProcess)
                .HasMaxLength(1000);

            builder.Property(cc => cc.ContactPerson)
                .HasMaxLength(200);

            builder.Property(cc => cc.ContactEmail)
                .HasMaxLength(200);

            builder.Property(cc => cc.ContactPhone)
                .HasMaxLength(50);

            builder.Property(cc => cc.CoverageAmount)
                .HasPrecision(18, 2);

            builder.Property(cc => cc.CoverageCurrency)
                .HasMaxLength(10);

            builder.Property(cc => cc.PolicyDetails)
                .HasMaxLength(2000);

            builder.Property(cc => cc.Restrictions)
                .HasMaxLength(1000);

            builder.Property(cc => cc.GeographicScope)
                .HasMaxLength(500);

            builder.Property(cc => cc.ServiceScope)
                .HasMaxLength(500);

            builder.Property(cc => cc.OrganizationId)
                .IsRequired();

            builder.Property(cc => cc.CreatedAt)
                .IsRequired();

            builder.Property(cc => cc.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cc => cc.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(cc => cc.DeletedBy)
                .HasMaxLength(100);

            // Configure indexes
            builder.HasIndex(cc => new { cc.CarrierId, cc.ComplianceType, cc.ComplianceName })
                .IsUnique()
                .HasDatabaseName("IX_CarrierCompliances_Carrier_Type_Name");

            builder.HasIndex(cc => cc.OrganizationId)
                .HasDatabaseName("IX_CarrierCompliances_OrganizationId");

            builder.HasIndex(cc => cc.ComplianceType)
                .HasDatabaseName("IX_CarrierCompliances_ComplianceType");

            builder.HasIndex(cc => cc.Status)
                .HasDatabaseName("IX_CarrierCompliances_Status");

            builder.HasIndex(cc => cc.ExpirationDate)
                .HasDatabaseName("IX_CarrierCompliances_ExpirationDate");

            builder.HasIndex(cc => cc.IsRequired)
                .HasDatabaseName("IX_CarrierCompliances_IsRequired");

            builder.HasIndex(cc => cc.IsActive)
                .HasDatabaseName("IX_CarrierCompliances_IsActive");

            builder.HasIndex(cc => cc.NextVerificationDate)
                .HasDatabaseName("IX_CarrierCompliances_NextVerificationDate");

            builder.HasIndex(cc => cc.CreatedAt)
                .HasDatabaseName("IX_CarrierCompliances_CreatedAt");

            // Ignore domain events for EF Core
            builder.Ignore(cc => cc.DomainEvents);
        }
    }
}
