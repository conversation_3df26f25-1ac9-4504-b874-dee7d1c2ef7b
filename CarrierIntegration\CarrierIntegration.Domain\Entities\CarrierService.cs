using CarrierIntegration.Domain.Common;
using CarrierIntegration.Domain.Enums;
using CarrierIntegration.Domain.ValueObjects;
using System;
using System.Collections.Generic;

namespace CarrierIntegration.Domain.Entities
{
    public class CarrierService : BaseEntity
    {
        public Guid CarrierId { get; private set; }
        public string ServiceCode { get; private set; }
        public string ServiceName { get; private set; }
        public string? Description { get; private set; }
        public ServiceLevel ServiceLevel { get; private set; }
        public bool IsActive { get; private set; }
        public int TransitDays { get; private set; }
        public int? TransitDaysMax { get; private set; }
        public bool IsGuaranteed { get; private set; }
        public TimeSpan? CutoffTime { get; private set; }
        public bool RequiresSignature { get; private set; }
        public bool SupportsInsurance { get; private set; }
        public bool SupportsCOD { get; private set; }
        public bool SupportsHazmat { get; private set; }
        public bool SupportsInternational { get; private set; }
        public bool SupportsResidential { get; private set; }
        public bool SupportsCommercial { get; private set; }
        public bool SupportsPickup { get; private set; }
        public bool SupportsDropoff { get; private set; }
        public bool SupportsSaturdayDelivery { get; private set; }
        public bool SupportsSundayDelivery { get; private set; }
        public bool SupportsHolidayDelivery { get; private set; }
        public decimal? MinWeight { get; private set; }
        public string? MinWeightUnit { get; private set; }
        public decimal? MaxWeight { get; private set; }
        public string? MaxWeightUnit { get; private set; }
        public decimal? MaxLength { get; private set; }
        public decimal? MaxWidth { get; private set; }
        public decimal? MaxHeight { get; private set; }
        public string? MaxDimensionsUnit { get; private set; }
        public decimal? MaxDeclaredValue { get; private set; }
        public Currency? MaxDeclaredValueCurrency { get; private set; }
        public string? ServiceArea { get; private set; }
        public string? RestrictedAreas { get; private set; }
        public string? SpecialInstructions { get; private set; }
        public Money? BaseRate { get; private set; }
        public string? RateStructure { get; private set; }
        public string? FuelSurchargeStructure { get; private set; }
        public string? AccessorialCharges { get; private set; }
        public int Priority { get; private set; }
        public string? ApiServiceCode { get; private set; }
        public string? TrackingUrlTemplate { get; private set; }
        public string? LabelFormat { get; private set; }
        public bool RequiresAccount { get; private set; }
        public string? AccountRequirements { get; private set; }
        public string? ComplianceRequirements { get; private set; }
        public string? DocumentationRequirements { get; private set; }
        public DateTime? EffectiveDate { get; private set; }
        public DateTime? ExpirationDate { get; private set; }
        public string? Terms { get; private set; }
        public string? Conditions { get; private set; }

        private readonly List<string> _supportedCountries = new();
        public IReadOnlyCollection<string> SupportedCountries => _supportedCountries.AsReadOnly();

        private readonly List<string> _restrictedCountries = new();
        public IReadOnlyCollection<string> RestrictedCountries => _restrictedCountries.AsReadOnly();

        private readonly List<string> _supportedPostalCodes = new();
        public IReadOnlyCollection<string> SupportedPostalCodes => _supportedPostalCodes.AsReadOnly();

        private readonly List<string> _restrictedPostalCodes = new();
        public IReadOnlyCollection<string> RestrictedPostalCodes => _restrictedPostalCodes.AsReadOnly();

        private CarrierService() { } // For EF Core

        public CarrierService(
            Guid carrierId,
            Guid organizationId,
            string serviceCode,
            string serviceName,
            ServiceLevel serviceLevel,
            int transitDays,
            string createdBy = "System") : base(organizationId, createdBy)
        {
            if (carrierId == Guid.Empty)
                throw new DomainException("Carrier ID is required");
            if (string.IsNullOrWhiteSpace(serviceCode))
                throw new DomainException("Service code is required");
            if (string.IsNullOrWhiteSpace(serviceName))
                throw new DomainException("Service name is required");
            if (transitDays < 0)
                throw new DomainException("Transit days cannot be negative");

            CarrierId = carrierId;
            ServiceCode = serviceCode.Trim().ToUpperInvariant();
            ServiceName = serviceName.Trim();
            ServiceLevel = serviceLevel;
            TransitDays = transitDays;
            IsActive = true;
            IsGuaranteed = false;
            RequiresSignature = false;
            SupportsInsurance = false;
            SupportsCOD = false;
            SupportsHazmat = false;
            SupportsInternational = false;
            SupportsResidential = true;
            SupportsCommercial = true;
            SupportsPickup = false;
            SupportsDropoff = true;
            SupportsSaturdayDelivery = false;
            SupportsSundayDelivery = false;
            SupportsHolidayDelivery = false;
            Priority = 100;
            RequiresAccount = false;
        }

        public void UpdateBasicInfo(
            string serviceName,
            string? description,
            ServiceLevel serviceLevel,
            int transitDays,
            int? transitDaysMax,
            bool isGuaranteed,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(serviceName))
                throw new DomainException("Service name is required");
            if (transitDays < 0)
                throw new DomainException("Transit days cannot be negative");
            if (transitDaysMax.HasValue && transitDaysMax.Value < transitDays)
                throw new DomainException("Maximum transit days cannot be less than minimum transit days");

            ServiceName = serviceName.Trim();
            Description = description?.Trim();
            ServiceLevel = serviceLevel;
            TransitDays = transitDays;
            TransitDaysMax = transitDaysMax;
            IsGuaranteed = isGuaranteed;

            Update(updatedBy);
        }

        public void UpdateCapabilities(
            bool requiresSignature,
            bool supportsInsurance,
            bool supportsCOD,
            bool supportsHazmat,
            bool supportsInternational,
            bool supportsResidential,
            bool supportsCommercial,
            bool supportsPickup,
            bool supportsDropoff,
            string updatedBy)
        {
            RequiresSignature = requiresSignature;
            SupportsInsurance = supportsInsurance;
            SupportsCOD = supportsCOD;
            SupportsHazmat = supportsHazmat;
            SupportsInternational = supportsInternational;
            SupportsResidential = supportsResidential;
            SupportsCommercial = supportsCommercial;
            SupportsPickup = supportsPickup;
            SupportsDropoff = supportsDropoff;

            Update(updatedBy);
        }

        public void UpdateScheduleCapabilities(
            bool supportsSaturdayDelivery,
            bool supportsSundayDelivery,
            bool supportsHolidayDelivery,
            TimeSpan? cutoffTime,
            string updatedBy)
        {
            SupportsSaturdayDelivery = supportsSaturdayDelivery;
            SupportsSundayDelivery = supportsSundayDelivery;
            SupportsHolidayDelivery = supportsHolidayDelivery;
            CutoffTime = cutoffTime;

            Update(updatedBy);
        }

        public void UpdateLimitations(
            decimal? minWeight,
            string? minWeightUnit,
            decimal? maxWeight,
            string? maxWeightUnit,
            decimal? maxLength,
            decimal? maxWidth,
            decimal? maxHeight,
            string? maxDimensionsUnit,
            decimal? maxDeclaredValue,
            Currency? maxDeclaredValueCurrency,
            string updatedBy)
        {
            if (minWeight.HasValue && minWeight.Value < 0)
                throw new DomainException("Minimum weight cannot be negative");
            if (maxWeight.HasValue && maxWeight.Value < 0)
                throw new DomainException("Maximum weight cannot be negative");
            if (minWeight.HasValue && maxWeight.HasValue && minWeight.Value > maxWeight.Value)
                throw new DomainException("Minimum weight cannot be greater than maximum weight");

            MinWeight = minWeight;
            MinWeightUnit = minWeightUnit?.Trim().ToUpperInvariant();
            MaxWeight = maxWeight;
            MaxWeightUnit = maxWeightUnit?.Trim().ToUpperInvariant();
            MaxLength = maxLength;
            MaxWidth = maxWidth;
            MaxHeight = maxHeight;
            MaxDimensionsUnit = maxDimensionsUnit?.Trim().ToUpperInvariant();
            MaxDeclaredValue = maxDeclaredValue;
            MaxDeclaredValueCurrency = maxDeclaredValueCurrency;

            Update(updatedBy);
        }

        public void UpdateServiceArea(
            string? serviceArea,
            string? restrictedAreas,
            string updatedBy)
        {
            ServiceArea = serviceArea?.Trim();
            RestrictedAreas = restrictedAreas?.Trim();

            Update(updatedBy);
        }

        public void UpdatePricing(
            Money? baseRate,
            string? rateStructure,
            string? fuelSurchargeStructure,
            string? accessorialCharges,
            string updatedBy)
        {
            BaseRate = baseRate;
            RateStructure = rateStructure?.Trim();
            FuelSurchargeStructure = fuelSurchargeStructure?.Trim();
            AccessorialCharges = accessorialCharges?.Trim();

            Update(updatedBy);
        }

        public void UpdateTechnicalInfo(
            string? apiServiceCode,
            string? trackingUrlTemplate,
            string? labelFormat,
            string updatedBy)
        {
            ApiServiceCode = apiServiceCode?.Trim();
            TrackingUrlTemplate = trackingUrlTemplate?.Trim();
            LabelFormat = labelFormat?.Trim();

            Update(updatedBy);
        }

        public void UpdateRequirements(
            bool requiresAccount,
            string? accountRequirements,
            string? complianceRequirements,
            string? documentationRequirements,
            string updatedBy)
        {
            RequiresAccount = requiresAccount;
            AccountRequirements = accountRequirements?.Trim();
            ComplianceRequirements = complianceRequirements?.Trim();
            DocumentationRequirements = documentationRequirements?.Trim();

            Update(updatedBy);
        }

        public void UpdateTerms(
            DateTime? effectiveDate,
            DateTime? expirationDate,
            string? terms,
            string? conditions,
            string updatedBy)
        {
            if (effectiveDate.HasValue && expirationDate.HasValue && effectiveDate.Value >= expirationDate.Value)
                throw new DomainException("Effective date must be before expiration date");

            EffectiveDate = effectiveDate;
            ExpirationDate = expirationDate;
            Terms = terms?.Trim();
            Conditions = conditions?.Trim();

            Update(updatedBy);
        }

        public void SetPriority(int priority, string updatedBy)
        {
            if (priority < 1 || priority > 1000)
                throw new DomainException("Priority must be between 1 and 1000");

            Priority = priority;
            Update(updatedBy);
        }

        public void Activate(string updatedBy)
        {
            IsActive = true;
            Update(updatedBy);
        }

        public void Deactivate(string updatedBy)
        {
            IsActive = false;
            Update(updatedBy);
        }

        public void AddSupportedCountry(string countryCode)
        {
            if (string.IsNullOrWhiteSpace(countryCode))
                throw new DomainException("Country code is required");

            var code = countryCode.Trim().ToUpperInvariant();
            if (!_supportedCountries.Contains(code))
            {
                _supportedCountries.Add(code);
            }
        }

        public void RemoveSupportedCountry(string countryCode)
        {
            if (!string.IsNullOrWhiteSpace(countryCode))
            {
                _supportedCountries.Remove(countryCode.Trim().ToUpperInvariant());
            }
        }

        public void AddRestrictedCountry(string countryCode)
        {
            if (string.IsNullOrWhiteSpace(countryCode))
                throw new DomainException("Country code is required");

            var code = countryCode.Trim().ToUpperInvariant();
            if (!_restrictedCountries.Contains(code))
            {
                _restrictedCountries.Add(code);
            }
        }

        public void RemoveRestrictedCountry(string countryCode)
        {
            if (!string.IsNullOrWhiteSpace(countryCode))
            {
                _restrictedCountries.Remove(countryCode.Trim().ToUpperInvariant());
            }
        }

        public bool CanHandleWeight(Weight weight)
        {
            if (MinWeight.HasValue && !string.IsNullOrWhiteSpace(MinWeightUnit))
            {
                var minWeight = new Weight(MinWeight.Value, MinWeightUnit);
                if (weight.IsLessThan(minWeight))
                    return false;
            }

            if (MaxWeight.HasValue && !string.IsNullOrWhiteSpace(MaxWeightUnit))
            {
                var maxWeight = new Weight(MaxWeight.Value, MaxWeightUnit);
                if (weight.IsGreaterThan(maxWeight))
                    return false;
            }

            return true;
        }

        public bool CanHandleDimensions(Dimensions dimensions)
        {
            if (string.IsNullOrWhiteSpace(MaxDimensionsUnit))
                return true;

            if (MaxLength.HasValue)
            {
                var maxDimensions = new Dimensions(MaxLength.Value, MaxWidth ?? MaxLength.Value, MaxHeight ?? MaxLength.Value, MaxDimensionsUnit);
                if (dimensions.GetLongestSide() > maxDimensions.GetLongestSide())
                    return false;
            }

            return true;
        }

        public bool IsAvailableForCountry(string countryCode)
        {
            if (string.IsNullOrWhiteSpace(countryCode))
                return false;

            var code = countryCode.Trim().ToUpperInvariant();

            // If restricted countries are defined and this country is in the list, not available
            if (_restrictedCountries.Count > 0 && _restrictedCountries.Contains(code))
                return false;

            // If supported countries are defined, only available if in the list
            if (_supportedCountries.Count > 0)
                return _supportedCountries.Contains(code);

            // If no restrictions and no specific support list, available for all
            return true;
        }

        public bool IsCurrentlyValid()
        {
            var now = DateTime.UtcNow;

            if (EffectiveDate.HasValue && now < EffectiveDate.Value)
                return false;

            if (ExpirationDate.HasValue && now > ExpirationDate.Value)
                return false;

            return IsActive;
        }

        public bool IsCutoffTimeMet(DateTime requestTime)
        {
            if (!CutoffTime.HasValue)
                return true;

            return requestTime.TimeOfDay <= CutoffTime.Value;
        }
    }
}
