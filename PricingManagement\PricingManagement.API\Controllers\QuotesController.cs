using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Services;
using PricingManagement.Application.Quotes.Commands.CreateQuote;
using PricingManagement.Application.Quotes.Commands.SendQuote;
using PricingManagement.Application.Quotes.Commands.ConvertQuoteToOrder;
using PricingManagement.Application.Quotes.Queries.GetQuotes;
using PricingManagement.Application.Quotes.Queries.GetQuoteById;

namespace PricingManagement.API.Controllers
{
    /// <summary>
    /// Quote management endpoints
    /// </summary>
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [Authorize]
    public class QuotesController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly IPricingEngine _pricingEngine;
        private readonly ILogger<QuotesController> _logger;

        public QuotesController(
            IMediator mediator,
            IPricingEngine pricingEngine,
            ILogger<QuotesController> logger)
        {
            _mediator = mediator;
            _pricingEngine = pricingEngine;
            _logger = logger;
        }

        /// <summary>
        /// Get all quotes with optional filtering and pagination
        /// </summary>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 10)</param>
        /// <param name="status">Filter by quote status</param>
        /// <param name="customerId">Filter by customer ID</param>
        /// <param name="shipperId">Filter by shipper ID</param>
        /// <param name="fromDate">Filter quotes from date</param>
        /// <param name="toDate">Filter quotes to date</param>
        /// <returns>Paginated list of quotes</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResultDto<QuoteDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<PagedResultDto<QuoteDto>>> GetQuotes([FromQuery] GetQuotesQuery query)
        {
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get a specific quote by ID
        /// </summary>
        /// <param name="id">Quote ID</param>
        /// <returns>Quote details</returns>
        [HttpGet("{id:guid}")]
        [ProducesResponseType(typeof(OperationResultDto<QuoteDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<QuoteDto>>> GetQuote(Guid id)
        {
            var result = await _mediator.Send(new GetQuoteByIdQuery(id));

            if (!result.IsSuccess)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Generate a new quote based on shipment details
        /// </summary>
        /// <param name="request">Quote calculation request</param>
        /// <returns>Generated quote</returns>
        [HttpPost("generate")]
        [ProducesResponseType(typeof(OperationResultDto<QuoteDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<QuoteDto>>> GenerateQuote([FromBody] QuoteCalculationRequestDto request)
        {
            try
            {
                // Calculate pricing first
                var rateRequest = new RateCalculationRequestDto
                {
                    CustomerId = request.CustomerId,
                    ShipperId = request.ShipperId,
                    ServiceType = request.ServiceType,
                    OriginAddress = request.OriginAddress,
                    DestinationAddress = request.DestinationAddress,
                    PackageWeight = request.PackageWeight,
                    PackageDimensions = request.PackageDimensions,
                    DeclaredValue = request.DeclaredValue,
                    SpecialServices = request.SpecialServices,
                    Currency = request.Currency,
                    ShipDate = request.ShipDate
                };

                var pricingResult = await _pricingEngine.CalculateRateAsync(rateRequest);

                // TODO: Implement CreateQuoteCommand and handler to create quote from pricing result
                return BadRequest("Generate quote functionality not yet implemented");
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid quote generation request");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating quote");
                return StatusCode(500, new { message = "An error occurred while generating the quote" });
            }
        }

        /// <summary>
        /// Create a new quote
        /// </summary>
        /// <param name="command">Quote creation data</param>
        /// <returns>Created quote</returns>
        [HttpPost]
        [ProducesResponseType(typeof(OperationResultDto<QuoteDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<QuoteDto>>> CreateQuote([FromBody] CreateQuoteCommand command)
        {
            var result = await _mediator.Send(command);

            if (!result.IsSuccess)
            {
                return BadRequest(result);
            }

            return CreatedAtAction(
                nameof(GetQuote),
                new { id = result.Data!.Id },
                result);
        }

        /// <summary>
        /// Update an existing quote
        /// </summary>
        /// <param name="id">Quote ID</param>
        /// <param name="command">Updated quote data</param>
        /// <returns>Updated quote</returns>
        [HttpPut("{id:guid}")]
        [ProducesResponseType(typeof(OperationResultDto<QuoteDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<QuoteDto>>> UpdateQuote(Guid id, [FromBody] UpdateQuoteDto command)
        {
            try
            {
                // TODO: Implement UpdateQuoteCommand and handler
                return BadRequest("Update quote functionality not yet implemented");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating quote {QuoteId}", id);
                return StatusCode(500, new { message = "An error occurred while updating the quote" });
            }
        }

        /// <summary>
        /// Send a quote to customer
        /// </summary>
        /// <param name="id">Quote ID</param>
        /// <returns>Operation result</returns>
        [HttpPost("{id:guid}/send")]
        [ProducesResponseType(typeof(OperationResultDto<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<bool>>> SendQuote(Guid id)
        {
            var result = await _mediator.Send(new SendQuoteCommand(id));

            if (!result.IsSuccess)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Accept a quote
        /// </summary>
        /// <param name="id">Quote ID</param>
        /// <returns>Operation result</returns>
        [HttpPost("{id:guid}/accept")]
        [ProducesResponseType(typeof(OperationResultDto<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<bool>>> AcceptQuote(Guid id)
        {
            try
            {
                // TODO: Implement AcceptQuoteCommand and handler
                return BadRequest("Accept quote functionality not yet implemented");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error accepting quote {QuoteId}", id);
                return StatusCode(500, new { message = "An error occurred while accepting the quote" });
            }
        }

        /// <summary>
        /// Convert a quote to an order
        /// </summary>
        /// <param name="id">Quote ID</param>
        /// <returns>Operation result with order ID</returns>
        [HttpPost("{id:guid}/convert")]
        [ProducesResponseType(typeof(OperationResultDto<Guid>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<Guid>>> ConvertQuoteToOrder(Guid id)
        {
            var result = await _mediator.Send(new ConvertQuoteToOrderCommand(id));

            if (!result.IsSuccess)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Delete a quote (soft delete)
        /// </summary>
        /// <param name="id">Quote ID</param>
        /// <returns>Operation result</returns>
        [HttpDelete("{id:guid}")]
        [ProducesResponseType(typeof(OperationResultDto<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<bool>>> DeleteQuote(Guid id)
        {
            try
            {
                // TODO: Implement DeleteQuoteCommand and handler
                return BadRequest("Delete quote functionality not yet implemented");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting quote {QuoteId}", id);
                return StatusCode(500, new { message = "An error occurred while deleting the quote" });
            }
        }
    }
}
