using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PricingManagement.Domain.Repositories
{
    public interface IQuoteRepository
    {
        Task<Quote?> GetByIdAsync(Guid id);
        Task<Quote?> GetByIdAsync(Guid id, Guid organizationId);
        Task<Quote?> GetByQuoteNumberAsync(string quoteNumber, Guid organizationId);
        Task<IEnumerable<Quote>> GetByOrganizationAsync(Guid organizationId);
        Task<IEnumerable<Quote>> GetByCustomerAsync(Guid customerId, Guid organizationId);
        Task<IEnumerable<Quote>> GetByShipperAsync(Guid shipperId, Guid organizationId);
        Task<IEnumerable<Quote>> GetByStatusAsync(QuoteStatus status, Guid organizationId);
        Task<IEnumerable<Quote>> GetExpiringQuotesAsync(Guid organizationId, DateTime beforeDate);
        Task<IEnumerable<Quote>> GetQuotesByDateRangeAsync(Guid organizationId, DateTime fromDate, DateTime toDate);
        Task<IEnumerable<Quote>> GetConvertedQuotesAsync(Guid organizationId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<bool> ExistsAsync(Guid id, Guid organizationId);
        Task<bool> QuoteNumberExistsAsync(string quoteNumber, Guid organizationId);
        Task<string> GenerateQuoteNumberAsync(Guid organizationId);
        Task AddAsync(Quote quote);
        Task UpdateAsync(Quote quote);
        Task DeleteAsync(Quote quote);
        Task<int> CountByOrganizationAsync(Guid organizationId);
        Task<int> CountByStatusAsync(QuoteStatus status, Guid organizationId);
        Task<decimal> GetTotalQuoteValueAsync(Guid organizationId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<decimal> GetConversionRateAsync(Guid organizationId, DateTime? fromDate = null, DateTime? toDate = null);
    }
}
