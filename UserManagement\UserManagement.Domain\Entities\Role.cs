using System;
using System.Collections.Generic;
using System.Linq;
using UserManagement.Domain.Common;
using UserManagement.Domain.Exceptions;

namespace UserManagement.Domain.Entities
{
    public class Role : Entity
    {
        public Guid Id { get; set; }
        public string Name { get; private set; }
        public string NormalizedName { get; private set; }
        public string Description { get; private set; }
        public bool IsSystem { get; private set; }
        public Guid? OrganizationId { get; private set; }
        private readonly List<RolePermission> _permissions = new();
        public IReadOnlyCollection<RolePermission> Permissions => _permissions.AsReadOnly();

        private Role() { }

        public Role(Guid id,string name, string description, bool isSystem = false, Guid? organizationId = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Role name cannot be empty");
            Id = id;
            Name = name;
            NormalizedName = name.ToUpperInvariant();
            Description = description ?? string.Empty;
            IsSystem = isSystem;
            OrganizationId = organizationId;
        }

        public void AddPermission(Permission permission)
        {
            if (permission == null)
                throw new DomainException("Permission cannot be null");

            if (_permissions.Any(p => p.PermissionId == permission.Id))
                return;

            _permissions.Add(new RolePermission(this.Id, permission.Id));
        }

        public void RemovePermission(Permission permission)
        {
            if (permission == null)
                throw new DomainException("Permission cannot be null");

            var rolePermission = _permissions.FirstOrDefault(p => p.PermissionId == permission.Id);
            if (rolePermission != null)
            {
                _permissions.Remove(rolePermission);
            }
        }

        public bool HasPermission(string permissionName)
        {
            // This method would need to be implemented with the actual permission objects
            // For now, we'll just return false
            return false;
        }

        public void UpdateDescription(string description)
        {
            Description = description ?? string.Empty;
        }
    }

    public class RolePermission
    {
        public Guid RoleId { get; private set; }
        public Guid PermissionId { get; private set; }

        private RolePermission() { }

        public RolePermission(Guid roleId, Guid permissionId)
        {
            RoleId = roleId;
            PermissionId = permissionId;
        }
    }
}
