using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CarrierIntegration.Infrastructure.Persistence.Configurations
{
    public class CarrierServiceConfiguration : IEntityTypeConfiguration<CarrierService>
    {
        public void Configure(EntityTypeBuilder<CarrierService> builder)
        {
            builder.ToTable("CarrierServices");

            builder.HasKey(cs => cs.Id);

            builder.Property(cs => cs.Id)
                .ValueGeneratedNever();

            builder.Property(cs => cs.CarrierId)
                .IsRequired();

            builder.Property(cs => cs.ServiceCode)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(cs => cs.ServiceName)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(cs => cs.Description)
                .HasMaxLength(1000);

            builder.Property(cs => cs.ServiceLevel)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(cs => cs.MinWeightUnit)
                .HasMaxLength(10);

            builder.Property(cs => cs.MaxWeightUnit)
                .HasMaxLength(10);

            builder.Property(cs => cs.MaxDimensionsUnit)
                .HasMaxLength(10);

            builder.Property(cs => cs.MaxDeclaredValueCurrency)
                .HasConversion<int?>();

            builder.Property(cs => cs.ServiceArea)
                .HasMaxLength(500);

            builder.Property(cs => cs.RestrictedAreas)
                .HasMaxLength(1000);

            builder.Property(cs => cs.SpecialInstructions)
                .HasMaxLength(1000);

            builder.Property(cs => cs.RateStructure)
                .HasMaxLength(500);

            builder.Property(cs => cs.FuelSurchargeStructure)
                .HasMaxLength(500);

            builder.Property(cs => cs.AccessorialCharges)
                .HasMaxLength(1000);

            builder.Property(cs => cs.ApiServiceCode)
                .HasMaxLength(50);

            builder.Property(cs => cs.TrackingUrlTemplate)
                .HasMaxLength(500);

            builder.Property(cs => cs.LabelFormat)
                .HasMaxLength(50);

            builder.Property(cs => cs.AccountRequirements)
                .HasMaxLength(1000);

            builder.Property(cs => cs.ComplianceRequirements)
                .HasMaxLength(1000);

            builder.Property(cs => cs.DocumentationRequirements)
                .HasMaxLength(1000);

            builder.Property(cs => cs.Terms)
                .HasMaxLength(2000);

            builder.Property(cs => cs.Conditions)
                .HasMaxLength(2000);

            builder.Property(cs => cs.OrganizationId)
                .IsRequired();

            builder.Property(cs => cs.CreatedAt)
                .IsRequired();

            builder.Property(cs => cs.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cs => cs.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(cs => cs.DeletedBy)
                .HasMaxLength(100);

            // Configure value objects
            builder.OwnsOne(cs => cs.BaseRate, money =>
            {
                money.Property(m => m.Amount)
                    .HasPrecision(18, 2)
                    .HasColumnName("BaseRateAmount");

                money.Property(m => m.Currency)
                    .HasConversion<int>()
                    .HasColumnName("BaseRateCurrency");
            });

            // Configure collections
            builder.Property(cs => cs.SupportedCountries)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                .HasColumnName("SupportedCountries");

            builder.Property(cs => cs.RestrictedCountries)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                .HasColumnName("RestrictedCountries");

            builder.Property(cs => cs.SupportedPostalCodes)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                .HasColumnName("SupportedPostalCodes");

            builder.Property(cs => cs.RestrictedPostalCodes)
                .HasConversion(
                    v => string.Join(',', v),
                    v => v.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList())
                .HasColumnName("RestrictedPostalCodes");

            // Configure indexes
            builder.HasIndex(cs => new { cs.CarrierId, cs.ServiceCode })
                .IsUnique()
                .HasDatabaseName("IX_CarrierServices_CarrierId_ServiceCode");

            builder.HasIndex(cs => cs.OrganizationId)
                .HasDatabaseName("IX_CarrierServices_OrganizationId");

            builder.HasIndex(cs => cs.ServiceLevel)
                .HasDatabaseName("IX_CarrierServices_ServiceLevel");

            builder.HasIndex(cs => cs.IsActive)
                .HasDatabaseName("IX_CarrierServices_IsActive");

            builder.HasIndex(cs => cs.CreatedAt)
                .HasDatabaseName("IX_CarrierServices_CreatedAt");

            // Ignore domain events for EF Core
            builder.Ignore(cs => cs.DomainEvents);
        }
    }
}
