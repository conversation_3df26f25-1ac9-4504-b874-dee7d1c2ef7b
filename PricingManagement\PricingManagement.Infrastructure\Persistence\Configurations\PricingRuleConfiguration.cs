using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;

namespace PricingManagement.Infrastructure.Persistence.Configurations
{
    public class PricingRuleConfiguration : IEntityTypeConfiguration<PricingRule>
    {
        public void Configure(EntityTypeBuilder<PricingRule> builder)
        {
            builder.ToTable("PricingRules");

            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id)
                .ValueGeneratedNever();

            builder.Property(x => x.Name)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(x => x.Description)
                .IsRequired()
                .HasMaxLength(1000);

            builder.Property(x => x.RuleType)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(x => x.CalculationMethod)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(x => x.Status)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(x => x.Priority)
                .IsRequired();

            builder.Property(x => x.EffectiveDate)
                .IsRequired();

            builder.Property(x => x.ExpirationDate);

            builder.Property(x => x.ServiceTypes)
                .HasColumnType("jsonb");

            builder.Property(x => x.OriginZones)
                .HasColumnType("jsonb");

            builder.Property(x => x.DestinationZones)
                .HasColumnType("jsonb");

            builder.Property(x => x.CustomerSegments)
                .HasColumnType("jsonb");

            builder.Property(x => x.ShipperTypes)
                .HasColumnType("jsonb");

            builder.Property(x => x.MinWeight)
                .HasPrecision(18, 3);

            builder.Property(x => x.MaxWeight)
                .HasPrecision(18, 3);

            builder.Property(x => x.MinDistance)
                .HasPrecision(18, 2);

            builder.Property(x => x.MaxDistance)
                .HasPrecision(18, 2);

            builder.Property(x => x.MinValue)
                .HasPrecision(18, 2);

            builder.Property(x => x.MaxValue)
                .HasPrecision(18, 2);

            builder.Property(x => x.RuleConfiguration)
                .IsRequired()
                .HasColumnType("jsonb");

            builder.Property(x => x.RequiresApproval)
                .IsRequired();

            builder.Property(x => x.ApprovalWorkflow)
                .HasMaxLength(500);

            builder.Property(x => x.Tags)
                .HasColumnType("jsonb");

            // Base entity properties
            builder.Property(x => x.CreatedAt)
                .IsRequired();

            builder.Property(x => x.UpdatedAt);

            builder.Property(x => x.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(x => x.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(x => x.DeletedAt);

            builder.Property(x => x.DeletedBy)
                .HasMaxLength(100);

            builder.Property(x => x.OrganizationId)
                .IsRequired();

            // Owned entities for tiers
            builder.OwnsMany(x => x.Tiers, tierBuilder =>
            {
                tierBuilder.ToTable("PricingRuleTiers");
                
                tierBuilder.WithOwner().HasForeignKey("PricingRuleId");
                
                tierBuilder.Property<int>("Id")
                    .ValueGeneratedOnAdd();
                
                tierBuilder.HasKey("Id");

                tierBuilder.Property(t => t.FromValue)
                    .IsRequired()
                    .HasPrecision(18, 3);

                tierBuilder.Property(t => t.ToValue)
                    .HasPrecision(18, 3);

                tierBuilder.Property(t => t.Rate)
                    .IsRequired()
                    .HasPrecision(18, 4);

                tierBuilder.Property(t => t.RateType)
                    .HasMaxLength(50);
            });

            // Indexes
            builder.HasIndex(x => x.OrganizationId);
            builder.HasIndex(x => new { x.OrganizationId, x.Name }).IsUnique();
            builder.HasIndex(x => new { x.OrganizationId, x.Status });
            builder.HasIndex(x => new { x.OrganizationId, x.RuleType });
            builder.HasIndex(x => new { x.OrganizationId, x.Priority });
            builder.HasIndex(x => new { x.OrganizationId, x.EffectiveDate });
            builder.HasIndex(x => new { x.OrganizationId, x.ExpirationDate });
        }
    }
}
