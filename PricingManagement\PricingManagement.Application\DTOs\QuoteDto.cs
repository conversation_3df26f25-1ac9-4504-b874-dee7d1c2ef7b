using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using System;
using System.Collections.Generic;

namespace PricingManagement.Application.DTOs
{
    public class QuoteDto
    {
        public Guid Id { get; set; }
        public string QuoteNumber { get; set; } = string.Empty;
        public Guid? CustomerId { get; set; }
        public Guid? ShipperId { get; set; }
        public string? CustomerName { get; set; }
        public string? ShipperName { get; set; }
        public QuoteStatus Status { get; set; }
        public DateTime ExpirationDate { get; set; }
        public string? OriginAddress { get; set; }
        public string? DestinationAddress { get; set; }
        public string? OriginZoneId { get; set; }
        public string? DestinationZoneId { get; set; }
        public decimal? Distance { get; set; }
        public string? ServiceType { get; set; }
        public WeightDto? PackageWeight { get; set; }
        public DimensionsDto? PackageDimensions { get; set; }
        public WeightDto? DimensionalWeight { get; set; }
        public WeightDto? BillableWeight { get; set; }
        public decimal? DeclaredValue { get; set; }
        public List<string> SpecialServices { get; set; } = new();
        public MoneyDto BaseRate { get; set; } = new();
        public MoneyDto TotalSurcharges { get; set; } = new();
        public MoneyDto TotalDiscounts { get; set; } = new();
        public MoneyDto TotalTaxes { get; set; } = new();
        public MoneyDto TotalAmount { get; set; } = new();
        public CurrencyCode Currency { get; set; }
        public string? Notes { get; set; }
        public string? Terms { get; set; }
        public DateTime? ConvertedToOrderAt { get; set; }
        public Guid? OrderId { get; set; }
        public string? ExternalQuoteId { get; set; }
        public List<QuoteLineItemDto> LineItems { get; set; } = new();
        public List<SurchargeDto> Surcharges { get; set; } = new();
        public List<DiscountDto> Discounts { get; set; } = new();
        public List<TaxDto> Taxes { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string? UpdatedBy { get; set; }
    }

    public class QuoteLineItemDto
    {
        public string Description { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public MoneyDto UnitPrice { get; set; } = new();
        public MoneyDto TotalPrice { get; set; } = new();
        public string? ItemType { get; set; }
    }

    public class CreateQuoteDto
    {
        public Guid? CustomerId { get; set; }
        public Guid? ShipperId { get; set; }
        public string? CustomerName { get; set; }
        public string? ShipperName { get; set; }
        public DateTime ExpirationDate { get; set; }
        public string OriginAddress { get; set; } = string.Empty;
        public string DestinationAddress { get; set; } = string.Empty;
        public string? OriginZoneId { get; set; }
        public string? DestinationZoneId { get; set; }
        public decimal? Distance { get; set; }
        public string ServiceType { get; set; } = string.Empty;
        public WeightDto PackageWeight { get; set; } = new();
        public DimensionsDto? PackageDimensions { get; set; }
        public decimal? DeclaredValue { get; set; }
        public List<string> SpecialServices { get; set; } = new();
        public CurrencyCode Currency { get; set; } = CurrencyCode.USD;
        public string? Notes { get; set; }
        public string? Terms { get; set; }
        public List<CreateQuoteLineItemDto> LineItems { get; set; } = new();
    }

    public class CreateQuoteLineItemDto
    {
        public string Description { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public MoneyDto UnitPrice { get; set; } = new();
        public string? ItemType { get; set; }
    }

    public class UpdateQuoteDto
    {
        public string? CustomerName { get; set; }
        public string? ShipperName { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public string? OriginAddress { get; set; }
        public string? DestinationAddress { get; set; }
        public string? ServiceType { get; set; }
        public WeightDto? PackageWeight { get; set; }
        public DimensionsDto? PackageDimensions { get; set; }
        public decimal? DeclaredValue { get; set; }
        public List<string> SpecialServices { get; set; } = new();
        public string? Notes { get; set; }
        public string? Terms { get; set; }
    }

    public class QuoteCalculationRequestDto
    {
        public Guid? CustomerId { get; set; }
        public Guid? ShipperId { get; set; }
        public string OriginAddress { get; set; } = string.Empty;
        public string DestinationAddress { get; set; } = string.Empty;
        public string ServiceType { get; set; } = string.Empty;
        public WeightDto PackageWeight { get; set; } = new();
        public DimensionsDto? PackageDimensions { get; set; }
        public decimal? DeclaredValue { get; set; }
        public List<string> SpecialServices { get; set; } = new();
        public CurrencyCode Currency { get; set; } = CurrencyCode.USD;
        public DateTime ShipDate { get; set; } = DateTime.UtcNow;
    }
}
