using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using System;

namespace PricingManagement.Application.DTOs
{
    public class SurchargeDto
    {
        public SurchargeType SurchargeType { get; set; }
        public string Description { get; set; } = string.Empty;
        public Money Amount { get; set; } = Money.Zero(CurrencyCode.USD);
        public string? CalculationBasis { get; set; }
        public bool IsApplied { get; set; } = true;
    }

    public class DiscountDto
    {
        public DiscountType DiscountType { get; set; }
        public string Description { get; set; } = string.Empty;
        public Money Amount { get; set; } = Money.Zero(CurrencyCode.USD);
        public decimal? Percentage { get; set; }
        public string? CalculationBasis { get; set; }
        public bool IsApplied { get; set; } = true;
        public bool RequiresApproval { get; set; }
        public ApprovalStatus? ApprovalStatus { get; set; }
    }

    public class TaxDto
    {
        public TaxType TaxType { get; set; }
        public string Description { get; set; } = string.Empty;
        public Money Amount { get; set; } = Money.Zero(CurrencyCode.USD);
        public decimal Rate { get; set; }
        public string? Jurisdiction { get; set; }
        public bool IsApplied { get; set; } = true;
    }

    public class AddressDto
    {
        public string Street1 { get; set; } = string.Empty;
        public string? Street2 { get; set; }
        public string City { get; set; } = string.Empty;
        public string State { get; set; } = string.Empty;
        public string PostalCode { get; set; } = string.Empty;
        public string Country { get; set; } = string.Empty;
        public bool IsResidential { get; set; }
        public bool IsCommercial { get; set; }
        public string? ZoneId { get; set; }
    }

    public class WeightDto
    {
        public decimal Value { get; set; }
        public WeightUnit Unit { get; set; }
    }

    public class DimensionsDto
    {
        public decimal Length { get; set; }
        public decimal Width { get; set; }
        public decimal Height { get; set; }
        public DimensionUnit Unit { get; set; }
    }

    public class MoneyDto
    {
        public decimal Amount { get; set; }
        public CurrencyCode Currency { get; set; }
    }

    public class PagedResultDto<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }

    public class PaginationDto
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; }
        public string? SearchTerm { get; set; }
    }

    public class FilterDto
    {
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string> Statuses { get; set; } = new();
        public List<string> Types { get; set; } = new();
        public List<Guid> CustomerIds { get; set; } = new();
        public List<Guid> ShipperIds { get; set; } = new();
        public List<string> ServiceTypes { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public Dictionary<string, object> CustomFilters { get; set; } = new();
    }

    public class ValidationResultDto
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, List<string>> FieldErrors { get; set; } = new();
    }

    public class OperationResultDto<T>
    {
        public bool IsSuccess { get; set; }
        public T? Data { get; set; }
        public string? Message { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();

        public static OperationResultDto<T> Success(T data, string? message = null)
        {
            return new OperationResultDto<T>
            {
                IsSuccess = true,
                Data = data,
                Message = message
            };
        }

        public static OperationResultDto<T> Failure(string error)
        {
            return new OperationResultDto<T>
            {
                IsSuccess = false,
                Errors = new List<string> { error }
            };
        }

        public static OperationResultDto<T> Failure(List<string> errors)
        {
            return new OperationResultDto<T>
            {
                IsSuccess = false,
                Errors = errors
            };
        }
    }
}
