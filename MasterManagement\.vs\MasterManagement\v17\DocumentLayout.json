{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|d:\\projects\\tritrackz\\tritrackzmicroservices\\mastermanagement\\mastermanagement.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|solutionrelative:mastermanagement.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|solutionrelative:mastermanagement.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|solutionrelative:mastermanagement.api\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.api\\mastermanagement.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|solutionrelative:mastermanagement.api\\mastermanagement.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.api\\controllers\\geographiccontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|solutionrelative:mastermanagement.api\\controllers\\geographiccontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2D1C64D-44F7-48A5-AAA1-3364FCBE4150}|MasterManagement.Infrastructure\\MasterManagement.Infrastructure.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.infrastructure\\services\\organizationcontextservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2D1C64D-44F7-48A5-AAA1-3364FCBE4150}|MasterManagement.Infrastructure\\MasterManagement.Infrastructure.csproj|solutionrelative:mastermanagement.infrastructure\\services\\organizationcontextservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{94941624-AEF1-4363-97D7-472AD0F168A5}|MasterManagement.Application\\MasterManagement.Application.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.application\\common\\behaviors\\organizationcontextbehavior.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{94941624-AEF1-4363-97D7-472AD0F168A5}|MasterManagement.Application\\MasterManagement.Application.csproj|solutionrelative:mastermanagement.application\\common\\behaviors\\organizationcontextbehavior.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2D1C64D-44F7-48A5-AAA1-3364FCBE4150}|MasterManagement.Infrastructure\\MasterManagement.Infrastructure.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.infrastructure\\services\\usermanagementintegrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2D1C64D-44F7-48A5-AAA1-3364FCBE4150}|MasterManagement.Infrastructure\\MasterManagement.Infrastructure.csproj|solutionrelative:mastermanagement.infrastructure\\services\\usermanagementintegrationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.api\\controllers\\servicemanagementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|solutionrelative:mastermanagement.api\\controllers\\servicemanagementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.api\\controllers\\dataqualitycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|solutionrelative:mastermanagement.api\\controllers\\dataqualitycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.api\\controllers\\businessrulescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{36A05D1E-88BD-4CAA-B366-9BD0268771CB}|MasterManagement.API\\MasterManagement.API.csproj|solutionrelative:mastermanagement.api\\controllers\\businessrulescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{94941624-AEF1-4363-97D7-472AD0F168A5}|MasterManagement.Application\\MasterManagement.Application.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.application\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{94941624-AEF1-4363-97D7-472AD0F168A5}|MasterManagement.Application\\MasterManagement.Application.csproj|solutionrelative:mastermanagement.application\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{94941624-AEF1-4363-97D7-472AD0F168A5}|MasterManagement.Application\\MasterManagement.Application.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.application\\mastermanagement.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{94941624-AEF1-4363-97D7-472AD0F168A5}|MasterManagement.Application\\MasterManagement.Application.csproj|solutionrelative:mastermanagement.application\\mastermanagement.application.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{94941624-AEF1-4363-97D7-472AD0F168A5}|MasterManagement.Application\\MasterManagement.Application.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.application\\common\\mappings\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{94941624-AEF1-4363-97D7-472AD0F168A5}|MasterManagement.Application\\MasterManagement.Application.csproj|solutionrelative:mastermanagement.application\\common\\mappings\\mappingprofile.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2D1C64D-44F7-48A5-AAA1-3364FCBE4150}|MasterManagement.Infrastructure\\MasterManagement.Infrastructure.csproj|D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\mastermanagement.infrastructure\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2D1C64D-44F7-48A5-AAA1-3364FCBE4150}|MasterManagement.Infrastructure\\MasterManagement.Infrastructure.csproj|solutionrelative:mastermanagement.infrastructure\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\appsettings.json", "RelativeDocumentMoniker": "MasterManagement.API\\appsettings.json", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\appsettings.json", "RelativeToolTip": "MasterManagement.API\\appsettings.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAwAAAA5AAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-03T12:43:01.727Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Program.cs", "RelativeDocumentMoniker": "MasterManagement.API\\Program.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Program.cs", "RelativeToolTip": "MasterManagement.API\\Program.cs", "ViewState": "AQIAAHEAAAAAAAAAAAAowIMAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T12:14:47.586Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "UserManagementIntegrationService.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Infrastructure\\Services\\UserManagementIntegrationService.cs", "RelativeDocumentMoniker": "MasterManagement.Infrastructure\\Services\\UserManagementIntegrationService.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Infrastructure\\Services\\UserManagementIntegrationService.cs", "RelativeToolTip": "MasterManagement.Infrastructure\\Services\\UserManagementIntegrationService.cs", "ViewState": "AQIAAAMAAAAAAAAAAAAhwBwAAAAIAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T11:22:06.961Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "OrganizationContextBehavior.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Application\\Common\\Behaviors\\OrganizationContextBehavior.cs", "RelativeDocumentMoniker": "MasterManagement.Application\\Common\\Behaviors\\OrganizationContextBehavior.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Application\\Common\\Behaviors\\OrganizationContextBehavior.cs", "RelativeToolTip": "MasterManagement.Application\\Common\\Behaviors\\OrganizationContextBehavior.cs", "ViewState": "AQIAABMAAAAAAAAAAAAQwB4AAAAQAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T11:22:06.939Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ServiceManagementController.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Controllers\\ServiceManagementController.cs", "RelativeDocumentMoniker": "MasterManagement.API\\Controllers\\ServiceManagementController.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Controllers\\ServiceManagementController.cs", "RelativeToolTip": "MasterManagement.API\\Controllers\\ServiceManagementController.cs", "ViewState": "AQIAACcAAAAAAAAAAAAYwEAAAAAMAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:12:59.315Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "MasterManagement.Application.csproj", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Application\\MasterManagement.Application.csproj", "RelativeDocumentMoniker": "MasterManagement.Application\\MasterManagement.Application.csproj", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Application\\MasterManagement.Application.csproj", "RelativeToolTip": "MasterManagement.Application\\MasterManagement.Application.csproj", "ViewState": "AQIAAAAAAAAAAAAAAAAAABAAAAAKAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-27T07:13:06.852Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "launchSettings.json", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "MasterManagement.API\\Properties\\launchSettings.json", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Properties\\launchSettings.json", "RelativeToolTip": "MasterManagement.API\\Properties\\launchSettings.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAAA4AAAAcAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-05-27T12:12:23.606Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "OrganizationContextService.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Infrastructure\\Services\\OrganizationContextService.cs", "RelativeDocumentMoniker": "MasterManagement.Infrastructure\\Services\\OrganizationContextService.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Infrastructure\\Services\\OrganizationContextService.cs", "RelativeToolTip": "MasterManagement.Infrastructure\\Services\\OrganizationContextService.cs", "ViewState": "AQIAABYAAAAAAAAAAAAuwCEAAABTAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T07:10:26.938Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "GeographicController.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Controllers\\GeographicController.cs", "RelativeDocumentMoniker": "MasterManagement.API\\Controllers\\GeographicController.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Controllers\\GeographicController.cs", "RelativeToolTip": "MasterManagement.API\\Controllers\\GeographicController.cs", "ViewState": "AQIAAAwAAAAAAAAAAAAuwBcAAAAiAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:12:49.112Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "MasterManagement.API.csproj", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\MasterManagement.API.csproj", "RelativeDocumentMoniker": "MasterManagement.API\\MasterManagement.API.csproj", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\MasterManagement.API.csproj", "RelativeToolTip": "MasterManagement.API\\MasterManagement.API.csproj", "ViewState": "AQIAAAAAAAAAAAAAAAAAABcAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-27T07:13:14.293Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "MappingProfile.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Application\\Common\\Mappings\\MappingProfile.cs", "RelativeDocumentMoniker": "MasterManagement.Application\\Common\\Mappings\\MappingProfile.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Application\\Common\\Mappings\\MappingProfile.cs", "RelativeToolTip": "MasterManagement.Application\\Common\\Mappings\\MappingProfile.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAEAAAAXAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T07:12:58.907Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "DataQualityController.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Controllers\\DataQualityController.cs", "RelativeDocumentMoniker": "MasterManagement.API\\Controllers\\DataQualityController.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Controllers\\DataQualityController.cs", "RelativeToolTip": "MasterManagement.API\\Controllers\\DataQualityController.cs", "ViewState": "AQIAANcAAAAAAAAAAAAUwA0AAAAPAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:12:47.89Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "BusinessRulesController.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Controllers\\BusinessRulesController.cs", "RelativeDocumentMoniker": "MasterManagement.API\\Controllers\\BusinessRulesController.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.API\\Controllers\\BusinessRulesController.cs", "RelativeToolTip": "MasterManagement.API\\Controllers\\BusinessRulesController.cs", "ViewState": "AQIAAMwAAAAAAAAAAAAIwDYAAAA/AAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T08:12:39.507Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "DependencyInjection.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Application\\DependencyInjection.cs", "RelativeDocumentMoniker": "MasterManagement.Application\\DependencyInjection.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Application\\DependencyInjection.cs", "RelativeToolTip": "MasterManagement.Application\\DependencyInjection.cs", "ViewState": "AQIAAAYAAAAAAAAAAAAcwBAAAAAVAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T07:13:46.709Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "DependencyInjection.cs", "DocumentMoniker": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Infrastructure\\DependencyInjection.cs", "RelativeDocumentMoniker": "MasterManagement.Infrastructure\\DependencyInjection.cs", "ToolTip": "D:\\Projects\\TRITRACKZ\\TriTrackzMicroservices\\MasterManagement\\MasterManagement.Infrastructure\\DependencyInjection.cs", "RelativeToolTip": "MasterManagement.Infrastructure\\DependencyInjection.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAsAAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T07:10:22.74Z"}]}]}]}