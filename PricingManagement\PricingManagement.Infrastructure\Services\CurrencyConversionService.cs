using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Services;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using System.Text.Json;

namespace PricingManagement.Infrastructure.Services
{
    public class CurrencyConversionService : ICurrencyConversionService
    {
        private readonly HttpClient _httpClient;
        private readonly IMemoryCache _cache;
        private readonly IConfiguration _configuration;
        private readonly ILogger<CurrencyConversionService> _logger;

        private const string EXCHANGE_RATES_CACHE_KEY = "exchange_rates";
        private const int CACHE_DURATION_MINUTES = 60;

        // Mock exchange rates (in production, these would come from a real API)
        private readonly Dictionary<CurrencyCode, decimal> _mockExchangeRates = new()
        {
            [CurrencyCode.USD] = 1.0m,      // Base currency
            [CurrencyCode.EUR] = 0.85m,     // 1 USD = 0.85 EUR
            [CurrencyCode.GBP] = 0.73m,     // 1 USD = 0.73 GBP
            [CurrencyCode.CAD] = 1.35m,     // 1 USD = 1.35 CAD
            [CurrencyCode.AUD] = 1.45m,     // 1 USD = 1.45 AUD
            [CurrencyCode.JPY] = 110.0m,    // 1 USD = 110 JPY
            [CurrencyCode.CHF] = 0.92m,     // 1 USD = 0.92 CHF
            [CurrencyCode.CNY] = 6.45m,     // 1 USD = 6.45 CNY
            [CurrencyCode.INR] = 74.5m,     // 1 USD = 74.5 INR
            [CurrencyCode.MXN] = 20.1m      // 1 USD = 20.1 MXN
        };

        public CurrencyConversionService(
            HttpClient httpClient,
            IMemoryCache cache,
            IConfiguration configuration,
            ILogger<CurrencyConversionService> logger)
        {
            _httpClient = httpClient;
            _cache = cache;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<Money> ConvertAsync(Money amount, CurrencyCode targetCurrency)
        {
            try
            {
                if (amount.Currency == targetCurrency)
                {
                    return amount;
                }

                var exchangeRate = await GetExchangeRateAsync(amount.Currency, targetCurrency);
                var convertedAmount = amount.Amount * exchangeRate;

                return new Money(Math.Round(convertedAmount, 4), targetCurrency);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting {Amount} {FromCurrency} to {ToCurrency}", 
                    amount.Amount, amount.Currency, targetCurrency);
                throw;
            }
        }

        public async Task<decimal> GetExchangeRateAsync(CurrencyCode fromCurrency, CurrencyCode toCurrency)
        {
            try
            {
                if (fromCurrency == toCurrency)
                {
                    return 1.0m;
                }

                var exchangeRates = await GetExchangeRatesAsync(CurrencyCode.USD);

                // Convert to USD first, then to target currency
                var fromRate = exchangeRates.GetValueOrDefault(fromCurrency, 1.0m);
                var toRate = exchangeRates.GetValueOrDefault(toCurrency, 1.0m);

                // If fromCurrency is USD, just return the target rate
                if (fromCurrency == CurrencyCode.USD)
                {
                    return toRate;
                }

                // If toCurrency is USD, return 1/fromRate
                if (toCurrency == CurrencyCode.USD)
                {
                    return 1.0m / fromRate;
                }

                // For other currencies, convert via USD
                return toRate / fromRate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting exchange rate from {FromCurrency} to {ToCurrency}", 
                    fromCurrency, toCurrency);
                return 1.0m; // Return 1:1 rate as fallback
            }
        }

        public async Task<Dictionary<CurrencyCode, decimal>> GetExchangeRatesAsync(CurrencyCode baseCurrency)
        {
            try
            {
                // Check cache first
                var cacheKey = $"{EXCHANGE_RATES_CACHE_KEY}_{baseCurrency}";
                if (_cache.TryGetValue(cacheKey, out Dictionary<CurrencyCode, decimal>? cachedRates) && cachedRates != null)
                {
                    return cachedRates;
                }

                // Get fresh rates
                var rates = await FetchExchangeRatesAsync(baseCurrency);

                // Cache the rates
                _cache.Set(cacheKey, rates, TimeSpan.FromMinutes(CACHE_DURATION_MINUTES));

                return rates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting exchange rates for base currency {BaseCurrency}", baseCurrency);
                return new Dictionary<CurrencyCode, decimal>();
            }
        }

        public async Task<bool> IsCurrencySupportedAsync(CurrencyCode currency)
        {
            try
            {
                return _mockExchangeRates.ContainsKey(currency);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if currency {Currency} is supported", currency);
                return false;
            }
        }

        public async Task<DateTime> GetLastUpdateTimeAsync()
        {
            try
            {
                // In a real implementation, this would return the actual last update time from the exchange rate provider
                return DateTime.UtcNow.AddMinutes(-30); // Mock: updated 30 minutes ago
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last update time");
                return DateTime.UtcNow.AddHours(-1); // Fallback
            }
        }

        public async Task RefreshExchangeRatesAsync()
        {
            try
            {
                _logger.LogInformation("Refreshing exchange rates");

                // Clear cache to force refresh
                foreach (var currency in Enum.GetValues<CurrencyCode>())
                {
                    var cacheKey = $"{EXCHANGE_RATES_CACHE_KEY}_{currency}";
                    _cache.Remove(cacheKey);
                }

                // Pre-load rates for major currencies
                var majorCurrencies = new[] { CurrencyCode.USD, CurrencyCode.EUR, CurrencyCode.GBP, CurrencyCode.CAD };
                foreach (var currency in majorCurrencies)
                {
                    await GetExchangeRatesAsync(currency);
                }

                _logger.LogInformation("Exchange rates refreshed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing exchange rates");
            }
        }

        public async Task<Money> ConvertWithHistoricalRateAsync(Money amount, CurrencyCode targetCurrency, DateTime date)
        {
            try
            {
                if (amount.Currency == targetCurrency)
                {
                    return amount;
                }

                var historicalRate = await GetHistoricalExchangeRateAsync(amount.Currency, targetCurrency, date);
                var convertedAmount = amount.Amount * historicalRate;

                return new Money(Math.Round(convertedAmount, 4), targetCurrency);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting {Amount} {FromCurrency} to {ToCurrency} with historical rate for {Date}", 
                    amount.Amount, amount.Currency, targetCurrency, date);
                
                // Fallback to current rate
                return await ConvertAsync(amount, targetCurrency);
            }
        }

        public async Task<decimal> GetHistoricalExchangeRateAsync(CurrencyCode fromCurrency, CurrencyCode toCurrency, DateTime date)
        {
            try
            {
                // TODO: Implement historical exchange rate lookup
                // For now, return current rate with some variation based on date
                var currentRate = await GetExchangeRateAsync(fromCurrency, toCurrency);
                
                // Add some mock historical variation (±5%)
                var daysDiff = (DateTime.UtcNow - date).Days;
                var variation = (daysDiff % 10 - 5) * 0.01m; // -5% to +5%
                
                return currentRate * (1 + variation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting historical exchange rate from {FromCurrency} to {ToCurrency} for {Date}", 
                    fromCurrency, toCurrency, date);
                return 1.0m;
            }
        }

        private async Task<Dictionary<CurrencyCode, decimal>> FetchExchangeRatesAsync(CurrencyCode baseCurrency)
        {
            try
            {
                // TODO: Integrate with real exchange rate API (e.g., exchangerate-api.com, fixer.io, etc.)
                // For now, return mock rates adjusted for the base currency

                var rates = new Dictionary<CurrencyCode, decimal>();

                if (baseCurrency == CurrencyCode.USD)
                {
                    // Return rates as-is for USD base
                    foreach (var kvp in _mockExchangeRates)
                    {
                        rates[kvp.Key] = kvp.Value;
                    }
                }
                else
                {
                    // Convert all rates to the specified base currency
                    var baseToUsdRate = _mockExchangeRates.GetValueOrDefault(baseCurrency, 1.0m);
                    
                    foreach (var kvp in _mockExchangeRates)
                    {
                        if (kvp.Key == baseCurrency)
                        {
                            rates[kvp.Key] = 1.0m;
                        }
                        else
                        {
                            // Convert via USD
                            rates[kvp.Key] = kvp.Value / baseToUsdRate;
                        }
                    }
                }

                _logger.LogInformation("Fetched exchange rates for base currency {BaseCurrency}", baseCurrency);
                return rates;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching exchange rates for base currency {BaseCurrency}", baseCurrency);
                return _mockExchangeRates.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
            }
        }
    }
}
