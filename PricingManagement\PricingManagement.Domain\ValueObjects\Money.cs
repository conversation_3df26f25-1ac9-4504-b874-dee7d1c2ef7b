using PricingManagement.Domain.Enums;
using PricingManagement.Domain.Exceptions;
using System;
using System.Collections.Generic;

namespace PricingManagement.Domain.ValueObjects
{
    public class Money : IEquatable<Money>
    {
        public decimal Amount { get; }
        public CurrencyCode Currency { get; }

        public Money(decimal amount, CurrencyCode currency)
        {
            if (amount < 0)
                throw new DomainException("Amount cannot be negative");

            Amount = Math.Round(amount, 2);
            Currency = currency;
        }

        public static Money Zero(CurrencyCode currency) => new(0, currency);

        public Money Add(Money other)
        {
            if (Currency != other.Currency)
                throw new InvalidCurrencyException($"Cannot add {Currency} and {other.Currency}");

            return new Money(Amount + other.Amount, Currency);
        }

        public Money Subtract(Money other)
        {
            if (Currency != other.Currency)
                throw new InvalidCurrencyException($"Cannot subtract {other.Currency} from {Currency}");

            var result = Amount - other.Amount;
            if (result < 0)
                throw new DomainException("Result cannot be negative");

            return new Money(result, Currency);
        }

        public Money Multiply(decimal factor)
        {
            if (factor < 0)
                throw new DomainException("Factor cannot be negative");

            return new Money(Amount * factor, Currency);
        }

        public Money ApplyPercentage(decimal percentage)
        {
            return new Money(Amount * (percentage / 100), Currency);
        }

        public bool Equals(Money? other)
        {
            if (other is null) return false;
            return Amount == other.Amount && Currency == other.Currency;
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as Money);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(Amount, Currency);
        }

        public static bool operator ==(Money? left, Money? right)
        {
            return EqualityComparer<Money>.Default.Equals(left, right);
        }

        public static bool operator !=(Money? left, Money? right)
        {
            return !(left == right);
        }

        public static Money operator +(Money left, Money right)
        {
            return left.Add(right);
        }

        public static Money operator -(Money left, Money right)
        {
            return left.Subtract(right);
        }

        public static Money operator *(Money left, decimal right)
        {
            return left.Multiply(right);
        }

        public override string ToString()
        {
            return $"{Amount:C} {Currency}";
        }
    }
}
