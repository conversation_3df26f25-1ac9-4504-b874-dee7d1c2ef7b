{"ConnectionStrings": {"DefaultConnection": "Server=**************;Port=5432;Database=tritrackz_mastermanagement;User Id=postgres;Password=************;", "Redis": "localhost:6379"}, "IdentityApi": {"BaseUrl": "https://localhost:7001/api/v1"}, "UserManagementApi": {"BaseUrl": "https://localhost:7002/api/v1"}, "JwtSettings": {"Secret": "ThisIsAVerySecureKeyThatShouldBeStoredInASecureVault", "Issuer": "TriTrackzIdentity", "Audience": "TriTrackzServices"}, "RabbitMQ": {"Host": "localhost", "UserName": "guest", "Password": "guest"}, "ExternalServices": {"GoogleMapsApiKey": "your-google-maps-api-key", "AddressValidationProvider": "Google"}, "FeatureFlags": {"EnableCaching": true, "EnableSpatialQueries": true, "EnableCrossOrganizationSharing": true}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/mastermanagement-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}, "AllowedHosts": "*"}