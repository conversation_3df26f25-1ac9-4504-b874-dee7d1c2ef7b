using PricingManagement.Domain.Common;
using System;

namespace PricingManagement.Domain.Events
{
    public class ContractCreatedEvent : DomainEvent
    {
        public Guid ContractId { get; }
        public string ContractNumber { get; }
        public string Name { get; }

        public ContractCreatedEvent(Guid contractId, string contractNumber, string name, Guid? organizationId)
            : base(organizationId)
        {
            ContractId = contractId;
            ContractNumber = contractNumber;
            Name = name;
        }
    }

    public class ContractUpdatedEvent : DomainEvent
    {
        public Guid ContractId { get; }
        public string ContractNumber { get; }
        public string Name { get; }

        public ContractUpdatedEvent(Guid contractId, string contractNumber, string name, Guid? organizationId)
            : base(organizationId)
        {
            ContractId = contractId;
            ContractNumber = contractNumber;
            Name = name;
        }
    }

    public class ContractActivatedEvent : DomainEvent
    {
        public Guid ContractId { get; }
        public string ContractNumber { get; }
        public string Name { get; }

        public ContractActivatedEvent(Guid contractId, string contractNumber, string name, Guid? organizationId)
            : base(organizationId)
        {
            ContractId = contractId;
            ContractNumber = contractNumber;
            Name = name;
        }
    }

    public class ContractSuspendedEvent : DomainEvent
    {
        public Guid ContractId { get; }
        public string ContractNumber { get; }
        public string Name { get; }
        public string Reason { get; }

        public ContractSuspendedEvent(Guid contractId, string contractNumber, string name, string reason, Guid? organizationId)
            : base(organizationId)
        {
            ContractId = contractId;
            ContractNumber = contractNumber;
            Name = name;
            Reason = reason;
        }
    }

    public class ContractTerminatedEvent : DomainEvent
    {
        public Guid ContractId { get; }
        public string ContractNumber { get; }
        public string Name { get; }
        public string Reason { get; }

        public ContractTerminatedEvent(Guid contractId, string contractNumber, string name, string reason, Guid? organizationId)
            : base(organizationId)
        {
            ContractId = contractId;
            ContractNumber = contractNumber;
            Name = name;
            Reason = reason;
        }
    }

    public class ContractExpiredEvent : DomainEvent
    {
        public Guid ContractId { get; }
        public string ContractNumber { get; }
        public string Name { get; }

        public ContractExpiredEvent(Guid contractId, string contractNumber, string name, Guid? organizationId)
            : base(organizationId)
        {
            ContractId = contractId;
            ContractNumber = contractNumber;
            Name = name;
        }
    }
}
