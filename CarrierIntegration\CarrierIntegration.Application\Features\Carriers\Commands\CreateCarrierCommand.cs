using AutoMapper;
using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.Enums;
using CarrierIntegration.Domain.ValueObjects;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Features.Carriers.Commands
{
    public class CreateCarrierCommand : IRequest<ApiResponseDto<CarrierDto>>
    {
        public Guid OrganizationId { get; set; }
        public string Name { get; set; } = null!;
        public string Code { get; set; } = null!;
        public CarrierType Type { get; set; }
        public string? Description { get; set; }
        public string? Website { get; set; }
        public ContactDto PrimaryContact { get; set; } = null!;
        public AddressDto HeadquartersAddress { get; set; } = null!;
        public string? ApiEndpoint { get; set; }
        public string? ApiVersion { get; set; }
        public string? TimeZone { get; set; }
        public string? OperatingHours { get; set; }
        public bool SupportsTracking { get; set; }
        public bool SupportsRating { get; set; }
        public bool SupportsLabeling { get; set; }
        public bool SupportsPickup { get; set; }
        public bool SupportsDeliveryConfirmation { get; set; }
        public bool SupportsInternational { get; set; }
        public bool RequiresAccount { get; set; } = true;
        public decimal? MaxWeight { get; set; }
        public string? MaxWeightUnit { get; set; }
        public decimal? MaxDimensions { get; set; }
        public string? MaxDimensionsUnit { get; set; }
        public int Priority { get; set; } = 100;
        public string CreatedBy { get; set; } = "System";
    }

    public class CreateCarrierCommandValidator : AbstractValidator<CreateCarrierCommand>
    {
        private readonly IUnitOfWork _unitOfWork;

        public CreateCarrierCommandValidator(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

            RuleFor(x => x.OrganizationId)
                .NotEmpty()
                .WithMessage("Organization ID is required");

            RuleFor(x => x.Name)
                .NotEmpty()
                .WithMessage("Carrier name is required")
                .MaximumLength(200)
                .WithMessage("Carrier name cannot exceed 200 characters");

            RuleFor(x => x.Code)
                .NotEmpty()
                .WithMessage("Carrier code is required")
                .MaximumLength(50)
                .WithMessage("Carrier code cannot exceed 50 characters")
                .MustAsync(BeUniqueCode)
                .WithMessage("Carrier code already exists in this organization");

            RuleFor(x => x.Type)
                .IsInEnum()
                .WithMessage("Invalid carrier type");

            RuleFor(x => x.PrimaryContact)
                .NotNull()
                .WithMessage("Primary contact is required");

            RuleFor(x => x.PrimaryContact.Name)
                .NotEmpty()
                .When(x => x.PrimaryContact != null)
                .WithMessage("Primary contact name is required");

            RuleFor(x => x.HeadquartersAddress)
                .NotNull()
                .WithMessage("Headquarters address is required");

            RuleFor(x => x.HeadquartersAddress.Street1)
                .NotEmpty()
                .When(x => x.HeadquartersAddress != null)
                .WithMessage("Street address is required");

            RuleFor(x => x.HeadquartersAddress.City)
                .NotEmpty()
                .When(x => x.HeadquartersAddress != null)
                .WithMessage("City is required");

            RuleFor(x => x.HeadquartersAddress.State)
                .NotEmpty()
                .When(x => x.HeadquartersAddress != null)
                .WithMessage("State is required");

            RuleFor(x => x.HeadquartersAddress.PostalCode)
                .NotEmpty()
                .When(x => x.HeadquartersAddress != null)
                .WithMessage("Postal code is required");

            RuleFor(x => x.HeadquartersAddress.Country)
                .NotEmpty()
                .When(x => x.HeadquartersAddress != null)
                .WithMessage("Country is required");

            RuleFor(x => x.Priority)
                .InclusiveBetween(1, 1000)
                .WithMessage("Priority must be between 1 and 1000");

            RuleFor(x => x.MaxWeight)
                .GreaterThan(0)
                .When(x => x.MaxWeight.HasValue)
                .WithMessage("Max weight must be greater than zero");

            RuleFor(x => x.MaxDimensions)
                .GreaterThan(0)
                .When(x => x.MaxDimensions.HasValue)
                .WithMessage("Max dimensions must be greater than zero");

            RuleFor(x => x.Website)
                .Must(BeValidUrl)
                .When(x => !string.IsNullOrWhiteSpace(x.Website))
                .WithMessage("Website must be a valid URL");

            RuleFor(x => x.ApiEndpoint)
                .Must(BeValidUrl)
                .When(x => !string.IsNullOrWhiteSpace(x.ApiEndpoint))
                .WithMessage("API endpoint must be a valid URL");
        }

        private async Task<bool> BeUniqueCode(CreateCarrierCommand command, string code, CancellationToken cancellationToken)
        {
            return !await _unitOfWork.Carriers.CodeExistsAsync(code, command.OrganizationId);
        }

        private static bool BeValidUrl(string? url)
        {
            if (string.IsNullOrWhiteSpace(url))
                return true;

            return Uri.TryCreate(url, UriKind.Absolute, out var result) &&
                   (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
        }
    }

    public class CreateCarrierCommandHandler : IRequestHandler<CreateCarrierCommand, ApiResponseDto<CarrierDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<CreateCarrierCommandHandler> _logger;

        public CreateCarrierCommandHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<CreateCarrierCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ApiResponseDto<CarrierDto>> Handle(CreateCarrierCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Creating carrier {CarrierName} with code {CarrierCode} for organization {OrganizationId}",
                    request.Name, request.Code, request.OrganizationId);

                // Create value objects
                var primaryContact = new ContactInfo(
                    request.PrimaryContact.Name,
                    request.PrimaryContact.Email,
                    request.PrimaryContact.Phone,
                    request.PrimaryContact.Title,
                    request.PrimaryContact.Department);

                var headquartersAddress = new Address(
                    request.HeadquartersAddress.Street1,
                    request.HeadquartersAddress.City,
                    request.HeadquartersAddress.State,
                    request.HeadquartersAddress.PostalCode,
                    request.HeadquartersAddress.Country,
                    request.HeadquartersAddress.Street2,
                    request.HeadquartersAddress.Latitude,
                    request.HeadquartersAddress.Longitude);

                // Create carrier entity
                var carrier = new Carrier(
                    request.OrganizationId,
                    request.Name,
                    request.Code,
                    request.Type,
                    primaryContact,
                    headquartersAddress,
                    request.CreatedBy);

                // Update optional properties
                carrier.UpdateBasicInfo(
                    request.Name,
                    request.Description,
                    request.Website,
                    primaryContact,
                    headquartersAddress,
                    request.CreatedBy);

                carrier.UpdateApiConfiguration(
                    request.ApiEndpoint,
                    request.ApiVersion,
                    request.TimeZone,
                    request.OperatingHours,
                    request.CreatedBy);

                carrier.UpdateCapabilities(
                    request.SupportsTracking,
                    request.SupportsRating,
                    request.SupportsLabeling,
                    request.SupportsPickup,
                    request.SupportsDeliveryConfirmation,
                    request.SupportsInternational,
                    request.RequiresAccount,
                    request.CreatedBy);

                carrier.UpdateLimitations(
                    request.MaxWeight,
                    request.MaxWeightUnit,
                    request.MaxDimensions,
                    request.MaxDimensionsUnit,
                    request.CreatedBy);

                carrier.SetPriority(request.Priority, request.CreatedBy);

                // Save to database
                await _unitOfWork.Carriers.AddAsync(carrier);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully created carrier {CarrierId} with code {CarrierCode}",
                    carrier.Id, carrier.Code);

                // Map to DTO and return
                var carrierDto = _mapper.Map<CarrierDto>(carrier);
                return ApiResponseDto<CarrierDto>.SuccessResult(carrierDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating carrier {CarrierName} with code {CarrierCode}",
                    request.Name, request.Code);

                return ApiResponseDto<CarrierDto>.ErrorResult($"Failed to create carrier: {ex.Message}");
            }
        }
    }
}
