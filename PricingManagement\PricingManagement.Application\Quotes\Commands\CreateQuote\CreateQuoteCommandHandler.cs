using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Common;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Services;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Repositories;
using PricingManagement.Domain.ValueObjects;
using System.Text.Json;

namespace PricingManagement.Application.Quotes.Commands.CreateQuote
{
    public class CreateQuoteCommandHandler : IRequestHandler<CreateQuoteCommand, OperationResultDto<QuoteDto>>
    {
        private readonly IQuoteRepository _quoteRepository;
        private readonly IPricingEngine _pricingEngine;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILogger<CreateQuoteCommandHandler> _logger;

        public CreateQuoteCommandHandler(
            IQuoteRepository quoteRepository,
            IPricingEngine pricingEngine,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILogger<CreateQuoteCommandHandler> logger)
        {
            _quoteRepository = quoteRepository;
            _pricingEngine = pricingEngine;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<OperationResultDto<QuoteDto>> Handle(CreateQuoteCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_currentUserService.OrganizationId.HasValue)
                {
                    return OperationResultDto<QuoteDto>.Failure("Organization context is required");
                }

                var organizationId = _currentUserService.OrganizationId.Value;
                var currentUser = _currentUserService.Username ?? "System";

                // Generate quote number
                var quoteNumber = await _quoteRepository.GenerateQuoteNumberAsync(organizationId);

                // Create quote entity
                var quote = new Quote(
                    quoteNumber,
                    request.ExpirationDate,
                    request.Currency,
                    organizationId,
                    currentUser);

                // Set customer and shipper information
                if (request.CustomerId.HasValue && !string.IsNullOrWhiteSpace(request.CustomerName))
                {
                    quote.SetCustomer(request.CustomerId.Value, request.CustomerName, currentUser);
                }

                if (request.ShipperId.HasValue && !string.IsNullOrWhiteSpace(request.ShipperName))
                {
                    quote.SetShipper(request.ShipperId.Value, request.ShipperName, currentUser);
                }

                // Set shipment details
                var packageWeight = new Weight(request.PackageWeight.Value, request.PackageWeight.Unit);
                Dimensions? packageDimensions = null;
                
                if (request.PackageDimensions != null)
                {
                    packageDimensions = new Dimensions(
                        request.PackageDimensions.Length,
                        request.PackageDimensions.Width,
                        request.PackageDimensions.Height,
                        request.PackageDimensions.Unit);
                }

                var specialServices = request.SpecialServices.Any() 
                    ? JsonSerializer.Serialize(request.SpecialServices) 
                    : null;

                quote.SetShipmentDetails(
                    request.OriginAddress,
                    request.DestinationAddress,
                    request.OriginZoneId,
                    request.DestinationZoneId,
                    request.Distance,
                    request.ServiceType,
                    packageWeight,
                    packageDimensions,
                    request.DeclaredValue,
                    specialServices,
                    currentUser);

                // Set notes and terms
                if (!string.IsNullOrWhiteSpace(request.Notes))
                {
                    quote.SetNotes(request.Notes, currentUser);
                }

                if (!string.IsNullOrWhiteSpace(request.Terms))
                {
                    quote.SetTerms(request.Terms, currentUser);
                }

                // Add line items
                foreach (var lineItem in request.LineItems)
                {
                    var unitPrice = new Money(lineItem.UnitPrice.Amount, lineItem.UnitPrice.Currency);
                    quote.AddLineItem(lineItem.Description, lineItem.Quantity, unitPrice, lineItem.ItemType, currentUser);
                }

                // Auto-calculate rates if requested
                if (request.AutoCalculateRates)
                {
                    await CalculateAndApplyRates(quote, request, currentUser);
                }

                // Save quote
                await _quoteRepository.AddAsync(quote);

                // Map to DTO
                var quoteDto = _mapper.Map<QuoteDto>(quote);

                _logger.LogInformation("Created quote {QuoteNumber} for organization {OrganizationId}",
                    quote.QuoteNumber, organizationId);

                return OperationResultDto<QuoteDto>.Success(quoteDto, "Quote created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating quote");
                return OperationResultDto<QuoteDto>.Failure("An error occurred while creating the quote");
            }
        }

        private async Task CalculateAndApplyRates(Quote quote, CreateQuoteCommand request, string currentUser)
        {
            try
            {
                // Create rate calculation request
                var rateRequest = new RateCalculationRequestDto
                {
                    OrganizationId = quote.OrganizationId,
                    CustomerId = request.CustomerId,
                    ShipperId = request.ShipperId,
                    ServiceType = request.ServiceType,
                    OriginAddress = request.OriginAddress,
                    DestinationAddress = request.DestinationAddress,
                    OriginZoneId = request.OriginZoneId,
                    DestinationZoneId = request.DestinationZoneId,
                    Distance = request.Distance,
                    PackageWeight = new Weight(request.PackageWeight.Value, request.PackageWeight.Unit),
                    PackageDimensions = request.PackageDimensions != null 
                        ? new Dimensions(request.PackageDimensions.Length, request.PackageDimensions.Width, 
                                       request.PackageDimensions.Height, request.PackageDimensions.Unit)
                        : null,
                    DeclaredValue = request.DeclaredValue,
                    SpecialServices = request.SpecialServices,
                    Currency = request.Currency,
                    ShipDate = DateTime.UtcNow
                };

                // Calculate pricing
                var pricingResult = await _pricingEngine.CalculateRateAsync(rateRequest);

                // Apply base rate
                quote.SetBaseRate(pricingResult.BaseRate, currentUser);

                // Apply surcharges
                foreach (var surcharge in pricingResult.Surcharges)
                {
                    quote.AddSurcharge(
                        surcharge.SurchargeType,
                        surcharge.Description,
                        surcharge.Amount,
                        surcharge.CalculationBasis,
                        currentUser);
                }

                // Apply discounts
                foreach (var discount in pricingResult.Discounts)
                {
                    quote.AddDiscount(
                        discount.DiscountType,
                        discount.Description,
                        discount.Amount,
                        discount.Percentage,
                        discount.CalculationBasis,
                        currentUser);
                }

                // Apply taxes
                foreach (var tax in pricingResult.Taxes)
                {
                    quote.AddTax(
                        tax.TaxType,
                        tax.Description,
                        tax.Amount,
                        tax.Rate,
                        tax.Jurisdiction,
                        currentUser);
                }

                _logger.LogInformation("Applied calculated rates to quote {QuoteNumber}. Total: {TotalAmount}",
                    quote.QuoteNumber, quote.TotalAmount);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to auto-calculate rates for quote {QuoteNumber}", quote.QuoteNumber);
                // Continue without rates - user can manually add them later
            }
        }
    }
}
