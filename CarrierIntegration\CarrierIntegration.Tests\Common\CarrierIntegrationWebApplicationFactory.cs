using CarrierIntegration.Infrastructure.Persistence;
using CarrierIntegration.Infrastructure.Services;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using Testcontainers.PostgreSql;

namespace CarrierIntegration.Tests.Common
{
    public class CarrierIntegrationWebApplicationFactory : WebApplicationFactory<Program>, IAsyncLifetime
    {
        private readonly PostgreSqlContainer _dbContainer = new PostgreSqlBuilder()
            .WithImage("postgres:15")
            .WithDatabase("tritrackz_carrier_integration_test")
            .WithUsername("test")
            .WithPassword("test")
            .WithCleanUp(true)
            .Build();

        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing DbContext registration
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<CarrierIntegrationDbContext>));
                if (descriptor != null)
                {
                    services.Remove(descriptor);
                }

                // Add test database
                services.AddDbContext<CarrierIntegrationDbContext>(options =>
                {
                    options.UseNpgsql(_dbContainer.GetConnectionString());
                });

                // Remove hosted services for testing
                var hostedServices = services.Where(d => d.ServiceType.Name.Contains("HostedService")).ToList();
                foreach (var service in hostedServices)
                {
                    services.Remove(service);
                }

                // Build service provider and ensure database is created
                var serviceProvider = services.BuildServiceProvider();
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<CarrierIntegrationDbContext>();
                context.Database.EnsureCreated();
            });

            builder.UseEnvironment("Testing");
        }

        public async Task InitializeAsync()
        {
            await _dbContainer.StartAsync();
        }

        public new async Task DisposeAsync()
        {
            await _dbContainer.StopAsync();
            await base.DisposeAsync();
        }

        public async Task<Guid> SeedTestDataAsync(Guid organizationId)
        {
            using var scope = Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<CarrierIntegrationDbContext>();
            var seedingService = scope.ServiceProvider.GetRequiredService<IDataSeedingService>();

            // Seed default carriers
            await seedingService.SeedDefaultCarriersAsync(organizationId);

            // Get the first carrier ID for testing
            var carrier = await context.Carriers
                .Where(c => c.OrganizationId == organizationId)
                .FirstOrDefaultAsync();

            if (carrier != null)
            {
                // Seed services and accounts for the carrier
                await seedingService.SeedCarrierServicesAsync(carrier.Id);
                await seedingService.SeedCarrierAccountsAsync(carrier.Id, organizationId);
                return carrier.Id;
            }

            throw new InvalidOperationException("No carriers were seeded");
        }

        public async Task CleanupTestDataAsync(Guid organizationId)
        {
            using var scope = Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<CarrierIntegrationDbContext>();

            // Remove all test data for the organization
            var carriers = await context.Carriers
                .Where(c => c.OrganizationId == organizationId)
                .ToListAsync();

            context.Carriers.RemoveRange(carriers);
            await context.SaveChangesAsync();
        }
    }
}
