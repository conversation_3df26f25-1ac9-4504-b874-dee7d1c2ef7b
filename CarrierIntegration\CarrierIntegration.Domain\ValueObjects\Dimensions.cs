using CarrierIntegration.Domain.Common;
using System;
using System.Collections.Generic;

namespace CarrierIntegration.Domain.ValueObjects
{
    public class Dimensions : ValueObject
    {
        public decimal Length { get; private set; }
        public decimal Width { get; private set; }
        public decimal Height { get; private set; }
        public string Unit { get; private set; } // IN, CM

        private Dimensions() { } // For EF Core

        public Dimensions(decimal length, decimal width, decimal height, string unit = "IN")
        {
            if (length <= 0)
                throw new DomainException("Length must be greater than zero");
            if (width <= 0)
                throw new DomainException("Width must be greater than zero");
            if (height <= 0)
                throw new DomainException("Height must be greater than zero");
            if (string.IsNullOrWhiteSpace(unit))
                throw new DomainException("Unit is required");

            var validUnits = new[] { "IN", "CM", "FT", "M" };
            if (!Array.Exists(validUnits, u => u.Equals(unit, StringComparison.OrdinalIgnoreCase)))
                throw new DomainException($"Invalid unit. Valid units are: {string.Join(", ", validUnits)}");

            Length = Math.Round(length, 2);
            Width = Math.Round(width, 2);
            Height = Math.Round(height, 2);
            Unit = unit.ToUpperInvariant();
        }

        public decimal GetVolume()
        {
            return Length * Width * Height;
        }

        public decimal GetLongestSide()
        {
            return Math.Max(Math.Max(Length, Width), Height);
        }

        public decimal GetGirth()
        {
            // Girth = 2 * (Width + Height)
            return 2 * (Width + Height);
        }

        public decimal GetLengthPlusGirth()
        {
            return Length + GetGirth();
        }

        public Dimensions ConvertTo(string targetUnit)
        {
            if (Unit.Equals(targetUnit, StringComparison.OrdinalIgnoreCase))
                return this;

            var conversionFactor = GetConversionFactor(Unit, targetUnit);
            return new Dimensions(
                Length * conversionFactor,
                Width * conversionFactor,
                Height * conversionFactor,
                targetUnit);
        }

        private static decimal GetConversionFactor(string fromUnit, string toUnit)
        {
            var from = fromUnit.ToUpperInvariant();
            var to = toUnit.ToUpperInvariant();

            return (from, to) switch
            {
                ("IN", "CM") => 2.54m,
                ("CM", "IN") => 0.393701m,
                ("FT", "IN") => 12m,
                ("IN", "FT") => 0.0833333m,
                ("M", "CM") => 100m,
                ("CM", "M") => 0.01m,
                ("FT", "M") => 0.3048m,
                ("M", "FT") => 3.28084m,
                _ when from == to => 1m,
                _ => throw new DomainException($"Conversion from {from} to {to} is not supported")
            };
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Length;
            yield return Width;
            yield return Height;
            yield return Unit;
        }

        public override string ToString()
        {
            return $"{Length} x {Width} x {Height} {Unit}";
        }
    }
}
