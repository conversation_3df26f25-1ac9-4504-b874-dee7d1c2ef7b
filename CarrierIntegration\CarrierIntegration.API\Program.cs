using CarrierIntegration.Application;
using CarrierIntegration.Infrastructure;
using Shared.Messaging;
using Serilog;
using System.Text;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.AspNetCore.Rewrite;
var builder = WebApplication.CreateBuilder(args);

// Add Serilog
builder.Host.UseSerilog((context, configuration) =>
    configuration.ReadFrom.Configuration(context.Configuration));

// Add services to the container
builder.Services.AddApplication();
builder.Services.AddInfrastructure(builder.Configuration);
builder.Services.AddMessaging(builder.Configuration.GetValue<string>("RabbitMQ:Host", "localhost"));

// Add controllers
builder.Services.AddControllers();

// Add API versioning
builder.Services.AddApiVersioning(opt =>
{
    opt.DefaultApiVersion = new Asp.Versioning.ApiVersion(1, 0);
    opt.AssumeDefaultVersionWhenUnspecified = true;
    opt.ApiVersionReader = Asp.Versioning.ApiVersionReader.Combine(
        new Asp.Versioning.QueryStringApiVersionReader("version"),
        new Asp.Versioning.HeaderApiVersionReader("X-Version"),
        new Asp.Versioning.UrlSegmentApiVersionReader()
    );
}).AddMvc();

// Add Swagger/OpenAPI
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "TriTrackz Carrier Integration API",
        Version = "v1",
        Description = "Comprehensive carrier integration service for the TriTrackz logistics platform",
        Contact = new Microsoft.OpenApi.Models.OpenApiContact
        {
            Name = "TriTrackz Support",
            Email = "<EMAIL>"
        }
    });

    // Add JWT authentication to Swagger
    c.AddSecurityDefinition("Bearer", new Microsoft.OpenApi.Models.OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = Microsoft.OpenApi.Models.ParameterLocation.Header,
        Type = Microsoft.OpenApi.Models.SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new Microsoft.OpenApi.Models.OpenApiSecurityRequirement
    {
        {
            new Microsoft.OpenApi.Models.OpenApiSecurityScheme
            {
                Reference = new Microsoft.OpenApi.Models.OpenApiReference
                {
                    Type = Microsoft.OpenApi.Models.ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });

    // Include XML comments
    var xmlFile = $"{System.Reflection.Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// Add JWT Authentication
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var key = Encoding.ASCII.GetBytes(jwtSettings["Secret"] ?? "ThisIsAVerySecureKeyThatShouldBeStoredInASecureVault");

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.RequireHttpsMetadata = false;
    options.SaveToken = true;
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidIssuer = jwtSettings["Issuer"] ?? "TriTrackzIdentity",
        ValidAudience = jwtSettings["Audience"] ?? "TriTrackzServices",
        ClockSkew = TimeSpan.Zero
    };
});

// Add Authorization
builder.Services.AddAuthorization();

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add Health Checks
builder.Services.AddHealthChecks()
    .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy());

// Add Application Insights
builder.Services.AddApplicationInsightsTelemetry();

// Add HTTP clients for external carrier APIs
builder.Services.AddHttpClient("UPS", client =>
{
    var upsSettings = builder.Configuration.GetSection("CarrierApiSettings:UPS");
    client.BaseAddress = new Uri(upsSettings["BaseUrl"] ?? "https://onlinetools.ups.com/api");
    client.Timeout = TimeSpan.FromSeconds(upsSettings.GetValue<int>("TimeoutSeconds", 30));
});

builder.Services.AddHttpClient("FedEx", client =>
{
    var fedexSettings = builder.Configuration.GetSection("CarrierApiSettings:FedEx");
    client.BaseAddress = new Uri(fedexSettings["BaseUrl"] ?? "https://apis.fedex.com");
    client.Timeout = TimeSpan.FromSeconds(fedexSettings.GetValue<int>("TimeoutSeconds", 30));
});

builder.Services.AddHttpClient("USPS", client =>
{
    var uspsSettings = builder.Configuration.GetSection("CarrierApiSettings:USPS");
    client.BaseAddress = new Uri(uspsSettings["BaseUrl"] ?? "https://api.usps.com");
    client.Timeout = TimeSpan.FromSeconds(uspsSettings.GetValue<int>("TimeoutSeconds", 30));
});

builder.Services.AddHttpClient("DHL", client =>
{
    var dhlSettings = builder.Configuration.GetSection("CarrierApiSettings:DHL");
    client.BaseAddress = new Uri(dhlSettings["BaseUrl"] ?? "https://api-eu.dhl.com");
    client.Timeout = TimeSpan.FromSeconds(dhlSettings.GetValue<int>("TimeoutSeconds", 30));
});

var app = builder.Build();
var options = new RewriteOptions()
    .AddRewrite("^$", "swagger", true)
    .AddRewrite("^swagger$", "swagger/index.html", true);

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "TriTrackz Carrier Integration API v1");
        c.RoutePrefix = "swagger";
    });
}

app.UseHttpsRedirection();

app.UseCors("AllowAll");

app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

// Add health check endpoint
app.MapHealthChecks("/health");

// Add custom middleware
app.UseMiddleware<CarrierIntegration.API.Middleware.ExceptionHandlingMiddleware>();
app.UseMiddleware<CarrierIntegration.API.Middleware.RequestLoggingMiddleware>();

app.Run();

public partial class Program { } // For testing
