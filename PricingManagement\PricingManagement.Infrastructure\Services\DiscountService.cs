using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Services;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.Repositories;
using PricingManagement.Domain.ValueObjects;

namespace PricingManagement.Infrastructure.Services
{
    public class DiscountService : IDiscountService
    {
        private readonly IContractRepository _contractRepository;
        private readonly IConfiguration _configuration;
        private readonly ILogger<DiscountService> _logger;

        public DiscountService(
            IContractRepository contractRepository,
            IConfiguration configuration,
            ILogger<DiscountService> logger)
        {
            _contractRepository = contractRepository;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<IEnumerable<DiscountDto>> CalculateDiscountsAsync(RateCalculationRequestDto request, Money baseAmount)
        {
            var discounts = new List<DiscountDto>();

            try
            {
                // Calculate volume discount
                var volumeDiscount = await CalculateVolumeDiscountAsync(request, baseAmount);
                if (volumeDiscount.Amount.Amount > 0)
                    discounts.Add(volumeDiscount);

                // Calculate customer-specific discount
                var customerDiscount = await CalculateCustomerDiscountAsync(request, baseAmount);
                if (customerDiscount.Amount.Amount > 0)
                    discounts.Add(customerDiscount);

                // Calculate promotional discount
                var promotionalDiscount = await CalculatePromotionalDiscountAsync(request, baseAmount);
                if (promotionalDiscount.Amount.Amount > 0)
                    discounts.Add(promotionalDiscount);

                // Calculate early payment discount
                var earlyPaymentDiscount = await CalculateEarlyPaymentDiscountAsync(request, baseAmount);
                if (earlyPaymentDiscount.Amount.Amount > 0)
                    discounts.Add(earlyPaymentDiscount);

                return discounts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating discounts for organization {OrganizationId}", request.OrganizationId);
                return new List<DiscountDto>();
            }
        }

        public async Task<DiscountDto> CalculateVolumeDiscountAsync(RateCalculationRequestDto request, Money baseAmount)
        {
            var discount = new DiscountDto
            {
                DiscountType = DiscountType.VolumeDiscount,
                Description = "Volume Discount",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                // Check if customer has an active contract with volume discounts
                if (request.CustomerId.HasValue)
                {
                    var contract = await _contractRepository.GetActiveContractForCustomerAsync(request.CustomerId.Value, request.OrganizationId);
                    if (contract != null)
                    {
                        var applicableDiscount = contract.Discounts
                            .Where(d => d.DiscountType == DiscountType.VolumeDiscount && d.IsApplicable(DateTime.UtcNow, null))
                            .OrderByDescending(d => d.Percentage ?? 0)
                            .FirstOrDefault();

                        if (applicableDiscount != null)
                        {
                            discount.Amount = applicableDiscount.Percentage.HasValue
                                ? baseAmount.ApplyPercentage(applicableDiscount.Percentage.Value)
                                : applicableDiscount.FixedAmount ?? Money.Zero(request.Currency);
                            
                            discount.Percentage = applicableDiscount.Percentage;
                            discount.CalculationBasis = "Contract Volume Discount";
                        }
                    }
                }

                // Apply standard volume discount tiers if no contract discount
                if (discount.Amount.Amount == 0)
                {
                    var weight = request.PackageWeight.Value;
                    var volumeDiscountPercentage = weight switch
                    {
                        >= 1000 => 15m,
                        >= 500 => 10m,
                        >= 100 => 5m,
                        >= 50 => 2m,
                        _ => 0m
                    };

                    if (volumeDiscountPercentage > 0)
                    {
                        discount.Amount = baseAmount.ApplyPercentage(volumeDiscountPercentage);
                        discount.Percentage = volumeDiscountPercentage;
                        discount.CalculationBasis = "Standard Volume Discount";
                    }
                }

                return discount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating volume discount");
                return discount;
            }
        }

        public async Task<DiscountDto> CalculateCustomerDiscountAsync(RateCalculationRequestDto request, Money baseAmount)
        {
            var discount = new DiscountDto
            {
                DiscountType = DiscountType.Loyalty,
                Description = "Customer Loyalty Discount",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                if (request.CustomerId.HasValue)
                {
                    // TODO: Integrate with Customer Management to get customer tier/loyalty level
                    // For now, apply a simple customer discount based on customer segment
                    var customerDiscountPercentage = request.CustomerSegment?.ToLower() switch
                    {
                        "premium" => 10m,
                        "gold" => 7m,
                        "silver" => 5m,
                        "bronze" => 2m,
                        _ => 0m
                    };

                    if (customerDiscountPercentage > 0)
                    {
                        discount.Amount = baseAmount.ApplyPercentage(customerDiscountPercentage);
                        discount.Percentage = customerDiscountPercentage;
                        discount.CalculationBasis = $"Customer Segment: {request.CustomerSegment}";
                    }
                }

                return discount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating customer discount");
                return discount;
            }
        }

        public async Task<DiscountDto> CalculatePromotionalDiscountAsync(RateCalculationRequestDto request, Money baseAmount, string? promotionCode = null)
        {
            var discount = new DiscountDto
            {
                DiscountType = DiscountType.Promotional,
                Description = "Promotional Discount",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                // TODO: Implement promotion code validation and discount application
                // This would integrate with a promotion management system

                // For now, apply seasonal discounts
                var currentMonth = DateTime.UtcNow.Month;
                var seasonalDiscountPercentage = currentMonth switch
                {
                    12 or 1 or 2 => 5m, // Winter promotion
                    6 or 7 or 8 => 3m,  // Summer promotion
                    _ => 0m
                };

                if (seasonalDiscountPercentage > 0)
                {
                    discount.Amount = baseAmount.ApplyPercentage(seasonalDiscountPercentage);
                    discount.Percentage = seasonalDiscountPercentage;
                    discount.CalculationBasis = "Seasonal Promotion";
                }

                return discount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating promotional discount");
                return discount;
            }
        }

        public async Task<DiscountDto> CalculateEarlyPaymentDiscountAsync(RateCalculationRequestDto request, Money baseAmount)
        {
            var discount = new DiscountDto
            {
                DiscountType = DiscountType.EarlyPayment,
                Description = "Early Payment Discount",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                // Apply early payment discount if customer has good payment history
                // TODO: Integrate with billing system to check payment history
                
                var earlyPaymentDiscountPercentage = _configuration.GetValue<decimal>("PricingSettings:EarlyPaymentDiscountPercentage", 2m);
                
                if (earlyPaymentDiscountPercentage > 0)
                {
                    discount.Amount = baseAmount.ApplyPercentage(earlyPaymentDiscountPercentage);
                    discount.Percentage = earlyPaymentDiscountPercentage;
                    discount.CalculationBasis = "Early Payment Terms";
                }

                return discount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating early payment discount");
                return discount;
            }
        }

        public async Task<bool> ValidateDiscountEligibilityAsync(RateCalculationRequestDto request, DiscountDto discount)
        {
            try
            {
                // Validate discount eligibility based on business rules
                var maxDiscountPercentage = _configuration.GetValue<decimal>("PricingSettings:MaxDiscountPercentage", 50m);
                
                if (discount.Percentage.HasValue && discount.Percentage > maxDiscountPercentage)
                {
                    _logger.LogWarning("Discount percentage {Percentage}% exceeds maximum allowed {MaxPercentage}%", 
                        discount.Percentage, maxDiscountPercentage);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating discount eligibility");
                return false;
            }
        }

        public async Task<bool> RequiresApprovalAsync(DiscountDto discount, Money baseAmount)
        {
            try
            {
                var approvalThreshold = _configuration.GetValue<decimal>("PricingSettings:RequireApprovalThreshold", 1000m);
                var approvalPercentageThreshold = _configuration.GetValue<decimal>("PricingSettings:RequireApprovalPercentageThreshold", 20m);

                // Require approval for large discount amounts
                if (discount.Amount.Amount > approvalThreshold)
                    return true;

                // Require approval for high discount percentages
                if (discount.Percentage.HasValue && discount.Percentage > approvalPercentageThreshold)
                    return true;

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking discount approval requirement");
                return true; // Default to requiring approval on error
            }
        }
    }
}
