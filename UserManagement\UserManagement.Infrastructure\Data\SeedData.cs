using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using UserManagement.Application.Common.Interfaces;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Enums;
using UserManagement.Domain.Repositories;
using UserManagement.Domain.ValueObjects;

namespace UserManagement.Infrastructure.Data
{
    public class SeedData
    {
        private readonly ISubscriptionPlanRepository _subscriptionPlanRepository;
        private readonly IRoleRepository _roleRepository;
        private readonly IPermissionRepository _permissionRepository;
        private readonly ILogger<SeedData> _logger;

        public SeedData(
            ISubscriptionPlanRepository subscriptionPlanRepository,
            IRoleRepository roleRepository,
            IPermissionRepository permissionRepository,
            ILogger<SeedData> logger)
        {
            _subscriptionPlanRepository = subscriptionPlanRepository;
            _roleRepository = roleRepository;
            _permissionRepository = permissionRepository;
            _logger = logger;
        }

        public async Task SeedAsync()
        {
            try
            {
                _logger.LogInformation("Starting UserManagement service data seeding");

                await SeedPermissionsAsync();
                await SeedRolesAsync();
                await SeedSubscriptionPlansAsync();

                _logger.LogInformation("UserManagement service data seeding completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during UserManagement service data seeding");
                throw;
            }
        }

        private async Task SeedPermissionsAsync()
        {
            var permissions = new[]
            {
                new Permission("profile.read", "Read user profile", "Profile Management"),
                new Permission("profile.write", "Update user profile", "Profile Management"),
                new Permission("organization.manage", "Manage organization settings", "Organization"),
                new Permission("team.manage", "Manage teams", "Team Management"),
                new Permission("branch.manage", "Manage branches", "Branch Management"),
                new Permission("subscription.manage", "Manage subscriptions", "Subscription"),
                new Permission("reports.view", "View reports", "Reporting"),
                new Permission("gdpr.manage", "Manage GDPR compliance", "Compliance"),
                new Permission("kyc.manage", "Manage KYC verification", "Compliance")
            };

            foreach (var permission in permissions)
            {
                var existingPermission = await _permissionRepository.GetByNameAsync(permission.Name);
                if (existingPermission == null)
                {
                    await _permissionRepository.AddAsync(permission);
                    _logger.LogInformation("Created permission: {PermissionName}", permission.Name);
                }
            }
        }

        private async Task SeedRolesAsync()
        {
            var Id = new Guid();
            // Create Employee role
            var employeeRole = await _roleRepository.GetByNameAsync("Employee");
            if (employeeRole == null)
            {
                employeeRole = new Role(Id,"Employee", "Standard employee role", true);
                await _roleRepository.AddAsync(employeeRole);

                // Add basic permissions
                var employeePermissions = new[] { "profile.read", "profile.write" };
                foreach (var permissionName in employeePermissions)
                {
                    var permission = await _permissionRepository.GetByNameAsync(permissionName);
                    if (permission != null)
                    {
                        employeeRole.AddPermission(permission);
                    }
                }
                await _roleRepository.UpdateAsync(employeeRole);
                _logger.LogInformation("Created Employee role");
            }
            var manageRoleId = new Guid();
            // Create Manager role
            var managerRole = await _roleRepository.GetByNameAsync("Manager");
            if (managerRole == null)
            {
                managerRole = new Role(manageRoleId, "Manager", "Team manager role", true);
                await _roleRepository.AddAsync(managerRole);

                // Add manager permissions
                var managerPermissions = new[]
                {
                    "profile.read", "profile.write", "team.manage", "reports.view"
                };
                foreach (var permissionName in managerPermissions)
                {
                    var permission = await _permissionRepository.GetByNameAsync(permissionName);
                    if (permission != null)
                    {
                        managerRole.AddPermission(permission);
                    }
                }
                await _roleRepository.UpdateAsync(managerRole);
                _logger.LogInformation("Created Manager role");
            }
            var roleId = new Guid();
            // Create Admin role
            var adminRole = await _roleRepository.GetByNameAsync("Admin");
            if (adminRole == null)
            {
                adminRole = new Role(roleId, "Admin", "Organization administrator", true);
                await _roleRepository.AddAsync(adminRole);

                // Add all permissions to admin role
                var allPermissions = await _permissionRepository.GetAllAsync();
                foreach (var permission in allPermissions)
                {
                    adminRole.AddPermission(permission);
                }
                await _roleRepository.UpdateAsync(adminRole);
                _logger.LogInformation("Created Admin role with all permissions");
            }
        }

        private async Task SeedSubscriptionPlansAsync()
        {
            var plans = new[]
            {
                new SubscriptionPlan(
                    "Starter",
                    "Perfect for small teams getting started",
                    29.99m,
                    "USD",
                    5, // Max users
                    2, // Max teams
                    1, // Max branches
                    false, // IncludesAdvancedSecurity
                    false, // IncludesApiAccess
                    false  // IncludesPrioritySupport
                ),
                new SubscriptionPlan(
                    "Professional",
                    "Ideal for growing businesses",
                    99.99m,
                    "USD",
                    25, // Max users
                    10, // Max teams
                    5,  // Max branches
                    true,  // IncludesAdvancedSecurity
                    true,  // IncludesApiAccess
                    true   // IncludesPrioritySupport
                ),
                new SubscriptionPlan(
                    "Enterprise",
                    "For large organizations with advanced needs",
                    299.99m,
                    "USD",
                    -1, // Unlimited users
                    -1, // Unlimited teams
                    -1, // Unlimited branches
                    true, // IncludesAdvancedSecurity
                    true, // IncludesApiAccess
                    true  // IncludesPrioritySupport
                )
            };

            foreach (var plan in plans)
            {
                var existingPlan = await _subscriptionPlanRepository.GetByNameAsync(plan.Name);
                if (existingPlan == null)
                {
                    await _subscriptionPlanRepository.AddAsync(plan);
                    _logger.LogInformation("Created subscription plan: {PlanName}", plan.Name);
                }
            }
        }
    }
}
