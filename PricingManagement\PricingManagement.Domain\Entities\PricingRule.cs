using PricingManagement.Domain.Common;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.Events;
using PricingManagement.Domain.Exceptions;
using PricingManagement.Domain.ValueObjects;
using System;
using System.Collections.Generic;

namespace PricingManagement.Domain.Entities
{
    public class PricingRule : BaseEntity
    {
        public string Name { get; private set; }
        public string Description { get; private set; }
        public PricingRuleType RuleType { get; private set; }
        public CalculationMethod CalculationMethod { get; private set; }
        public PricingStatus Status { get; private set; }
        public int Priority { get; private set; }
        public DateTime EffectiveDate { get; private set; }
        public DateTime? ExpirationDate { get; private set; }
        public string? ServiceTypes { get; private set; } // JSON array of service type IDs
        public string? OriginZones { get; private set; } // JSON array of zone IDs
        public string? DestinationZones { get; private set; } // JSON array of zone IDs
        public string? CustomerSegments { get; private set; } // JSON array of customer segments
        public string? ShipperTypes { get; private set; } // JSON array of shipper types
        public decimal? MinWeight { get; private set; }
        public decimal? MaxWeight { get; private set; }
        public decimal? MinDistance { get; private set; }
        public decimal? MaxDistance { get; private set; }
        public decimal? MinValue { get; private set; }
        public decimal? MaxValue { get; private set; }
        public string RuleConfiguration { get; private set; } // JSON configuration
        public bool RequiresApproval { get; private set; }
        public string? ApprovalWorkflow { get; private set; }
        public string? Tags { get; private set; } // JSON array of tags

        private readonly List<PricingRuleTier> _tiers = new();
        public IReadOnlyCollection<PricingRuleTier> Tiers => _tiers.AsReadOnly();

        protected PricingRule() { }

        public PricingRule(
            string name,
            string description,
            PricingRuleType ruleType,
            CalculationMethod calculationMethod,
            int priority,
            DateTime effectiveDate,
            string ruleConfiguration,
            Guid organizationId,
            string createdBy) : base(organizationId, createdBy)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Pricing rule name cannot be empty");

            if (string.IsNullOrWhiteSpace(description))
                throw new DomainException("Pricing rule description cannot be empty");

            if (priority < 1)
                throw new DomainException("Priority must be greater than 0");

            if (effectiveDate < DateTime.UtcNow.Date)
                throw new DomainException("Effective date cannot be in the past");

            if (string.IsNullOrWhiteSpace(ruleConfiguration))
                throw new DomainException("Rule configuration cannot be empty");

            Name = name;
            Description = description;
            RuleType = ruleType;
            CalculationMethod = calculationMethod;
            Priority = priority;
            EffectiveDate = effectiveDate;
            RuleConfiguration = ruleConfiguration;
            Status = PricingStatus.Draft;
            RequiresApproval = false;

            AddDomainEvent(new PricingRuleCreatedEvent(Id, Name, RuleType, OrganizationId));
        }

        public void UpdateDetails(
            string name,
            string description,
            int priority,
            string ruleConfiguration,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Pricing rule name cannot be empty");

            if (string.IsNullOrWhiteSpace(description))
                throw new DomainException("Pricing rule description cannot be empty");

            if (priority < 1)
                throw new DomainException("Priority must be greater than 0");

            if (string.IsNullOrWhiteSpace(ruleConfiguration))
                throw new DomainException("Rule configuration cannot be empty");

            Name = name;
            Description = description;
            Priority = priority;
            RuleConfiguration = ruleConfiguration;
            Update(updatedBy);

            AddDomainEvent(new PricingRuleUpdatedEvent(Id, Name, OrganizationId));
        }

        public void SetEffectivePeriod(DateTime effectiveDate, DateTime? expirationDate, string updatedBy)
        {
            if (effectiveDate < DateTime.UtcNow.Date && Status == PricingStatus.Draft)
                throw new DomainException("Effective date cannot be in the past for draft rules");

            if (expirationDate.HasValue && expirationDate <= effectiveDate)
                throw new DomainException("Expiration date must be after effective date");

            EffectiveDate = effectiveDate;
            ExpirationDate = expirationDate;
            Update(updatedBy);
        }

        public void SetCriteria(
            string? serviceTypes,
            string? originZones,
            string? destinationZones,
            string? customerSegments,
            string? shipperTypes,
            decimal? minWeight,
            decimal? maxWeight,
            decimal? minDistance,
            decimal? maxDistance,
            decimal? minValue,
            decimal? maxValue,
            string updatedBy)
        {
            if (minWeight.HasValue && minWeight < 0)
                throw new DomainException("Minimum weight cannot be negative");

            if (maxWeight.HasValue && maxWeight < 0)
                throw new DomainException("Maximum weight cannot be negative");

            if (minWeight.HasValue && maxWeight.HasValue && minWeight > maxWeight)
                throw new DomainException("Minimum weight cannot be greater than maximum weight");

            if (minDistance.HasValue && minDistance < 0)
                throw new DomainException("Minimum distance cannot be negative");

            if (maxDistance.HasValue && maxDistance < 0)
                throw new DomainException("Maximum distance cannot be negative");

            if (minDistance.HasValue && maxDistance.HasValue && minDistance > maxDistance)
                throw new DomainException("Minimum distance cannot be greater than maximum distance");

            if (minValue.HasValue && minValue < 0)
                throw new DomainException("Minimum value cannot be negative");

            if (maxValue.HasValue && maxValue < 0)
                throw new DomainException("Maximum value cannot be negative");

            if (minValue.HasValue && maxValue.HasValue && minValue > maxValue)
                throw new DomainException("Minimum value cannot be greater than maximum value");

            ServiceTypes = serviceTypes;
            OriginZones = originZones;
            DestinationZones = destinationZones;
            CustomerSegments = customerSegments;
            ShipperTypes = shipperTypes;
            MinWeight = minWeight;
            MaxWeight = maxWeight;
            MinDistance = minDistance;
            MaxDistance = maxDistance;
            MinValue = minValue;
            MaxValue = maxValue;
            Update(updatedBy);
        }

        public void AddTier(decimal fromValue, decimal? toValue, decimal rate, string? rateType, string updatedBy)
        {
            if (fromValue < 0)
                throw new DomainException("From value cannot be negative");

            if (toValue.HasValue && toValue <= fromValue)
                throw new DomainException("To value must be greater than from value");

            if (rate < 0)
                throw new DomainException("Rate cannot be negative");

            var tier = new PricingRuleTier(fromValue, toValue, rate, rateType);
            _tiers.Add(tier);
            Update(updatedBy);
        }

        public void ClearTiers(string updatedBy)
        {
            _tiers.Clear();
            Update(updatedBy);
        }

        public void Activate(string updatedBy)
        {
            if (Status == PricingStatus.Active)
                throw new DomainException("Pricing rule is already active");

            if (DateTime.UtcNow < EffectiveDate)
                throw new DomainException("Cannot activate rule before effective date");

            if (ExpirationDate.HasValue && DateTime.UtcNow > ExpirationDate)
                throw new DomainException("Cannot activate expired rule");

            Status = PricingStatus.Active;
            Update(updatedBy);

            AddDomainEvent(new PricingRuleActivatedEvent(Id, Name, OrganizationId));
        }

        public void Deactivate(string updatedBy)
        {
            if (Status != PricingStatus.Active)
                throw new DomainException("Only active rules can be deactivated");

            Status = PricingStatus.Inactive;
            Update(updatedBy);

            AddDomainEvent(new PricingRuleDeactivatedEvent(Id, Name, OrganizationId));
        }

        public void Suspend(string reason, string updatedBy)
        {
            if (Status != PricingStatus.Active)
                throw new DomainException("Only active rules can be suspended");

            Status = PricingStatus.Suspended;
            Update(updatedBy);

            AddDomainEvent(new PricingRuleSuspendedEvent(Id, Name, reason, OrganizationId));
        }

        public void SetApprovalRequirement(bool requiresApproval, string? approvalWorkflow, string updatedBy)
        {
            RequiresApproval = requiresApproval;
            ApprovalWorkflow = approvalWorkflow;
            Update(updatedBy);
        }

        public void SetTags(string? tags, string updatedBy)
        {
            Tags = tags;
            Update(updatedBy);
        }

        public bool IsApplicable(DateTime date)
        {
            return Status == PricingStatus.Active &&
                   date >= EffectiveDate &&
                   (!ExpirationDate.HasValue || date <= ExpirationDate);
        }

        public bool IsExpired()
        {
            return ExpirationDate.HasValue && DateTime.UtcNow > ExpirationDate;
        }

        public void CheckAndUpdateExpiration()
        {
            if (IsExpired() && Status == PricingStatus.Active)
            {
                Status = PricingStatus.Expired;
                AddDomainEvent(new PricingRuleExpiredEvent(Id, Name, OrganizationId));
            }
        }
    }

    public class PricingRuleTier
    {
        public decimal FromValue { get; private set; }
        public decimal? ToValue { get; private set; }
        public decimal Rate { get; private set; }
        public string? RateType { get; private set; } // "fixed", "percentage", "per_unit"

        public PricingRuleTier(decimal fromValue, decimal? toValue, decimal rate, string? rateType)
        {
            FromValue = fromValue;
            ToValue = toValue;
            Rate = rate;
            RateType = rateType;
        }

        public bool IsInRange(decimal value)
        {
            return value >= FromValue && (!ToValue.HasValue || value < ToValue);
        }
    }
}
