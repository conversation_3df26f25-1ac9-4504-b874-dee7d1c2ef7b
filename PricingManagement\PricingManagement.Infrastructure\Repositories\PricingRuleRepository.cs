using Microsoft.EntityFrameworkCore;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.Repositories;
using PricingManagement.Infrastructure.Persistence;
using System.Text.Json;

namespace PricingManagement.Infrastructure.Repositories
{
    public class PricingRuleRepository : IPricingRuleRepository
    {
        private readonly PricingManagementDbContext _context;

        public PricingRuleRepository(PricingManagementDbContext context)
        {
            _context = context;
        }

        public async Task<PricingRule?> GetByIdAsync(Guid id)
        {
            return await _context.PricingRules
                .Include(x => x.Tiers)
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<PricingRule?> GetByIdAsync(Guid id, Guid organizationId)
        {
            return await _context.PricingRules
                .Include(x => x.Tiers)
                .FirstOrDefaultAsync(x => x.Id == id && x.OrganizationId == organizationId);
        }

        public async Task<IEnumerable<PricingRule>> GetByOrganizationAsync(Guid organizationId)
        {
            return await _context.PricingRules
                .Include(x => x.Tiers)
                .Where(x => x.OrganizationId == organizationId)
                .OrderBy(x => x.Priority)
                .ThenBy(x => x.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<PricingRule>> GetActiveRulesAsync(Guid organizationId)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.PricingRules
                .Include(x => x.Tiers)
                .Where(x => x.OrganizationId == organizationId &&
                           x.Status == PricingStatus.Active &&
                           x.EffectiveDate <= currentDate &&
                           (!x.ExpirationDate.HasValue || x.ExpirationDate > currentDate))
                .OrderBy(x => x.Priority)
                .ThenBy(x => x.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<PricingRule>> GetActiveRulesByTypeAsync(Guid organizationId, PricingRuleType ruleType)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.PricingRules
                .Include(x => x.Tiers)
                .Where(x => x.OrganizationId == organizationId &&
                           x.Status == PricingStatus.Active &&
                           x.RuleType == ruleType &&
                           x.EffectiveDate <= currentDate &&
                           (!x.ExpirationDate.HasValue || x.ExpirationDate > currentDate))
                .OrderBy(x => x.Priority)
                .ThenBy(x => x.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<PricingRule>> GetApplicableRulesAsync(
            Guid organizationId,
            string? serviceType = null,
            string? originZone = null,
            string? destinationZone = null,
            decimal? weight = null,
            decimal? distance = null,
            decimal? value = null,
            string? customerSegment = null,
            string? shipperType = null)
        {
            var currentDate = DateTime.UtcNow;
            var query = _context.PricingRules
                .Include(x => x.Tiers)
                .Where(x => x.OrganizationId == organizationId &&
                           x.Status == PricingStatus.Active &&
                           x.EffectiveDate <= currentDate &&
                           (!x.ExpirationDate.HasValue || x.ExpirationDate > currentDate));

            // Apply service type filter
            if (!string.IsNullOrEmpty(serviceType))
            {
                query = query.Where(x => x.ServiceTypes == null || 
                                        x.ServiceTypes.Contains($"\"{serviceType}\""));
            }

            // Apply origin zone filter
            if (!string.IsNullOrEmpty(originZone))
            {
                query = query.Where(x => x.OriginZones == null || 
                                        x.OriginZones.Contains($"\"{originZone}\""));
            }

            // Apply destination zone filter
            if (!string.IsNullOrEmpty(destinationZone))
            {
                query = query.Where(x => x.DestinationZones == null || 
                                        x.DestinationZones.Contains($"\"{destinationZone}\""));
            }

            // Apply customer segment filter
            if (!string.IsNullOrEmpty(customerSegment))
            {
                query = query.Where(x => x.CustomerSegments == null || 
                                        x.CustomerSegments.Contains($"\"{customerSegment}\""));
            }

            // Apply shipper type filter
            if (!string.IsNullOrEmpty(shipperType))
            {
                query = query.Where(x => x.ShipperTypes == null || 
                                        x.ShipperTypes.Contains($"\"{shipperType}\""));
            }

            // Apply weight filters
            if (weight.HasValue)
            {
                query = query.Where(x => (!x.MinWeight.HasValue || x.MinWeight <= weight) &&
                                        (!x.MaxWeight.HasValue || x.MaxWeight > weight));
            }

            // Apply distance filters
            if (distance.HasValue)
            {
                query = query.Where(x => (!x.MinDistance.HasValue || x.MinDistance <= distance) &&
                                        (!x.MaxDistance.HasValue || x.MaxDistance > distance));
            }

            // Apply value filters
            if (value.HasValue)
            {
                query = query.Where(x => (!x.MinValue.HasValue || x.MinValue <= value) &&
                                        (!x.MaxValue.HasValue || x.MaxValue > value));
            }

            return await query
                .OrderBy(x => x.Priority)
                .ThenBy(x => x.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<PricingRule>> GetRulesByPriorityAsync(Guid organizationId, int minPriority, int maxPriority)
        {
            return await _context.PricingRules
                .Include(x => x.Tiers)
                .Where(x => x.OrganizationId == organizationId &&
                           x.Priority >= minPriority &&
                           x.Priority <= maxPriority)
                .OrderBy(x => x.Priority)
                .ThenBy(x => x.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<PricingRule>> GetExpiringRulesAsync(Guid organizationId, DateTime beforeDate)
        {
            return await _context.PricingRules
                .Include(x => x.Tiers)
                .Where(x => x.OrganizationId == organizationId &&
                           x.Status == PricingStatus.Active &&
                           x.ExpirationDate.HasValue &&
                           x.ExpirationDate <= beforeDate)
                .OrderBy(x => x.ExpirationDate)
                .ThenBy(x => x.Name)
                .ToListAsync();
        }

        public async Task<bool> ExistsAsync(Guid id, Guid organizationId)
        {
            return await _context.PricingRules
                .AnyAsync(x => x.Id == id && x.OrganizationId == organizationId);
        }

        public async Task<bool> NameExistsAsync(string name, Guid organizationId, Guid? excludeId = null)
        {
            var query = _context.PricingRules
                .Where(x => x.OrganizationId == organizationId && x.Name == name);

            if (excludeId.HasValue)
            {
                query = query.Where(x => x.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        public async Task AddAsync(PricingRule pricingRule)
        {
            await _context.PricingRules.AddAsync(pricingRule);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(PricingRule pricingRule)
        {
            _context.PricingRules.Update(pricingRule);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(PricingRule pricingRule)
        {
            _context.PricingRules.Remove(pricingRule);
            await _context.SaveChangesAsync();
        }

        public async Task<int> CountByOrganizationAsync(Guid organizationId)
        {
            return await _context.PricingRules
                .CountAsync(x => x.OrganizationId == organizationId);
        }

        public async Task<int> CountActiveByOrganizationAsync(Guid organizationId)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.PricingRules
                .CountAsync(x => x.OrganizationId == organizationId &&
                                x.Status == PricingStatus.Active &&
                                x.EffectiveDate <= currentDate &&
                                (!x.ExpirationDate.HasValue || x.ExpirationDate > currentDate));
        }
    }
}
