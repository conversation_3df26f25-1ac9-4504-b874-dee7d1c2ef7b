using System;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using UserManagement.Application.Common.Interfaces;
using UserManagement.Domain.Entities;
using UserManagement.Domain.Repositories;
using UserManagement.Infrastructure.Persistence;
using UserManagement.Infrastructure.Persistence.Repositories;
using UserManagement.Infrastructure.Services;
using UserManagement.Infrastructure.Settings;

namespace UserManagement.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Database
            services.AddDbContext<UserManagementDbContext>(options =>
                options.UseNpgsql(
                    configuration.GetConnectionString("DefaultConnection"),
                    b => b.MigrationsAssembly(typeof(UserManagementDbContext).Assembly.FullName)));

            // Repositories
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IRoleRepository, RoleRepository>();
            services.AddScoped<IPermissionRepository, PermissionRepository>();
            services.AddScoped<IOrganizationRepository, OrganizationRepository>();
            services.AddScoped<IBranchRepository, BranchRepository>();
            services.AddScoped<ITeamRepository, TeamRepository>();
            services.AddScoped<ISubscriptionPlanRepository, SubscriptionPlanRepository>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // Services
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<IOrganizationService, OrganizationService>();
            services.AddScoped<IGdprService, GdprService>();
            services.AddScoped<IKycVerificationService, KycVerificationService>();
            services.AddScoped<ISmsService, SmsService>();
            services.AddScoped<ITokenService, TokenService>();
            services.AddScoped<ITwoFactorAuthService, TwoFactorAuthService>();
            services.AddSingleton<IMessageBroker, RabbitMqMessageBroker>();

            // Add ASP.NET Core Identity Password Hasher
            services.AddScoped<IPasswordHasher<User>, PasswordHasher<User>>();

            // Event Handlers
            services.AddScoped<UserManagement.Application.EventHandlers.UserRegisteredEventHandler>();
            services.AddHostedService<EventSubscriptionService>();

            // Identity Service
            services.AddHttpClient<IIdentityService, IdentityService>();

            // Redis Cache - Use in-memory cache for development
            var redisConnectionString = configuration.GetConnectionString("Redis");
            if (!string.IsNullOrEmpty(redisConnectionString))
            {
                services.AddStackExchangeRedisCache(options =>
                {
                    options.Configuration = redisConnectionString;
                    options.InstanceName = "UserManagement:";
                });
            }
            else
            {
                services.AddDistributedMemoryCache(); // Fallback to in-memory cache
            }
            services.AddScoped<ICacheService, RedisCacheService>();

            // Settings
            services.Configure<JwtSettings>(configuration.GetSection("JwtSettings"));
            services.Configure<KycSettings>(configuration.GetSection("KycSettings"));

            // JWT Authentication
            var jwtSettings = configuration.GetSection("JwtSettings").Get<JwtSettings>();
            var key = Encoding.ASCII.GetBytes(jwtSettings.Secret);

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = jwtSettings.Issuer,
                    ValidateAudience = true,
                    ValidAudience = jwtSettings.Audience,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };
            });

            // HTTP Clients
            services.AddHttpClient<KycVerificationService>();

            return services;
        }
    }
}
